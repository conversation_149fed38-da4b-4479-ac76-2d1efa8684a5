import React from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";

gsap.registerPlugin(ScrollTrigger, useGSAP);

const Home = () => {
  useGSAP(() => {
    gsap.fromTo(
      ".video-container",                      // 👈 targeting by className
      { height: "3vh" },
      {
        height: "30vh",
        ease: "power2.out",
        scrollTrigger: {
          trigger: ".video-container",         // 👈 trigger also by className
          start: "top 80%",
          end: "bottom center",
          scrub: true,
          
        },
      }
    );
  });

  return (
    <div className="min-h-screen bg-gray-900 font-[font1] font-bold leading-tight flex flex-col justify-center">
      {/* Quote Section */}
      <div className="text1 lg:text-5xl text-center scale-y-110 text-white p-4">
        <h1>
          We only <span className="text-green-500">teach</span> what we are
          really <br />
          really <span className="italic">good</span> at.
        </h1>
      </div>

      {/* Video Section (animated) */}
      <div className="flex justify-center items-center my-6">
        <div
          className="video-container bg-red-700 w-[80vw] h-[3vh] overflow-hidden rounded-xl shadow-lg"
        >
          <video
            autoPlay
            loop
            muted
            playsInline
            className="w-full h-full object-cover"
          >
            <source src="/assets/video.mp4" type="video/mp4" />
          </video>
        </div>
      </div>

      {/* Second Quote */}
      <div className="text2 text1 lg:text-5xl text-center scale-y-110 text-white p-4">
        <h1>
          Guiding every student to <br /> brighter future paths.
        </h1>
      </div>

    </div>
  );
};

export default Home;
