import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { 
  Star, 
  Clock, 
  Users, 
  BookOpen, 
  Award, 
  Play, 
  Download,
  Share2,
  Heart,
  ArrowLeft,
  CheckCircle
} from 'lucide-react'
import { courses } from '../data/courses'

const CourseDetail = () => {
  const { id } = useParams()
  const course = courses.find(c => c.id === parseInt(id))

  if (!course) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Course Not Found</h2>
          <Link to="/courses" className="text-primary-600 hover:text-primary-700">
            Back to Courses
          </Link>
        </div>
      </div>
    )
  }

  const {
    title,
    fullDescription,
    instructor,
    instructorBio,
    price,
    originalPrice,
    banner,
    category,
    duration,
    lessons,
    level,
    rating,
    students,
    syllabus
  } = course

  const discount = originalPrice ? Math.round(((originalPrice - price) / originalPrice) * 100) : 0

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-gray-500 hover:text-primary-600">Home</Link>
            <span className="text-gray-400">/</span>
            <Link to="/courses" className="text-gray-500 hover:text-primary-600">Courses</Link>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900">{title}</span>
          </div>
        </div>
      </div>

      {/* Course Header */}
      <div className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Course Info */}
            <div className="lg:col-span-2 space-y-6">
              <div className="flex items-center space-x-2">
                <Link 
                  to="/courses"
                  className="text-gray-300 hover:text-white flex items-center space-x-1"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Back to Courses</span>
                </Link>
              </div>
              
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <span className="bg-primary-600 text-white px-2 py-1 rounded text-sm font-medium">
                    {category}
                  </span>
                  <span className="text-gray-300 text-sm">{level}</span>
                </div>
                <h1 className="text-3xl md:text-4xl font-bold mb-4">{title}</h1>
                <p className="text-xl text-gray-300 mb-6">{fullDescription}</p>
              </div>

              {/* Course Stats */}
              <div className="flex flex-wrap items-center gap-6 text-sm">
                <div className="flex items-center space-x-1">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(rating)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-400'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="font-medium">{rating}</span>
                  <span className="text-gray-300">({students.toLocaleString()} students)</span>
                </div>
                <div className="flex items-center space-x-1 text-gray-300">
                  <Clock className="h-4 w-4" />
                  <span>{duration}</span>
                </div>
                <div className="flex items-center space-x-1 text-gray-300">
                  <BookOpen className="h-4 w-4" />
                  <span>{lessons} lessons</span>
                </div>
                <div className="flex items-center space-x-1 text-gray-300">
                  <Award className="h-4 w-4" />
                  <span>Certificate included</span>
                </div>
              </div>

              {/* Instructor */}
              <div className="flex items-center space-x-4">
                <img
                  src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face"
                  alt={instructor}
                  className="w-12 h-12 rounded-full"
                />
                <div>
                  <p className="font-medium">Created by {instructor}</p>
                  <p className="text-sm text-gray-300">{instructorBio}</p>
                </div>
              </div>
            </div>

            {/* Course Preview Card */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-lg overflow-hidden sticky top-4">
                <div className="relative">
                  <img
                    src={banner}
                    alt={title}
                    className="w-full h-48 object-cover"
                  />
                  <button className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 hover:bg-opacity-40 transition-opacity">
                    <Play className="h-16 w-16 text-white" />
                  </button>
                  {discount > 0 && (
                    <div className="absolute top-4 left-4 bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold">
                      {discount}% OFF
                    </div>
                  )}
                </div>
                
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <span className="text-3xl font-bold text-gray-900">${price}</span>
                      {originalPrice && (
                        <span className="text-lg text-gray-500 line-through">${originalPrice}</span>
                      )}
                    </div>
                  </div>
                  
                  <button className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-lg font-semibold text-lg mb-3 transition-colors">
                    Buy Now
                  </button>
                  
                  <button className="w-full border border-gray-300 hover:bg-gray-50 text-gray-700 py-3 rounded-lg font-semibold mb-4 transition-colors">
                    Add to Cart
                  </button>
                  
                  <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
                    <button className="flex items-center space-x-1 hover:text-primary-600">
                      <Heart className="h-4 w-4" />
                      <span>Wishlist</span>
                    </button>
                    <button className="flex items-center space-x-1 hover:text-primary-600">
                      <Share2 className="h-4 w-4" />
                      <span>Share</span>
                    </button>
                  </div>
                  
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <h4 className="font-semibold text-gray-900 mb-3">This course includes:</h4>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>{duration} on-demand video</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Downloadable resources</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Full lifetime access</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Certificate of completion</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Course Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Course Syllabus */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Course Curriculum</h2>
              <div className="space-y-3">
                {syllabus.map((item, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div className="flex-shrink-0 w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-semibold">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{item}</h3>
                    </div>
                    <Play className="h-4 w-4 text-gray-400" />
                  </div>
                ))}
              </div>
            </div>

            {/* Instructor Profile */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">About the Instructor</h2>
              <div className="flex items-start space-x-4">
                <img
                  src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face"
                  alt={instructor}
                  className="w-20 h-20 rounded-full"
                />
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{instructor}</h3>
                  <p className="text-gray-600 mb-4">{instructorBio}</p>
                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span>4.8 Instructor Rating</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>15,000+ Students</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <BookOpen className="h-4 w-4" />
                      <span>12 Courses</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Related Courses */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Courses</h3>
              <div className="space-y-4">
                {courses.filter(c => c.category === category && c.id !== course.id).slice(0, 3).map((relatedCourse) => (
                  <Link
                    key={relatedCourse.id}
                    to={`/course/${relatedCourse.id}`}
                    className="block group"
                  >
                    <div className="flex space-x-3">
                      <img
                        src={relatedCourse.thumbnail}
                        alt={relatedCourse.title}
                        className="w-16 h-12 object-cover rounded"
                      />
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 group-hover:text-primary-600 line-clamp-2">
                          {relatedCourse.title}
                        </h4>
                        <p className="text-sm text-gray-500">{relatedCourse.instructor}</p>
                        <p className="text-sm font-semibold text-gray-900">${relatedCourse.price}</p>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CourseDetail
