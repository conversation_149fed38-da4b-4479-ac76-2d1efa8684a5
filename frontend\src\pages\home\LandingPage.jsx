import React from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";

gsap.registerPlugin(ScrollTrigger, useGSAP);

const LandingPage = () => {
  useGSAP(() => {
    // Timeline for initial page load animations
    const tl = gsap.timeline();

    // Scroll-triggered video expansion animation
    gsap.fromTo(
      ".video-container",
      { height: "1vh" },
      {
        height: "70vh",
        width: "90vw",
        ease: "power2.inOut",
        scrollTrigger: {
          trigger: ".page1",
          start: "top 10% ",
          end: "bottom 10%",
          scrub: 1,
          pin: true,
          markers: true,
          onUpdate: (self) => {
            // Add rotation effect during scroll
            gsap.to(".video-container", {
              rotationY: self.progress * 5,
              duration: 0.1,
            });
          },
        },
      }
    );

    gsap.to(".text1", {
      opacity: 0,
      scaleX: 0.8,
      scaleY: 0.5,
      ease: "power2.inOut",
      scrollTrigger: {
        trigger: ".page1",
        start: "top 10% ",
        end: "bottom 10%",
        scrub: 1,
        markers: true,
      },
    });
  });

  return (
    <div className="min-h-screen page1 bg-gray-900 font-[font1] font-bold leading-tight flex flex-col justify-center">
      {/* Quote Section */}
      <div className="text1 lg:text-5xl text-center scale-y-110 text-white p-4">
        <h1>
          We only <span className="text-green-500">teach</span> what we are
          really <br />
          really <span className="italic">good</span> at.
        </h1>
      </div>

      {/* Video Section (animated) */}
      <div className="flex justify-center items-center ">
        <div className="video-container bg-red-700 w-[80vw] h-[5px] overflow-hidden ">
          <iframe
            width="100%"
            height="100%"
            src="https://www.youtube.com/embed/60SRAWmMXyc?si=nN8svAUDUdOibEx4&amp;controls=1"
            title="YouTube video player"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerpolicy="strict-origin-when-cross-origin"
            allowfullscreen
          ></iframe>
        </div>
      </div>

      {/* Second Quote */}
      <div className="text2 text1 lg:text-5xl text-center  text-white p-4  my-1.5 ">
        <h1>
          Guiding every student to <br /> brighter future paths.
        </h1>
      </div>
    </div>
  );
};

export default LandingPage;
