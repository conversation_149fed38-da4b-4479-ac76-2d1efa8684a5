import React, { useRef, useState } from "react";
import { Link } from "react-router-dom";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { ScrollToPlugin } from "gsap/ScrollToPlugin";
import { useGSAP } from "@gsap/react";
import { courses } from "../../data/courses";

gsap.registerPlugin(ScrollTrigger, ScrollToPlugin, useGSAP);

const CourseShowing = () => {
  const containerRef = useRef(null);
  const leftPanelRef = useRef(null);
  const rightPanelRef = useRef(null);
  const imageRef = useRef(null);
  const [currentCourseIndex, setCurrentCourseIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Use first 4 courses from data with enhanced display info
  const displayCourses = courses.slice(0, 4).map((course, index) => ({
    ...course,
    features: course.features?.slice(0, 4) || ["Live Classes", "Projects", "Mentorship", "Certification"],
    image: course.banner || course.thumbnail,
    color: [
      "from-blue-500 to-purple-600",
      "from-green-500 to-teal-600",
      "from-orange-500 to-red-600",
      "from-pink-500 to-rose-600"
    ][index] || "from-blue-500 to-purple-600"
  }));

  useGSAP(() => {
    const container = containerRef.current;
    const leftPanel = leftPanelRef.current;
    const rightPanel = rightPanelRef.current;
    const image = imageRef.current;

    if (!container || !leftPanel || !rightPanel || !image) return;

    let lastUpdateTime = 0;
    const throttleDelay = 16; // ~60fps

    // Set initial states
    gsap.set(".course-card", {
      y: "150%",
      opacity: 0,
      scale: 0.95
    });

    gsap.set(image, {
      scale: 1,
      opacity: 0
    });

    // Pin the entire section during scroll
    ScrollTrigger.create({
      trigger: container,
      start: "top top",
      end: () => `+=${displayCourses.length * window.innerHeight}`,
      pin: true,
      pinSpacing: true,
      anticipatePin: 1,
      onUpdate: (self) => {
        const now = Date.now();
        if (now - lastUpdateTime < throttleDelay) return;
        lastUpdateTime = now;

        // Calculate which course should be active based on scroll progress
        const progress = self.progress;
        const courseIndex = Math.floor(progress * displayCourses.length);
        const clampedIndex = Math.min(courseIndex, displayCourses.length - 1);

        // Update active course
        updateActiveCourse(clampedIndex);

        // Update progress indicators
        updateProgressIndicators(clampedIndex);
      }
    });

    // Animate course cards one by one
    displayCourses.forEach((_, index) => {
      const card = document.querySelector(`[data-course="${index}"]`);
      if (!card) return;

      ScrollTrigger.create({
        trigger: container,
        start: "top top",
        end: () => `+=${displayCourses.length * window.innerHeight}`,
        onUpdate: (self) => {
          const progress = self.progress;
          const courseProgress = (progress * displayCourses.length) - index;

          // Smooth interpolation for better transitions
          const normalizedProgress = Math.max(0, Math.min(1, courseProgress));
          const easeProgress = gsap.parseEase("power2.out")(normalizedProgress);

          if (courseProgress >= -0.2 && courseProgress <= 1.2) {
            // Calculate smooth position and opacity
            let yPos, opacity, scale;

            if (courseProgress < 0) {
              // Before active
              yPos = 100 + (courseProgress * 50);
              opacity = 0;
              scale = 0.9;
            } else if (courseProgress <= 1) {
              // Active transition
              yPos = (1 - easeProgress) * 30;
              opacity = easeProgress;
              scale = 0.95 + (easeProgress * 0.05);
            } else {
              // After active
              const exitProgress = courseProgress - 1;
              yPos = -(exitProgress * 80);
              opacity = Math.max(0, 1 - (exitProgress * 2));
              scale = 1 + (exitProgress * 0.1);
            }

            gsap.to(card, {
              y: `${yPos}%`,
              opacity: opacity,
              scale: scale,
              duration: 0.4,
              ease: "power2.out"
            });

            // Update active class
            if (courseProgress >= 0.3 && courseProgress <= 0.7) {
              card.classList.add('active');
            } else {
              card.classList.remove('active');
            }
          } else {
            // Far from active range
            const isAfter = courseProgress > 1.2;
            gsap.to(card, {
              y: isAfter ? "-100%" : "150%",
              opacity: 0,
              scale: isAfter ? 1.1 : 0.8,
              duration: 0.3,
              ease: "power2.out"
            });
            card.classList.remove('active');
          }
        }
      });
    });

    // Image change animation with preloading
    const updateActiveCourse = (index) => {
      if (currentCourseIndex === index || isTransitioning) return;

      const newImage = displayCourses[index]?.image;
      const newColor = displayCourses[index]?.color;

      if (newImage && image.src !== newImage) {
        setIsTransitioning(true);

        // Create a temporary image element for crossfade
        const tempImg = document.createElement('img');
        tempImg.style.position = 'absolute';
        tempImg.style.top = '0';
        tempImg.style.left = '0';
        tempImg.style.width = '100%';
        tempImg.style.height = '100%';
        tempImg.style.objectFit = 'cover';
        tempImg.style.opacity = '0';
        tempImg.style.zIndex = '1';

        tempImg.onload = () => {
          // Add temp image to container
          image.parentNode.appendChild(tempImg);

          // Crossfade animation
          gsap.to(tempImg, {
            opacity: 1,
            duration: 0.4,
            ease: "power2.inOut",
            onComplete: () => {
              // Replace main image
              image.src = newImage;
              image.style.opacity = '1';

              // Remove temp image
              tempImg.remove();

              setIsTransitioning(false);
              setCurrentCourseIndex(index);
            }
          });
        };

        tempImg.onerror = () => {
          tempImg.remove();
          setIsTransitioning(false);
        };

        tempImg.src = newImage;

        // Update background gradient smoothly
        if (newColor && rightPanel) {
          gsap.to(rightPanel, {
            duration: 0.5,
            ease: "power2.inOut",
            onUpdate: () => {
              rightPanel.className = rightPanel.className.replace(
                /from-\w+-\d+\s+to-\w+-\d+/g,
                newColor
              );
            }
          });
        }
      }
    };

    // Update progress indicators with smooth animation
    const updateProgressIndicators = (activeIndex) => {
      const indicators = document.querySelectorAll('[data-progress]');
      indicators.forEach((indicator, index) => {
        gsap.to(indicator, {
          scale: index === activeIndex ? 1.3 : 1,
          duration: 0.3,
          ease: "power2.out"
        });

        if (index === activeIndex) {
          indicator.classList.remove('bg-white/30');
          indicator.classList.add('bg-white');
        } else {
          indicator.classList.remove('bg-white');
          indicator.classList.add('bg-white/30');
        }
      });
    };

    // Initial setup
    if (displayCourses[0]?.image) {
      image.src = displayCourses[0].image;
      gsap.to(image, {
        opacity: 1,
        scale: 1,
        duration: 0.8,
        ease: "power2.out",
        delay: 0.3
      });
    }

    // Initialize first card as active
    const firstCard = document.querySelector(`[data-course="0"]`);
    if (firstCard) {
      gsap.set(firstCard, { y: "0%", opacity: 1, scale: 1 });
      firstCard.classList.add('active');
    }

    // Initialize progress indicators
    updateProgressIndicators(0);

  }, [displayCourses, isTransitioning]);

  return (
    <div
      ref={containerRef}
      className="w-full h-screen courseShowing bg-gray-900 relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-600/20"></div>
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 1px, transparent 1px)`,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      <div className="relative z-10 w-full h-full flex">
        {/* Left Panel - Course Descriptions */}
        <div
          ref={leftPanelRef}
          className="w-1/2 h-full flex flex-col justify-center items-center p-12 relative"
        >
          <div className="max-w-lg w-full space-y-8">
            {displayCourses.map((course, index) => (
              <div
                key={course.id}
                data-course={index}
                className="course-card absolute inset-0 flex flex-col justify-center"
              >
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 shadow-2xl">
                  <div className="mb-6">
                    <span className="text-sm font-medium text-blue-400 uppercase tracking-wider">
                      Course {index + 1}
                    </span>
                    <h2 className="text-3xl font-bold text-white mt-2 mb-4">
                      {course.title}
                    </h2>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      {course.description}
                    </p>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-white">What you'll learn:</h3>
                    <ul className="space-y-2">
                      {course.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-gray-300">
                          <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Link
                    to={`/course/${course.id}`}
                    className="mt-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300 inline-block text-center"
                  >
                    View Course
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Right Panel - Course Images */}
        <div
          ref={rightPanelRef}
          className="w-1/2 h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center relative overflow-hidden"
        >
          {/* Decorative Elements */}
          <div className="absolute top-10 right-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-20 left-10 w-24 h-24 bg-white/10 rounded-full blur-lg"></div>

          <div className="relative w-4/5 h-4/5 rounded-2xl overflow-hidden shadow-2xl">
            <img
              ref={imageRef}
              src={courses[0]?.image || null}
              alt="Course"
              className="w-full h-full object-cover"
            />

            {/* Image Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>

            {/* Floating Elements */}
            <div className="absolute top-6 left-6 bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
              <span className="text-white font-semibold">Premium Course</span>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {displayCourses.map((_, index) => (
          <div
            key={index}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === 0 ? 'bg-white' : 'bg-white/30'
            }`}
            data-progress={index}
          ></div>
        ))}
      </div>
    </div>
  );
};

export default CourseShowing;
