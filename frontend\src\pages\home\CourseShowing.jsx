import React, { useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { ScrollToPlugin } from "gsap/ScrollToPlugin";
import { useGSAP } from "@gsap/react";

gsap.registerPlugin(ScrollTrigger, ScrollToPlugin, useGSAP);

const CourseShowing = () => {
  const containerRef = useRef(null);
  const leftPanelRef = useRef(null);
  const rightPanelRef = useRef(null);
  const imageRef = useRef(null);

  // Course data with descriptions and images
  const courses = [
    {
      id: 1,
      title: "React Development Mastery",
      description: "Master modern React development with hooks, context, and advanced patterns. Build scalable applications with best practices.",
      features: ["React Hooks", "State Management", "Component Architecture", "Performance Optimization"],
      image: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=600&h=400&fit=crop",
      color: "from-blue-500 to-purple-600"
    },
    {
      id: 2,
      title: "Node.js Backend Development",
      description: "Build robust backend applications with Node.js, Express, and MongoDB. Learn API development and database management.",
      features: ["Express.js", "MongoDB", "RESTful APIs", "Authentication"],
      image: "https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=600&h=400&fit=crop",
      color: "from-green-500 to-teal-600"
    },
    {
      id: 3,
      title: "Full Stack JavaScript",
      description: "Complete full-stack development course covering frontend, backend, and deployment. Build real-world applications.",
      features: ["MERN Stack", "Deployment", "DevOps", "Project Management"],
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop",
      color: "from-orange-500 to-red-600"
    },
    {
      id: 4,
      title: "UI/UX Design Fundamentals",
      description: "Learn design principles, user research, and create stunning interfaces. Master Figma and design systems.",
      features: ["Design Principles", "Figma", "User Research", "Prototyping"],
      image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600&h=400&fit=crop",
      color: "from-pink-500 to-rose-600"
    }
  ];

  useGSAP(() => {
    const container = containerRef.current;
    const leftPanel = leftPanelRef.current;
    const rightPanel = rightPanelRef.current;
    const image = imageRef.current;

    if (!container || !leftPanel || !rightPanel || !image) return;

    // Set initial states
    gsap.set(".course-card", {
      y: "200%",
      opacity: 0,
      scale: 0.8
    });

    gsap.set(image, {
      scale: 1.2,
      opacity: 0
    });

    // Pin the entire section during scroll
    ScrollTrigger.create({
      trigger: container,
      start: "top top",
      end: () => `+=${courses.length * window.innerHeight}`,
      pin: true,
      pinSpacing: true,
      anticipatePin: 1,
      onUpdate: (self) => {
        // Calculate which course should be active based on scroll progress
        const progress = self.progress;
        const courseIndex = Math.floor(progress * courses.length);
        const clampedIndex = Math.min(courseIndex, courses.length - 1);

        // Update active course
        updateActiveCourse(clampedIndex);

        // Update progress indicators
        updateProgressIndicators(clampedIndex);
      }
    });

    // Animate course cards one by one
    courses.forEach((_, index) => {
      const card = document.querySelector(`[data-course="${index}"]`);
      if (!card) return;

      ScrollTrigger.create({
        trigger: container,
        start: "top top",
        end: () => `+=${courses.length * window.innerHeight}`,
        onUpdate: (self) => {
          const progress = self.progress;
          const courseProgress = (progress * courses.length) - index;

          if (courseProgress >= 0 && courseProgress <= 1) {
            // Animate card in
            gsap.to(card, {
              y: "0%",
              opacity: 1,
              scale: 1,
              duration: 0.8,
              ease: "power3.out"
            });

            // Add active class
            card.classList.add('active');
          } else if (courseProgress > 1) {
            // Animate card out
            gsap.to(card, {
              y: "-100%",
              opacity: 0.3,
              scale: 0.9,
              duration: 0.6,
              ease: "power2.in"
            });

            card.classList.remove('active');
          } else {
            // Keep card hidden
            gsap.set(card, {
              y: "200%",
              opacity: 0,
              scale: 0.8
            });

            card.classList.remove('active');
          }
        }
      });
    });

    // Image change animation
    const updateActiveCourse = (index) => {
      const newImage = courses[index]?.image;
      const newColor = courses[index]?.color;

      if (newImage && image.src !== newImage) {
        // Fade out current image
        gsap.to(image, {
          opacity: 0,
          scale: 1.1,
          duration: 0.4,
          ease: "power2.out",
          onComplete: () => {
            // Change image source
            image.src = newImage;

            // Fade in new image
            gsap.to(image, {
              opacity: 1,
              scale: 1,
              duration: 0.6,
              ease: "power2.out"
            });
          }
        });

        // Update background gradient
        if (newColor && rightPanel) {
          rightPanel.className = rightPanel.className.replace(
            /from-\w+-\d+\s+to-\w+-\d+/g,
            newColor
          );
        }
      }
    };

    // Update progress indicators
    const updateProgressIndicators = (activeIndex) => {
      const indicators = document.querySelectorAll('[data-progress]');
      indicators.forEach((indicator, index) => {
        if (index === activeIndex) {
          indicator.className = indicator.className.replace('bg-white/30', 'bg-white');
        } else {
          indicator.className = indicator.className.replace('bg-white', 'bg-white/30');
        }
      });
    };

    // Initial image load
    if (courses[0]?.image) {
      image.src = courses[0].image;
      gsap.to(image, {
        opacity: 1,
        scale: 1,
        duration: 1,
        ease: "power2.out",
        delay: 0.5
      });
    }

  }, []);

  return (
    <div
      ref={containerRef}
      className="w-full h-screen courseShowing bg-gray-900 relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-600/20"></div>
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 1px, transparent 1px)`,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      <div className="relative z-10 w-full h-full flex">
        {/* Left Panel - Course Descriptions */}
        <div
          ref={leftPanelRef}
          className="w-1/2 h-full flex flex-col justify-center items-center p-12 relative"
        >
          <div className="max-w-lg w-full space-y-8">
            {courses.map((course, index) => (
              <div
                key={course.id}
                data-course={index}
                className="course-card absolute inset-0 flex flex-col justify-center"
              >
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 shadow-2xl">
                  <div className="mb-6">
                    <span className="text-sm font-medium text-blue-400 uppercase tracking-wider">
                      Course {index + 1}
                    </span>
                    <h2 className="text-3xl font-bold text-white mt-2 mb-4">
                      {course.title}
                    </h2>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      {course.description}
                    </p>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-white">What you'll learn:</h3>
                    <ul className="space-y-2">
                      {course.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-gray-300">
                          <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <button className="mt-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    Enroll Now
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Right Panel - Course Images */}
        <div
          ref={rightPanelRef}
          className="w-1/2 h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center relative overflow-hidden"
        >
          {/* Decorative Elements */}
          <div className="absolute top-10 right-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-20 left-10 w-24 h-24 bg-white/10 rounded-full blur-lg"></div>

          <div className="relative w-4/5 h-4/5 rounded-2xl overflow-hidden shadow-2xl">
            <img
              ref={imageRef}
              src={courses[0]?.image || null}
              alt="Course"
              className="w-full h-full object-cover"
            />

            {/* Image Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>

            {/* Floating Elements */}
            <div className="absolute top-6 left-6 bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
              <span className="text-white font-semibold">Premium Course</span>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {courses.map((_, index) => (
          <div
            key={index}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === 0 ? 'bg-white' : 'bg-white/30'
            }`}
            data-progress={index}
          ></div>
        ))}
      </div>
    </div>
  );
};

export default CourseShowing;
