{"version": 3, "file": "MotionPathHelper.min.js", "sources": ["../src/utils/paths.js", "../src/utils/matrix.js", "../src/utils/PathEditor.js", "../src/MotionPathHelper.js"], "sourcesContent": ["/*!\n * paths 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n\t_numbersExp = /(?:(-)?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n\t_scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\n\t_selectorExp = /(^[#\\.][a-z]|[a-y][a-z])/i,\n\t_DEG2RAD = Math.PI / 180,\n\t_RAD2DEG = 180 / Math.PI,\n\t_sin = Math.sin,\n\t_cos = Math.cos,\n\t_abs = Math.abs,\n\t_sqrt = Math.sqrt,\n\t_atan2 = Math.atan2,\n\t_largeNum = 1e8,\n\t_isString = value => typeof(value) === \"string\",\n\t_isNumber = value => typeof(value) === \"number\",\n\t_isUndefined = value => typeof(value) === \"undefined\",\n\t_temp = {},\n\t_temp2 = {},\n\t_roundingNum = 1e5,\n\t_wrapProgress = progress => (Math.round((progress + _largeNum) % 1 * _roundingNum) / _roundingNum) || ((progress < 0) ? 0 : 1), //if progress lands on 1, the % will make it 0 which is why we || 1, but not if it's negative because it makes more sense for motion to end at 0 in that case.\n\t_round = value => (Math.round(value * _roundingNum) / _roundingNum) || 0,\n\t_roundPrecise = value => (Math.round(value * 1e10) / 1e10) || 0,\n\t_splitSegment = (rawPath, segIndex, i, t) => {\n\t\tlet segment = rawPath[segIndex],\n\t\t\tshift = t === 1 ? 6 : subdivideSegment(segment, i, t);\n\t\tif ((shift || !t) && shift + i + 2 < segment.length) {\n\t\t\trawPath.splice(segIndex, 0, segment.slice(0, i + shift + 2));\n\t\t\tsegment.splice(0, i + shift);\n\t\t\treturn 1;\n\t\t}\n\t},\n\t_getSampleIndex = (samples, length, progress) => {\n\t\t// slightly slower way than doing this (when there's no lookup): segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0;\n\t\tlet l = samples.length,\n\t\t\ti = ~~(progress * l);\n\t\tif (samples[i] > length) {\n\t\t\twhile (--i && samples[i] > length) {}\n\t\t\ti < 0 && (i = 0);\n\t\t} else {\n\t\t\twhile (samples[++i] < length && i < l) {}\n\t\t}\n\t\treturn i < l ? i : l - 1;\n\t},\n\t_reverseRawPath = (rawPath, skipOuter) => {\n\t\tlet i = rawPath.length;\n\t\tskipOuter || rawPath.reverse();\n\t\twhile (i--) {\n\t\t\trawPath[i].reversed || reverseSegment(rawPath[i]);\n\t\t}\n\t},\n\t_copyMetaData = (source, copy) => {\n\t\tcopy.totalLength = source.totalLength;\n\t\tif (source.samples) { //segment\n\t\t\tcopy.samples = source.samples.slice(0);\n\t\t\tcopy.lookup = source.lookup.slice(0);\n\t\t\tcopy.minLength = source.minLength;\n\t\t\tcopy.resolution = source.resolution;\n\t\t} else if (source.totalPoints) { //rawPath\n\t\t\tcopy.totalPoints = source.totalPoints;\n\t\t}\n\t\treturn copy;\n\t},\n\t//pushes a new segment into a rawPath, but if its starting values match the ending values of the last segment, it'll merge it into that same segment (to reduce the number of segments)\n\t_appendOrMerge = (rawPath, segment) => {\n\t\tlet index = rawPath.length,\n\t\t\tprevSeg = rawPath[index - 1] || [],\n\t\t\tl = prevSeg.length;\n\t\tif (index && segment[0] === prevSeg[l-2] && segment[1] === prevSeg[l-1]) {\n\t\t\tsegment = prevSeg.concat(segment.slice(2));\n\t\t\tindex--;\n\t\t}\n\t\trawPath[index] = segment;\n\t},\n\t_bestDistance;\n\n/* TERMINOLOGY\n - RawPath - an array of arrays, one for each Segment. A single RawPath could have multiple \"M\" commands, defining Segments (paths aren't always connected).\n - Segment - an array containing a sequence of Cubic Bezier coordinates in alternating x, y, x, y format. Starting anchor, then control point 1, control point 2, and ending anchor, then the next control point 1, control point 2, anchor, etc. Uses less memory than an array with a bunch of {x, y} points.\n - Bezier - a single cubic Bezier with a starting anchor, two control points, and an ending anchor.\n - the variable \"t\" is typically the position along an individual Bezier path (time) and it's NOT linear, meaning it could accelerate/decelerate based on the control points whereas the \"p\" or \"progress\" value is linearly mapped to the whole path, so it shouldn't really accelerate/decelerate based on control points. So a progress of 0.2 would be almost exactly 20% along the path. \"t\" is ONLY in an individual Bezier piece.\n */\n\n//accepts basic selector text, a path instance, a RawPath instance, or a Segment and returns a RawPath (makes it easy to homogenize things). If an element or selector text is passed in, it'll also cache the value so that if it's queried again, it'll just take the path data from there instead of parsing it all over again (as long as the path data itself hasn't changed - it'll check).\nexport function getRawPath(value) {\n\tvalue = (_isString(value) && _selectorExp.test(value)) ? document.querySelector(value) || value : value;\n\tlet e = value.getAttribute ? value : 0,\n\t\trawPath;\n\tif (e && (value = value.getAttribute(\"d\"))) {\n\t\t//implements caching\n\t\tif (!e._gsPath) {\n\t\t\te._gsPath = {};\n\t\t}\n\t\trawPath = e._gsPath[value];\n\t\treturn (rawPath && !rawPath._dirty) ? rawPath : (e._gsPath[value] = stringToRawPath(value));\n\t}\n\treturn !value ? console.warn(\"Expecting a <path> element or an SVG path data string\") : _isString(value) ? stringToRawPath(value) : (_isNumber(value[0])) ? [value] : value;\n}\n\n//copies a RawPath WITHOUT the length meta data (for speed)\nexport function copyRawPath(rawPath) {\n\tlet a = [],\n\t\ti = 0;\n\tfor (; i < rawPath.length; i++) {\n\t\ta[i] = _copyMetaData(rawPath[i], rawPath[i].slice(0));\n\t}\n\treturn _copyMetaData(rawPath, a);\n}\n\nexport function reverseSegment(segment) {\n\tlet i = 0,\n\t\ty;\n\tsegment.reverse(); //this will invert the order y, x, y, x so we must flip it back.\n\tfor (; i < segment.length; i += 2) {\n\t\ty = segment[i];\n\t\tsegment[i] = segment[i+1];\n\t\tsegment[i+1] = y;\n\t}\n\tsegment.reversed = !segment.reversed;\n}\n\n\n\nlet _createPath = (e, ignore) => {\n\t\tlet path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\"),\n\t\t\tattr = [].slice.call(e.attributes),\n\t\t\ti = attr.length,\n\t\t\tname;\n\t\tignore = \",\" + ignore + \",\";\n\t\twhile (--i > -1) {\n\t\t\tname = attr[i].nodeName.toLowerCase(); //in Microsoft Edge, if you don't set the attribute with a lowercase name, it doesn't render correctly! Super weird.\n\t\t\tif (ignore.indexOf(\",\" + name + \",\") < 0) {\n\t\t\t\tpath.setAttributeNS(null, name, attr[i].nodeValue);\n\t\t\t}\n\t\t}\n\t\treturn path;\n\t},\n\t_typeAttrs = {\n\t\trect:\"rx,ry,x,y,width,height\",\n\t\tcircle:\"r,cx,cy\",\n\t\tellipse:\"rx,ry,cx,cy\",\n\t\tline:\"x1,x2,y1,y2\"\n\t},\n\t_attrToObj = (e, attrs) => {\n\t\tlet props = attrs ? attrs.split(\",\") : [],\n\t\t\tobj = {},\n\t\t\ti = props.length;\n\t\twhile (--i > -1) {\n\t\t\tobj[props[i]] = +e.getAttribute(props[i]) || 0;\n\t\t}\n\t\treturn obj;\n\t};\n\n//converts an SVG shape like <circle>, <rect>, <polygon>, <polyline>, <ellipse>, etc. to a <path>, swapping it in and copying the attributes to match.\nexport function convertToPath(element, swap) {\n\tlet type = element.tagName.toLowerCase(),\n\t\tcirc = 0.552284749831,\n\t\tdata, x, y, r, ry, path, rcirc, rycirc, points, w, h, x2, x3, x4, x5, x6, y2, y3, y4, y5, y6, attr;\n\tif (type === \"path\" || !element.getBBox) {\n\t\treturn element;\n\t}\n\tpath = _createPath(element, \"x,y,width,height,cx,cy,rx,ry,r,x1,x2,y1,y2,points\");\n\tattr = _attrToObj(element, _typeAttrs[type]);\n\tif (type === \"rect\") {\n\t\tr = attr.rx;\n\t\try = attr.ry || r;\n\t\tx = attr.x;\n\t\ty = attr.y;\n\t\tw = attr.width - r * 2;\n\t\th = attr.height - ry * 2;\n\t\tif (r || ry) { //if there are rounded corners, render cubic beziers\n\t\t\tx2 = x + r * (1 - circ);\n\t\t\tx3 = x + r;\n\t\t\tx4 = x3 + w;\n\t\t\tx5 = x4 + r * circ;\n\t\t\tx6 = x4 + r;\n\t\t\ty2 = y + ry * (1 - circ);\n\t\t\ty3 = y + ry;\n\t\t\ty4 = y3 + h;\n\t\t\ty5 = y4 + ry * circ;\n\t\t\ty6 = y4 + ry;\n\t\t\tdata = \"M\" + x6 + \",\" + y3 + \" V\" + y4 + \" C\" + [x6, y5, x5, y6, x4, y6, x4 - (x4 - x3) / 3, y6, x3 + (x4 - x3) / 3, y6, x3, y6, x2, y6, x, y5, x, y4, x, y4 - (y4 - y3) / 3, x, y3 + (y4 - y3) / 3, x, y3, x, y2, x2, y, x3, y, x3 + (x4 - x3) / 3, y, x4 - (x4 - x3) / 3, y, x4, y, x5, y, x6, y2, x6, y3].join(\",\") + \"z\";\n\t\t} else {\n\t\t\tdata = \"M\" + (x + w) + \",\" + y + \" v\" + h + \" h\" + (-w) + \" v\" + (-h) + \" h\" + w + \"z\";\n\t\t}\n\n\t} else if (type === \"circle\" || type === \"ellipse\") {\n\t\tif (type === \"circle\") {\n\t\t\tr = ry = attr.r;\n\t\t\trycirc = r * circ;\n\t\t} else {\n\t\t\tr = attr.rx;\n\t\t\try = attr.ry;\n\t\t\trycirc = ry * circ;\n\t\t}\n\t\tx = attr.cx;\n\t\ty = attr.cy;\n\t\trcirc = r * circ;\n\t\tdata = \"M\" + (x+r) + \",\" + y + \" C\" + [x+r, y + rycirc, x + rcirc, y + ry, x, y + ry, x - rcirc, y + ry, x - r, y + rycirc, x - r, y, x - r, y - rycirc, x - rcirc, y - ry, x, y - ry, x + rcirc, y - ry, x + r, y - rycirc, x + r, y].join(\",\") + \"z\";\n\t} else if (type === \"line\") {\n\t\tdata = \"M\" + attr.x1 + \",\" + attr.y1 + \" L\" + attr.x2 + \",\" + attr.y2; //previously, we just converted to \"Mx,y Lx,y\" but Safari has bugs that cause that not to render properly when using a stroke-dasharray that's not fully visible! Using a cubic bezier fixes that issue.\n\t} else if (type === \"polyline\" || type === \"polygon\") {\n\t\tpoints = (element.getAttribute(\"points\") + \"\").match(_numbersExp) || [];\n\t\tx = points.shift();\n\t\ty = points.shift();\n\t\tdata = \"M\" + x + \",\" + y + \" L\" + points.join(\",\");\n\t\tif (type === \"polygon\") {\n\t\t\tdata += \",\" + x + \",\" + y + \"z\";\n\t\t}\n\t}\n\tpath.setAttribute(\"d\", rawPathToString(path._gsRawPath = stringToRawPath(data)));\n\tif (swap && element.parentNode) {\n\t\telement.parentNode.insertBefore(path, element);\n\t\telement.parentNode.removeChild(element);\n\t}\n\treturn path;\n}\n\n\n\n//returns the rotation (in degrees) at a particular progress on a rawPath (the slope of the tangent)\nexport function getRotationAtProgress(rawPath, progress) {\n\tlet d = getProgressData(rawPath, progress >= 1 ? 1 - 1e-9 : progress ? progress : 1e-9);\n\treturn getRotationAtBezierT(d.segment, d.i, d.t);\n}\n\nfunction getRotationAtBezierT(segment, i, t) {\n\tlet a = segment[i],\n\t\tb = segment[i+2],\n\t\tc = segment[i+4],\n\t\tx;\n\ta += (b - a) * t;\n\tb += (c - b) * t;\n\ta += (b - a) * t;\n\tx = b + ((c + (segment[i+6] - c) * t) - b) * t - a;\n\ta = segment[i+1];\n\tb = segment[i+3];\n\tc = segment[i+5];\n\ta += (b - a) * t;\n\tb += (c - b) * t;\n\ta += (b - a) * t;\n\treturn _round(_atan2(b + ((c + (segment[i+7] - c) * t) - b) * t - a, x) * _RAD2DEG);\n}\n\nexport function sliceRawPath(rawPath, start, end) {\n\tend = _isUndefined(end) ? 1 : _roundPrecise(end) || 0; // we must round to avoid issues like 4.15 / 8 = 0.8300000000000001 instead of 0.83 or 2.8 / 5 = 0.5599999999999999 instead of 0.56 and if someone is doing a loop like start: 2.8 / 0.5, end: 2.8 / 0.5 + 1.\n\tstart = _roundPrecise(start) || 0;\n\tlet loops = Math.max(0, ~~(_abs(end - start) - 1e-8)),\n\t\tpath = copyRawPath(rawPath);\n\tif (start > end) {\n\t\tstart = 1 - start;\n\t\tend = 1 - end;\n\t\t_reverseRawPath(path);\n\t\tpath.totalLength = 0;\n\t}\n\tif (start < 0 || end < 0) {\n\t\tlet offset = Math.abs(~~Math.min(start, end)) + 1;\n\t\tstart += offset;\n\t\tend += offset;\n\t}\n\tpath.totalLength || cacheRawPathMeasurements(path);\n\tlet wrap = (end > 1),\n\t\ts = getProgressData(path, start, _temp, true),\n\t\te = getProgressData(path, end, _temp2),\n\t\teSeg = e.segment,\n\t\tsSeg = s.segment,\n\t\teSegIndex = e.segIndex,\n\t\tsSegIndex = s.segIndex,\n\t\tei = e.i,\n\t\tsi = s.i,\n\t\tsameSegment = (sSegIndex === eSegIndex),\n\t\tsameBezier = (ei === si && sameSegment),\n\t\twrapsBehind, sShift, eShift, i, copy, totalSegments, l, j;\n\tif (wrap || loops) {\n\t\twrapsBehind = eSegIndex < sSegIndex || (sameSegment && ei < si) || (sameBezier && e.t < s.t);\n\t\tif (_splitSegment(path, sSegIndex, si, s.t)) {\n\t\t\tsSegIndex++;\n\t\t\tif (!wrapsBehind) {\n\t\t\t\teSegIndex++;\n\t\t\t\tif (sameBezier) {\n\t\t\t\t\te.t = (e.t - s.t) / (1 - s.t);\n\t\t\t\t\tei = 0;\n\t\t\t\t} else if (sameSegment) {\n\t\t\t\t\tei -= si;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (Math.abs(1 - (end - start)) < 1e-5) {\n\t\t\teSegIndex = sSegIndex - 1;\n\t\t} else if (!e.t && eSegIndex) {\n\t\t\teSegIndex--;\n\t\t} else if (_splitSegment(path, eSegIndex, ei, e.t) && wrapsBehind) {\n\t\t\tsSegIndex++;\n\t\t}\n\t\tif (s.t === 1) {\n\t\t\tsSegIndex = (sSegIndex + 1) % path.length;\n\t\t}\n\t\tcopy = [];\n\t\ttotalSegments = path.length;\n\t\tl = 1 + totalSegments * loops;\n\t\tj = sSegIndex;\n\t\tl += ((totalSegments - sSegIndex) + eSegIndex) % totalSegments;\n\t\tfor (i = 0; i < l; i++) {\n\t\t\t_appendOrMerge(copy, path[j++ % totalSegments]);\n\t\t}\n\t\tpath = copy;\n\t} else {\n\t\teShift = e.t === 1 ? 6 : subdivideSegment(eSeg, ei, e.t);\n\t\tif (start !== end) {\n\t\t\tsShift = subdivideSegment(sSeg, si, sameBezier ? s.t / e.t : s.t);\n\t\t\tsameSegment && (eShift += sShift);\n\t\t\teSeg.splice(ei + eShift + 2);\n\t\t\t(sShift || si) && sSeg.splice(0, si + sShift);\n\t\t\ti = path.length;\n\t\t\twhile (i--) {\n\t\t\t\t//chop off any extra segments\n\t\t\t\t(i < sSegIndex || i > eSegIndex) &&\tpath.splice(i, 1);\n\t\t\t}\n\t\t} else {\n\t\t\teSeg.angle = getRotationAtBezierT(eSeg, ei + eShift, 0); //record the value before we chop because it'll be impossible to determine the angle after its length is 0!\n\t\t\tei += eShift;\n\t\t\ts = eSeg[ei];\n\t\t\te = eSeg[ei+1];\n\t\t\teSeg.length = eSeg.totalLength = 0;\n\t\t\teSeg.totalPoints = path.totalPoints = 8;\n\t\t\teSeg.push(s, e, s, e, s, e, s, e);\n\t\t}\n\t}\n\tpath.totalLength = 0;\n\treturn path;\n}\n\n//measures a Segment according to its resolution (so if segment.resolution is 6, for example, it'll take 6 samples equally across each Bezier) and create/populate a \"samples\" Array that has the length up to each of those sample points (always increasing from the start) as well as a \"lookup\" array that's broken up according to the smallest distance between 2 samples. This gives us a very fast way of looking up a progress position rather than looping through all the points/Beziers. You can optionally have it only measure a subset, starting at startIndex and going for a specific number of beziers (remember, there are 3 x/y pairs each, for a total of 6 elements for each Bezier). It will also populate a \"totalLength\" property, but that's not generally super accurate because by default it'll only take 6 samples per Bezier. But for performance reasons, it's perfectly adequate for measuring progress values along the path. If you need a more accurate totalLength, either increase the resolution or use the more advanced bezierToPoints() method which keeps adding points until they don't deviate by more than a certain precision value.\nfunction measureSegment(segment, startIndex, bezierQty) {\n\tstartIndex = startIndex || 0;\n\tif (!segment.samples) {\n\t\tsegment.samples = [];\n\t\tsegment.lookup = [];\n\t}\n\tlet resolution = ~~segment.resolution || 12,\n\t\tinc = 1 / resolution,\n\t\tendIndex = bezierQty ? startIndex + bezierQty * 6 + 1 : segment.length,\n\t\tx1 = segment[startIndex],\n\t\ty1 = segment[startIndex + 1],\n\t\tsamplesIndex = startIndex ? (startIndex / 6) * resolution : 0,\n\t\tsamples = segment.samples,\n\t\tlookup = segment.lookup,\n\t\tmin = (startIndex ? segment.minLength : _largeNum) || _largeNum,\n\t\tprevLength = samples[samplesIndex + bezierQty * resolution - 1],\n\t\tlength = startIndex ? samples[samplesIndex-1] : 0,\n\t\ti, j, x4, x3, x2, xd, xd1, y4, y3, y2, yd, yd1, inv, t, lengthIndex, l, segLength;\n\tsamples.length = lookup.length = 0;\n\tfor (j = startIndex + 2; j < endIndex; j += 6) {\n\t\tx4 = segment[j + 4] - x1;\n\t\tx3 = segment[j + 2] - x1;\n\t\tx2 = segment[j] - x1;\n\t\ty4 = segment[j + 5] - y1;\n\t\ty3 = segment[j + 3] - y1;\n\t\ty2 = segment[j + 1] - y1;\n\t\txd = xd1 = yd = yd1 = 0;\n\t\tif (_abs(x4) < .01 && _abs(y4) < .01 && _abs(x2) + _abs(y2) < .01) { //dump points that are sufficiently close (basically right on top of each other, making a bezier super tiny or 0 length)\n\t\t\tif (segment.length > 8) {\n\t\t\t\tsegment.splice(j, 6);\n\t\t\t\tj -= 6;\n\t\t\t\tendIndex -= 6;\n\t\t\t}\n\t\t} else {\n\t\t\tfor (i = 1; i <= resolution; i++) {\n\t\t\t\tt = inc * i;\n\t\t\t\tinv = 1 - t;\n\t\t\t\txd = xd1 - (xd1 = (t * t * x4 + 3 * inv * (t * x3 + inv * x2)) * t);\n\t\t\t\tyd = yd1 - (yd1 = (t * t * y4 + 3 * inv * (t * y3 + inv * y2)) * t);\n\t\t\t\tl = _sqrt(yd * yd + xd * xd);\n\t\t\t\tif (l < min) {\n\t\t\t\t\tmin = l;\n\t\t\t\t}\n\t\t\t\tlength += l;\n\t\t\t\tsamples[samplesIndex++] = length;\n\t\t\t}\n\t\t}\n\t\tx1 += x4;\n\t\ty1 += y4;\n\t}\n\tif (prevLength) {\n\t\tprevLength -= length;\n\t\tfor (; samplesIndex < samples.length; samplesIndex++) {\n\t\t\tsamples[samplesIndex] += prevLength;\n\t\t}\n\t}\n\tif (samples.length && min) {\n\t\tsegment.totalLength = segLength = samples[samples.length-1] || 0;\n\t\tsegment.minLength = min;\n\t\tif (segLength / min < 9999) { // if the lookup would require too many values (memory problem), we skip this and instead we use a loop to lookup values directly in the samples Array\n\t\t\tl = lengthIndex = 0;\n\t\t\tfor (i = 0; i < segLength; i += min) {\n\t\t\t\tlookup[l++] = (samples[lengthIndex] < i) ? ++lengthIndex : lengthIndex;\n\t\t\t}\n\t\t}\n\t} else {\n\t\tsegment.totalLength = samples[0] = 0;\n\t}\n\treturn startIndex ? length - samples[startIndex / 2 - 1] : length;\n}\n\nexport function cacheRawPathMeasurements(rawPath, resolution) {\n\tlet pathLength, points, i;\n\tfor (i = pathLength = points = 0; i < rawPath.length; i++) {\n\t\trawPath[i].resolution = ~~resolution || 12; //steps per Bezier curve (anchor, 2 control points, to anchor)\n\t\tpoints += rawPath[i].length;\n\t\tpathLength += measureSegment(rawPath[i]);\n\t}\n\trawPath.totalPoints = points;\n\trawPath.totalLength = pathLength;\n\treturn rawPath;\n}\n\n//divide segment[i] at position t (value between 0 and 1, progress along that particular cubic bezier segment that starts at segment[i]). Returns how many elements were spliced into the segment array (either 0 or 6)\nexport function subdivideSegment(segment, i, t) {\n\tif (t <= 0 || t >= 1) {\n\t\treturn 0;\n\t}\n\tlet ax = segment[i],\n\t\tay = segment[i+1],\n\t\tcp1x = segment[i+2],\n\t\tcp1y = segment[i+3],\n\t\tcp2x = segment[i+4],\n\t\tcp2y = segment[i+5],\n\t\tbx = segment[i+6],\n\t\tby = segment[i+7],\n\t\tx1a = ax + (cp1x - ax) * t,\n\t\tx2 = cp1x + (cp2x - cp1x) * t,\n\t\ty1a = ay + (cp1y - ay) * t,\n\t\ty2 = cp1y + (cp2y - cp1y) * t,\n\t\tx1 = x1a + (x2 - x1a) * t,\n\t\ty1 = y1a + (y2 - y1a) * t,\n\t\tx2a = cp2x + (bx - cp2x) * t,\n\t\ty2a = cp2y + (by - cp2y) * t;\n\tx2 += (x2a - x2) * t;\n\ty2 += (y2a - y2) * t;\n\tsegment.splice(i + 2, 4,\n\t\t_round(x1a),                  //first control point\n\t\t_round(y1a),\n\t\t_round(x1),                   //second control point\n\t\t_round(y1),\n\t\t_round(x1 + (x2 - x1) * t),   //new fabricated anchor on line\n\t\t_round(y1 + (y2 - y1) * t),\n\t\t_round(x2),                   //third control point\n\t\t_round(y2),\n\t\t_round(x2a),                  //fourth control point\n\t\t_round(y2a)\n\t);\n\tsegment.samples && segment.samples.splice(((i / 6) * segment.resolution) | 0, 0, 0, 0, 0, 0, 0, 0);\n\treturn 6;\n}\n\n// returns an object {path, segment, segIndex, i, t}\nfunction getProgressData(rawPath, progress, decoratee, pushToNextIfAtEnd) {\n\tdecoratee = decoratee || {};\n\trawPath.totalLength || cacheRawPathMeasurements(rawPath);\n\tif (progress < 0 || progress > 1) {\n\t\tprogress = _wrapProgress(progress);\n\t}\n\tlet segIndex = 0,\n\t\tsegment = rawPath[0],\n\t\tsamples, resolution, length, min, max, i, t;\n\tif (!progress) {\n\t\tt = i = segIndex = 0;\n\t\tsegment = rawPath[0];\n\t} else if (progress === 1) {\n\t\tt = 1;\n\t\tsegIndex = rawPath.length - 1;\n\t\tsegment = rawPath[segIndex];\n\t\ti = segment.length - 8;\n\t} else {\n\t\tif (rawPath.length > 1) { //speed optimization: most of the time, there's only one segment so skip the recursion.\n\t\t\tlength = rawPath.totalLength * progress;\n\t\t\tmax = i = 0;\n\t\t\twhile ((max += rawPath[i++].totalLength) < length) {\n\t\t\t\tsegIndex = i;\n\t\t\t}\n\t\t\tsegment = rawPath[segIndex];\n\t\t\tmin = max - segment.totalLength;\n\t\t\tprogress = ((length - min) / (max - min)) || 0;\n\t\t}\n\t\tsamples = segment.samples;\n\t\tresolution = segment.resolution; //how many samples per cubic bezier chunk\n\t\tlength = segment.totalLength * progress;\n\t\ti = segment.lookup.length ? segment.lookup[~~(length / segment.minLength)] || 0 : _getSampleIndex(samples, length, progress);\n\t\tmin = i ? samples[i-1] : 0;\n\t\tmax = samples[i];\n\t\tif (max < length) {\n\t\t\tmin = max;\n\t\t\tmax = samples[++i];\n\t\t}\n\t\tt = (1 / resolution) * (((length - min) / (max - min)) + ((i % resolution)));\n\t\ti = ~~(i / resolution) * 6;\n\t\tif (pushToNextIfAtEnd && t === 1) {\n\t\t\tif (i + 6 < segment.length) {\n\t\t\t\ti += 6;\n\t\t\t\tt = 0;\n\t\t\t} else if (segIndex + 1 < rawPath.length) {\n\t\t\t\ti = t = 0;\n\t\t\t\tsegment = rawPath[++segIndex];\n\t\t\t}\n\t\t}\n\t}\n\tdecoratee.t = t;\n\tdecoratee.i = i;\n\tdecoratee.path = rawPath;\n\tdecoratee.segment = segment;\n\tdecoratee.segIndex = segIndex;\n\treturn decoratee;\n}\n\nexport function getPositionOnPath(rawPath, progress, includeAngle, point) {\n\tlet segment = rawPath[0],\n\t\tresult = point || {},\n\t\tsamples, resolution, length, min, max, i, t, a, inv;\n\tif (progress < 0 || progress > 1) {\n\t\tprogress = _wrapProgress(progress);\n\t}\n\tsegment.lookup || cacheRawPathMeasurements(rawPath);\n\tif (rawPath.length > 1) { //speed optimization: most of the time, there's only one segment so skip the recursion.\n\t\tlength = rawPath.totalLength * progress;\n\t\tmax = i = 0;\n\t\twhile ((max += rawPath[i++].totalLength) < length) {\n\t\t\tsegment = rawPath[i];\n\t\t}\n\t\tmin = max - segment.totalLength;\n\t\tprogress = ((length - min) / (max - min)) || 0;\n\t}\n\tsamples = segment.samples;\n\tresolution = segment.resolution;\n\tlength = segment.totalLength * progress;\n\ti = segment.lookup.length ? segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0 : _getSampleIndex(samples, length, progress);\n\tmin = i ? samples[i-1] : 0;\n\tmax = samples[i];\n\tif (max < length) {\n\t\tmin = max;\n\t\tmax = samples[++i];\n\t}\n\tt = ((1 / resolution) * (((length - min) / (max - min)) + ((i % resolution)))) || 0;\n\tinv = 1 - t;\n\ti = ~~(i / resolution) * 6;\n\ta = segment[i];\n\tresult.x = _round((t * t * (segment[i + 6] - a) + 3 * inv * (t * (segment[i + 4] - a) + inv * (segment[i + 2] - a))) * t + a);\n\tresult.y = _round((t * t * (segment[i + 7] - (a = segment[i+1])) + 3 * inv * (t * (segment[i + 5] - a) + inv * (segment[i + 3] - a))) * t + a);\n\tif (includeAngle) {\n\t\tresult.angle = segment.totalLength ? getRotationAtBezierT(segment, i, t >= 1 ? 1 - 1e-9 : t ? t : 1e-9) : segment.angle || 0;\n\t}\n\treturn result;\n}\n\n\n\n//applies a matrix transform to RawPath (or a segment in a RawPath) and returns whatever was passed in (it transforms the values in the array(s), not a copy).\nexport function transformRawPath(rawPath, a, b, c, d, tx, ty) {\n\tlet j = rawPath.length,\n\t\tsegment, l, i, x, y;\n\twhile (--j > -1) {\n\t\tsegment = rawPath[j];\n\t\tl = segment.length;\n\t\tfor (i = 0; i < l; i += 2) {\n\t\t\tx = segment[i];\n\t\t\ty = segment[i+1];\n\t\t\tsegment[i] = x * a + y * c + tx;\n\t\t\tsegment[i+1] = x * b + y * d + ty;\n\t\t}\n\t}\n\trawPath._dirty = 1;\n\treturn rawPath;\n}\n\n\n\n// translates SVG arc data into a segment (cubic beziers). Angle is in degrees.\nfunction arcToSegment(lastX, lastY, rx, ry, angle, largeArcFlag, sweepFlag, x, y) {\n\tif (lastX === x && lastY === y) {\n\t\treturn;\n\t}\n\trx = _abs(rx);\n\try = _abs(ry);\n\tlet angleRad = (angle % 360) * _DEG2RAD,\n\t\tcosAngle = _cos(angleRad),\n\t\tsinAngle = _sin(angleRad),\n\t\tPI = Math.PI,\n\t\tTWOPI = PI * 2,\n\t\tdx2 = (lastX - x) / 2,\n\t\tdy2 = (lastY - y) / 2,\n\t\tx1 = (cosAngle * dx2 + sinAngle * dy2),\n\t\ty1 = (-sinAngle * dx2 + cosAngle * dy2),\n\t\tx1_sq = x1 * x1,\n\t\ty1_sq = y1 * y1,\n\t\tradiiCheck = x1_sq / (rx * rx) + y1_sq / (ry * ry);\n\tif (radiiCheck > 1) {\n\t\trx = _sqrt(radiiCheck) * rx;\n\t\try = _sqrt(radiiCheck) * ry;\n\t}\n\tlet rx_sq = rx * rx,\n\t\try_sq = ry * ry,\n\t\tsq = ((rx_sq * ry_sq) - (rx_sq * y1_sq) - (ry_sq * x1_sq)) / ((rx_sq * y1_sq) + (ry_sq * x1_sq));\n\tif (sq < 0) {\n\t\tsq = 0;\n\t}\n\tlet coef = ((largeArcFlag === sweepFlag) ? -1 : 1) * _sqrt(sq),\n\t\tcx1 = coef * ((rx * y1) / ry),\n\t\tcy1 = coef * -((ry * x1) / rx),\n\t\tsx2 = (lastX + x) / 2,\n\t\tsy2 = (lastY + y) / 2,\n\t\tcx = sx2 + (cosAngle * cx1 - sinAngle * cy1),\n\t\tcy = sy2 + (sinAngle * cx1 + cosAngle * cy1),\n\t\tux = (x1 - cx1) / rx,\n\t\tuy = (y1 - cy1) / ry,\n\t\tvx = (-x1 - cx1) / rx,\n\t\tvy = (-y1 - cy1) / ry,\n\t\ttemp = ux * ux + uy * uy,\n\t\tangleStart = ((uy < 0) ? -1 : 1) * Math.acos(ux / _sqrt(temp)),\n\t\tangleExtent = ((ux * vy - uy * vx < 0) ? -1 : 1) * Math.acos((ux * vx + uy * vy) / _sqrt(temp * (vx * vx + vy * vy)));\n\tisNaN(angleExtent) && (angleExtent = PI); //rare edge case. Math.cos(-1) is NaN.\n\tif (!sweepFlag && angleExtent > 0) {\n\t\tangleExtent -= TWOPI;\n\t} else if (sweepFlag && angleExtent < 0) {\n\t\tangleExtent += TWOPI;\n\t}\n\tangleStart %= TWOPI;\n\tangleExtent %= TWOPI;\n\tlet segments = Math.ceil(_abs(angleExtent) / (TWOPI / 4)),\n\t\trawPath = [],\n\t\tangleIncrement = angleExtent / segments,\n\t\tcontrolLength = 4 / 3 * _sin(angleIncrement / 2) / (1 + _cos(angleIncrement / 2)),\n\t\tma = cosAngle * rx,\n\t\tmb = sinAngle * rx,\n\t\tmc = sinAngle * -ry,\n\t\tmd = cosAngle * ry,\n\t\ti;\n\tfor (i = 0; i < segments; i++) {\n\t\tangle = angleStart + i * angleIncrement;\n\t\tx1 = _cos(angle);\n\t\ty1 = _sin(angle);\n\t\tux = _cos(angle += angleIncrement);\n\t\tuy = _sin(angle);\n\t\trawPath.push(x1 - controlLength * y1, y1 + controlLength * x1, ux + controlLength * uy, uy - controlLength * ux, ux, uy);\n\t}\n\t//now transform according to the actual size of the ellipse/arc (the beziers were noramlized, between 0 and 1 on a circle).\n\tfor (i = 0; i < rawPath.length; i+=2) {\n\t\tx1 = rawPath[i];\n\t\ty1 = rawPath[i+1];\n\t\trawPath[i] = x1 * ma + y1 * mc + cx;\n\t\trawPath[i+1] = x1 * mb + y1 * md + cy;\n\t}\n\trawPath[i-2] = x; //always set the end to exactly where it's supposed to be\n\trawPath[i-1] = y;\n\treturn rawPath;\n}\n\n//Spits back a RawPath with absolute coordinates. Each segment starts with a \"moveTo\" command (x coordinate, then y) and then 2 control points (x, y, x, y), then anchor. The goal is to minimize memory and maximize speed.\nexport function stringToRawPath(d) {\n\tlet a = (d + \"\").replace(_scientific, m => { let n = +m; return (n < 0.0001 && n > -0.0001) ? 0 : n; }).match(_svgPathExp) || [], //some authoring programs spit out very small numbers in scientific notation like \"1e-5\", so make sure we round that down to 0 first.\n\t\tpath = [],\n\t\trelativeX = 0,\n\t\trelativeY = 0,\n\t\ttwoThirds = 2 / 3,\n\t\telements = a.length,\n\t\tpoints = 0,\n\t\terrorMessage = \"ERROR: malformed path: \" + d,\n\t\ti, j, x, y, command, isRelative, segment, startX, startY, difX, difY, beziers, prevCommand, flag1, flag2,\n\t\tline = function(sx, sy, ex, ey) {\n\t\t\tdifX = (ex - sx) / 3;\n\t\t\tdifY = (ey - sy) / 3;\n\t\t\tsegment.push(sx + difX, sy + difY, ex - difX, ey - difY, ex, ey);\n\t\t};\n\tif (!d || !isNaN(a[0]) || isNaN(a[1])) {\n\t\tconsole.log(errorMessage);\n\t\treturn path;\n\t}\n\tfor (i = 0; i < elements; i++) {\n\t\tprevCommand = command;\n\t\tif (isNaN(a[i])) {\n\t\t\tcommand = a[i].toUpperCase();\n\t\t\tisRelative = (command !== a[i]); //lower case means relative\n\t\t} else { //commands like \"C\" can be strung together without any new command characters between.\n\t\t\ti--;\n\t\t}\n\t\tx = +a[i + 1];\n\t\ty = +a[i + 2];\n\t\tif (isRelative) {\n\t\t\tx += relativeX;\n\t\t\ty += relativeY;\n\t\t}\n\t\tif (!i) {\n\t\t\tstartX = x;\n\t\t\tstartY = y;\n\t\t}\n\n\t\t// \"M\" (move)\n\t\tif (command === \"M\") {\n\t\t\tif (segment) {\n\t\t\t\tif (segment.length < 8) { //if the path data was funky and just had a M with no actual drawing anywhere, skip it.\n\t\t\t\t\tpath.length -= 1;\n\t\t\t\t} else {\n\t\t\t\t\tpoints += segment.length;\n\t\t\t\t}\n\t\t\t}\n\t\t\trelativeX = startX = x;\n\t\t\trelativeY = startY = y;\n\t\t\tsegment = [x, y];\n\t\t\tpath.push(segment);\n\t\t\ti += 2;\n\t\t\tcommand = \"L\"; //an \"M\" with more than 2 values gets interpreted as \"lineTo\" commands (\"L\").\n\n\t\t// \"C\" (cubic bezier)\n\t\t} else if (command === \"C\") {\n\t\t\tif (!segment) {\n\t\t\t\tsegment = [0, 0];\n\t\t\t}\n\t\t\tif (!isRelative) {\n\t\t\t\trelativeX = relativeY = 0;\n\t\t\t}\n\t\t\t//note: \"*1\" is just a fast/short way to cast the value as a Number. WAAAY faster in Chrome, slightly slower in Firefox.\n\t\t\tsegment.push(x,\ty, relativeX + a[i + 3] * 1, relativeY + a[i + 4] * 1, (relativeX += a[i + 5] * 1),\t(relativeY += a[i + 6] * 1));\n\t\t\ti += 6;\n\n\t\t// \"S\" (continuation of cubic bezier)\n\t\t} else if (command === \"S\") {\n\t\t\tdifX = relativeX;\n\t\t\tdifY = relativeY;\n\t\t\tif (prevCommand === \"C\" || prevCommand === \"S\") {\n\t\t\t\tdifX += relativeX - segment[segment.length - 4];\n\t\t\t\tdifY += relativeY - segment[segment.length - 3];\n\t\t\t}\n\t\t\tif (!isRelative) {\n\t\t\t\trelativeX = relativeY = 0;\n\t\t\t}\n\t\t\tsegment.push(difX, difY, x,\ty, (relativeX += a[i + 3] * 1), (relativeY += a[i + 4] * 1));\n\t\t\ti += 4;\n\n\t\t// \"Q\" (quadratic bezier)\n\t\t} else if (command === \"Q\") {\n\t\t\tdifX = relativeX + (x - relativeX) * twoThirds;\n\t\t\tdifY = relativeY + (y - relativeY) * twoThirds;\n\t\t\tif (!isRelative) {\n\t\t\t\trelativeX = relativeY = 0;\n\t\t\t}\n\t\t\trelativeX += a[i + 3] * 1;\n\t\t\trelativeY += a[i + 4] * 1;\n\t\t\tsegment.push(difX, difY, relativeX + (x - relativeX) * twoThirds, relativeY + (y - relativeY) * twoThirds, relativeX, relativeY);\n\t\t\ti += 4;\n\n\t\t// \"T\" (continuation of quadratic bezier)\n\t\t} else if (command === \"T\") {\n\t\t\tdifX = relativeX - segment[segment.length - 4];\n\t\t\tdifY = relativeY - segment[segment.length - 3];\n\t\t\tsegment.push(relativeX + difX, relativeY + difY, x + ((relativeX + difX * 1.5) - x) * twoThirds, y + ((relativeY + difY * 1.5) - y) * twoThirds, (relativeX = x), (relativeY = y));\n\t\t\ti += 2;\n\n\t\t// \"H\" (horizontal line)\n\t\t} else if (command === \"H\") {\n\t\t\tline(relativeX, relativeY, (relativeX = x), relativeY);\n\t\t\ti += 1;\n\n\t\t// \"V\" (vertical line)\n\t\t} else if (command === \"V\") {\n\t\t\t//adjust values because the first (and only one) isn't x in this case, it's y.\n\t\t\tline(relativeX, relativeY, relativeX, (relativeY = x + (isRelative ? relativeY - relativeX : 0)));\n\t\t\ti += 1;\n\n\t\t// \"L\" (line) or \"Z\" (close)\n\t\t} else if (command === \"L\" || command === \"Z\") {\n\t\t\tif (command === \"Z\") {\n\t\t\t\tx = startX;\n\t\t\t\ty = startY;\n\t\t\t\tsegment.closed = true;\n\t\t\t}\n\t\t\tif (command === \"L\" || _abs(relativeX - x) > 0.5 || _abs(relativeY - y) > 0.5) {\n\t\t\t\tline(relativeX, relativeY, x, y);\n\t\t\t\tif (command === \"L\") {\n\t\t\t\t\ti += 2;\n\t\t\t\t}\n\t\t\t}\n\t\t\trelativeX = x;\n\t\t\trelativeY = y;\n\n\t\t// \"A\" (arc)\n\t\t} else if (command === \"A\") {\n\t\t\tflag1 = a[i+4];\n\t\t\tflag2 = a[i+5];\n\t\t\tdifX = a[i+6];\n\t\t\tdifY = a[i+7];\n\t\t\tj = 7;\n\t\t\tif (flag1.length > 1) { // for cases when the flags are merged, like \"a8 8 0 018 8\" (the 0 and 1 flags are WITH the x value of 8, but it could also be \"a8 8 0 01-8 8\" so it may include x or not)\n\t\t\t\tif (flag1.length < 3) {\n\t\t\t\t\tdifY = difX;\n\t\t\t\t\tdifX = flag2;\n\t\t\t\t\tj--;\n\t\t\t\t} else {\n\t\t\t\t\tdifY = flag2;\n\t\t\t\t\tdifX = flag1.substr(2);\n\t\t\t\t\tj-=2;\n\t\t\t\t}\n\t\t\t\tflag2 = flag1.charAt(1);\n\t\t\t\tflag1 = flag1.charAt(0);\n\t\t\t}\n\t\t\tbeziers = arcToSegment(relativeX, relativeY, +a[i+1], +a[i+2], +a[i+3], +flag1, +flag2, (isRelative ? relativeX : 0) + difX*1, (isRelative ? relativeY : 0) + difY*1);\n\t\t\ti += j;\n\t\t\tif (beziers) {\n\t\t\t\tfor (j = 0; j < beziers.length; j++) {\n\t\t\t\t\tsegment.push(beziers[j]);\n\t\t\t\t}\n\t\t\t}\n\t\t\trelativeX = segment[segment.length-2];\n\t\t\trelativeY = segment[segment.length-1];\n\n\t\t} else {\n\t\t\tconsole.log(errorMessage);\n\t\t}\n\t}\n\ti = segment.length;\n\tif (i < 6) { //in case there's odd SVG like a M0,0 command at the very end.\n\t\tpath.pop();\n\t\ti = 0;\n\t} else if (segment[0] === segment[i-2] && segment[1] === segment[i-1]) {\n\t\tsegment.closed = true;\n\t}\n\tpath.totalPoints = points + i;\n\treturn path;\n}\n\n//populates the points array in alternating x/y values (like [x, y, x, y...] instead of individual point objects [{x, y}, {x, y}...] to conserve memory and stay in line with how we're handling segment arrays\nexport function bezierToPoints(x1, y1, x2, y2, x3, y3, x4, y4, threshold, points, index) {\n\tlet x12 = (x1 + x2) / 2,\n\t\ty12 = (y1 + y2) / 2,\n\t\tx23 = (x2 + x3) / 2,\n\t\ty23 = (y2 + y3) / 2,\n\t\tx34 = (x3 + x4) / 2,\n\t\ty34 = (y3 + y4) / 2,\n\t\tx123 = (x12 + x23) / 2,\n\t\ty123 = (y12 + y23) / 2,\n\t\tx234 = (x23 + x34) / 2,\n\t\ty234 = (y23 + y34) / 2,\n\t\tx1234 = (x123 + x234) / 2,\n\t\ty1234 = (y123 + y234) / 2,\n\t\tdx = x4 - x1,\n\t\tdy = y4 - y1,\n\t\td2 = _abs((x2 - x4) * dy - (y2 - y4) * dx),\n\t\td3 = _abs((x3 - x4) * dy - (y3 - y4) * dx),\n\t\tlength;\n\tif (!points) {\n\t\tpoints = [x1, y1, x4, y4];\n\t\tindex = 2;\n\t}\n\tpoints.splice(index || points.length - 2, 0, x1234, y1234);\n\tif ((d2 + d3) * (d2 + d3) > threshold * (dx * dx + dy * dy)) {\n\t\tlength = points.length;\n\t\tbezierToPoints(x1, y1, x12, y12, x123, y123, x1234, y1234, threshold, points, index);\n\t\tbezierToPoints(x1234, y1234, x234, y234, x34, y34, x4, y4, threshold, points, index + 2 + (points.length - length));\n\t}\n\treturn points;\n}\n\n/*\nfunction getAngleBetweenPoints(x0, y0, x1, y1, x2, y2) { //angle between 3 points in radians\n\tvar dx1 = x1 - x0,\n\t\tdy1 = y1 - y0,\n\t\tdx2 = x2 - x1,\n\t\tdy2 = y2 - y1,\n\t\tdx3 = x2 - x0,\n\t\tdy3 = y2 - y0,\n\t\ta = dx1 * dx1 + dy1 * dy1,\n\t\tb = dx2 * dx2 + dy2 * dy2,\n\t\tc = dx3 * dx3 + dy3 * dy3;\n\treturn Math.acos( (a + b - c) / _sqrt(4 * a * b) );\n},\n*/\n\n//pointsToSegment() doesn't handle flat coordinates (where y is always 0) the way we need (the resulting control points are always right on top of the anchors), so this function basically makes the control points go directly up and down, varying in length based on the curviness (more curvy, further control points)\nexport function flatPointsToSegment(points, curviness=1) {\n\tlet x = points[0],\n\t\ty = 0,\n\t\tsegment = [x, y],\n\t\ti = 2;\n\tfor (; i < points.length; i+=2) {\n\t\tsegment.push(\n\t\t\tx,\n\t\t\ty,\n\t\t\tpoints[i],\n\t\t\t(y = (points[i] - x) * curviness / 2),\n\t\t\t(x = points[i]),\n\t\t\t-y\n\t\t);\n\t}\n\treturn segment;\n}\n\n//points is an array of x/y points, like [x, y, x, y, x, y]\nexport function pointsToSegment(points, curviness) {\n\t//points = simplifyPoints(points, tolerance);\n\t_abs(points[0] - points[2]) < 1e-4 && _abs(points[1] - points[3]) < 1e-4 && (points = points.slice(2)); // if the first two points are super close, dump the first one.\n\tlet l = points.length-2,\n\t\tx = +points[0],\n\t\ty = +points[1],\n\t\tnextX = +points[2],\n\t\tnextY = +points[3],\n\t\tsegment = [x, y, x, y],\n\t\tdx2 = nextX - x,\n\t\tdy2 = nextY - y,\n\t\tclosed = Math.abs(points[l] - x) < 0.001 && Math.abs(points[l+1] - y) < 0.001,\n\t\tprevX, prevY, i, dx1, dy1, r1, r2, r3, tl, mx1, mx2, mxm, my1, my2, mym;\n\tif (closed) { // if the start and end points are basically on top of each other, close the segment by adding the 2nd point to the end, and the 2nd-to-last point to the beginning (we'll remove them at the end, but this allows the curvature to look perfect)\n\t\tpoints.push(nextX, nextY);\n\t\tnextX = x;\n\t\tnextY = y;\n\t\tx = points[l-2];\n\t\ty = points[l-1];\n\t\tpoints.unshift(x, y);\n\t\tl+=4;\n\t}\n\tcurviness = (curviness || curviness === 0) ? +curviness : 1;\n\tfor (i = 2; i < l; i+=2) {\n\t\tprevX = x;\n\t\tprevY = y;\n\t\tx = nextX;\n\t\ty = nextY;\n\t\tnextX = +points[i+2];\n\t\tnextY = +points[i+3];\n\t\tif (x === nextX && y === nextY) {\n\t\t\tcontinue;\n\t\t}\n\t\tdx1 = dx2;\n\t\tdy1 = dy2;\n\t\tdx2 = nextX - x;\n\t\tdy2 = nextY - y;\n\t\tr1 = _sqrt(dx1 * dx1 + dy1 * dy1); // r1, r2, and r3 correlate x and y (and z in the future). Basically 2D or 3D hypotenuse\n\t\tr2 = _sqrt(dx2 * dx2 + dy2 * dy2);\n\t\tr3 =  _sqrt((dx2 / r2 + dx1 / r1) ** 2 + (dy2 / r2 + dy1 / r1) ** 2);\n\t\ttl = ((r1 + r2) * curviness * 0.25) / r3;\n\t\tmx1 = x - (x - prevX) * (r1 ? tl / r1 : 0);\n\t\tmx2 = x + (nextX - x) * (r2 ? tl / r2 : 0);\n\t\tmxm = x - (mx1 + (((mx2 - mx1) * ((r1 * 3 / (r1 + r2)) + 0.5) / 4) || 0));\n\t\tmy1 = y - (y - prevY) * (r1 ? tl / r1 : 0);\n\t\tmy2 = y + (nextY - y) * (r2 ? tl / r2 : 0);\n\t\tmym = y - (my1 + (((my2 - my1) * ((r1 * 3 / (r1 + r2)) + 0.5) / 4) || 0));\n\t\tif (x !== prevX || y !== prevY) {\n\t\t\tsegment.push(\n\t\t\t\t_round(mx1 + mxm),  // first control point\n\t\t\t\t_round(my1 + mym),\n\t\t\t\t_round(x),          // anchor\n\t\t\t\t_round(y),\n\t\t\t\t_round(mx2 + mxm),  // second control point\n\t\t\t\t_round(my2 + mym)\n\t\t\t);\n\t\t}\n\t}\n\tx !== nextX || y !== nextY || segment.length < 4 ? segment.push(_round(nextX), _round(nextY), _round(nextX), _round(nextY)) : (segment.length -= 2);\n\tif (segment.length === 2) { // only one point!\n\t\tsegment.push(x, y, x, y, x, y);\n\t} else if (closed) {\n\t\tsegment.splice(0, 6);\n\t\tsegment.length = segment.length - 6;\n\t}\n\treturn segment;\n}\n\n//returns the squared distance between an x/y coordinate and a segment between x1/y1 and x2/y2\nfunction pointToSegDist(x, y, x1, y1, x2, y2) {\n\tlet dx = x2 - x1,\n\t\tdy = y2 - y1,\n\t\tt;\n\tif (dx || dy) {\n\t\tt = ((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy);\n\t\tif (t > 1) {\n\t\t\tx1 = x2;\n\t\t\ty1 = y2;\n\t\t} else if (t > 0) {\n\t\t\tx1 += dx * t;\n\t\t\ty1 += dy * t;\n\t\t}\n\t}\n\treturn (x - x1) ** 2 + (y - y1) ** 2;\n}\n\nfunction simplifyStep(points, first, last, tolerance, simplified) {\n\tlet maxSqDist = tolerance,\n\t\tfirstX = points[first],\n\t\tfirstY = points[first+1],\n\t\tlastX = points[last],\n\t\tlastY = points[last+1],\n\t\tindex, i, d;\n\tfor (i = first + 2; i < last; i += 2) {\n\t\td = pointToSegDist(points[i], points[i+1], firstX, firstY, lastX, lastY);\n\t\tif (d > maxSqDist) {\n\t\t\tindex = i;\n\t\t\tmaxSqDist = d;\n\t\t}\n\t}\n\tif (maxSqDist > tolerance) {\n\t\tindex - first > 2 && simplifyStep(points, first, index, tolerance, simplified);\n\t\tsimplified.push(points[index], points[index+1]);\n\t\tlast - index > 2 && simplifyStep(points, index, last, tolerance, simplified);\n\t}\n}\n\n//points is an array of x/y values like [x, y, x, y, x, y]\nexport function simplifyPoints(points, tolerance) {\n\tlet prevX = parseFloat(points[0]),\n\t\tprevY = parseFloat(points[1]),\n\t\ttemp = [prevX, prevY],\n\t\tl = points.length - 2,\n\t\ti, x, y, dx, dy, result, last;\n\ttolerance = (tolerance || 1) ** 2;\n\tfor (i = 2; i < l; i += 2) {\n\t\tx = parseFloat(points[i]);\n\t\ty = parseFloat(points[i+1]);\n\t\tdx = prevX - x;\n\t\tdy = prevY - y;\n\t\tif (dx * dx + dy * dy > tolerance) {\n\t\t\ttemp.push(x, y);\n\t\t\tprevX = x;\n\t\t\tprevY = y;\n\t\t}\n\t}\n\ttemp.push(parseFloat(points[l]), parseFloat(points[l+1]));\n\tlast = temp.length - 2;\n\tresult = [temp[0], temp[1]];\n\tsimplifyStep(temp, 0, last, tolerance, result);\n\tresult.push(temp[last], temp[last+1]);\n\treturn result;\n}\n\nfunction getClosestProgressOnBezier(iterations, px, py, start, end, slices, x0, y0, x1, y1, x2, y2, x3, y3) {\n\tlet inc = (end - start) / slices,\n\t\tbest = 0,\n\t\tt = start,\n\t\tx, y, d, dx, dy, inv;\n\t_bestDistance = _largeNum;\n\twhile (t <= end) {\n\t\tinv = 1 - t;\n\t\tx = inv * inv * inv * x0 + 3 * inv * inv * t * x1 + 3 * inv * t * t * x2 + t * t * t * x3;\n\t\ty = inv * inv * inv * y0 + 3 * inv * inv * t * y1 + 3 * inv * t * t * y2 + t * t * t * y3;\n\t\tdx = x - px;\n\t\tdy = y - py;\n\t\td = dx * dx + dy * dy;\n\t\tif (d < _bestDistance) {\n\t\t\t_bestDistance = d;\n\t\t\tbest = t;\n\t\t}\n\t\tt += inc;\n\t}\n\treturn (iterations > 1) ? getClosestProgressOnBezier(iterations - 1, px, py, Math.max(best - inc, 0), Math.min(best + inc, 1), slices, x0, y0, x1, y1, x2, y2, x3, y3) : best;\n}\n\nexport function getClosestData(rawPath, x, y, slices) { //returns an object with the closest j, i, and t (j is the segment index, i is the index of the point in that segment, and t is the time/progress along that bezier)\n\tlet closest = {j:0, i:0, t:0},\n\t\tbestDistance = _largeNum,\n\t\ti, j, t, segment;\n\tfor (j = 0; j < rawPath.length; j++) {\n\t\tsegment = rawPath[j];\n\t\tfor (i = 0; i < segment.length; i+=6) {\n\t\t\tt = getClosestProgressOnBezier(1, x, y, 0, 1, slices || 20, segment[i], segment[i+1], segment[i+2], segment[i+3], segment[i+4], segment[i+5], segment[i+6], segment[i+7]);\n\t\t\tif (bestDistance > _bestDistance) {\n\t\t\t\tbestDistance = _bestDistance;\n\t\t\t\tclosest.j = j;\n\t\t\t\tclosest.i = i;\n\t\t\t\tclosest.t = t;\n\t\t\t}\n\t\t}\n\t}\n\treturn closest;\n}\n\n//subdivide a Segment closest to a specific x,y coordinate\nexport function subdivideSegmentNear(x, y, segment, slices, iterations) {\n\tlet l = segment.length,\n\t\tbestDistance = _largeNum,\n\t\tbestT = 0,\n\t\tbestSegmentIndex = 0,\n\t\tt, i;\n\tslices = slices || 20;\n\titerations = iterations || 3;\n\tfor (i = 0; i < l; i += 6) {\n\t\tt = getClosestProgressOnBezier(1, x, y, 0, 1, slices, segment[i], segment[i+1], segment[i+2], segment[i+3], segment[i+4], segment[i+5], segment[i+6], segment[i+7]);\n\t\tif (bestDistance > _bestDistance) {\n\t\t\tbestDistance = _bestDistance;\n\t\t\tbestT = t;\n\t\t\tbestSegmentIndex = i;\n\t\t}\n\t}\n\tt = getClosestProgressOnBezier(iterations, x, y, bestT - 0.05, bestT + 0.05, slices, segment[bestSegmentIndex], segment[bestSegmentIndex+1], segment[bestSegmentIndex+2], segment[bestSegmentIndex+3], segment[bestSegmentIndex+4], segment[bestSegmentIndex+5], segment[bestSegmentIndex+6], segment[bestSegmentIndex+7]);\n\tsubdivideSegment(segment, bestSegmentIndex, t);\n\treturn bestSegmentIndex + 6;\n}\n\n/*\nTakes any of the following and converts it to an all Cubic Bezier SVG data string:\n- A <path> data string like \"M0,0 L2,4 v20,15 H100\"\n- A RawPath, like [[x, y, x, y, x, y, x, y][[x, y, x, y, x, y, x, y]]\n- A Segment, like [x, y, x, y, x, y, x, y]\n\nNote: all numbers are rounded down to the closest 0.001 to minimize memory, maximize speed, and avoid odd numbers like 1e-13\n*/\nexport function rawPathToString(rawPath) {\n\tif (_isNumber(rawPath[0])) { //in case a segment is passed in instead\n\t\trawPath = [rawPath];\n\t}\n\tlet result = \"\",\n\t\tl = rawPath.length,\n\t\tsl, s, i, segment;\n\tfor (s = 0; s < l; s++) {\n\t\tsegment = rawPath[s];\n\t\tresult += \"M\" + _round(segment[0]) + \",\" + _round(segment[1]) + \" C\";\n\t\tsl = segment.length;\n\t\tfor (i = 2; i < sl; i++) {\n\t\t\tresult += _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i]) + \" \";\n\t\t}\n\t\tif (segment.closed) {\n\t\t\tresult += \"z\";\n\t\t}\n\t}\n\treturn result;\n}\n\n/*\n// takes a segment with coordinates [x, y, x, y, ...] and converts the control points into angles and lengths [x, y, angle, length, angle, length, x, y, angle, length, ...] so that it animates more cleanly and avoids odd breaks/kinks. For example, if you animate from 1 o'clock to 6 o'clock, it'd just go directly/linearly rather than around. So the length would be very short in the middle of the tween.\nexport function cpCoordsToAngles(segment, copy) {\n\tvar result = copy ? segment.slice(0) : segment,\n\t\tx, y, i;\n\tfor (i = 0; i < segment.length; i+=6) {\n\t\tx = segment[i+2] - segment[i];\n\t\ty = segment[i+3] - segment[i+1];\n\t\tresult[i+2] = Math.atan2(y, x);\n\t\tresult[i+3] = Math.sqrt(x * x + y * y);\n\t\tx = segment[i+6] - segment[i+4];\n\t\ty = segment[i+7] - segment[i+5];\n\t\tresult[i+4] = Math.atan2(y, x);\n\t\tresult[i+5] = Math.sqrt(x * x + y * y);\n\t}\n\treturn result;\n}\n\n// takes a segment that was converted with cpCoordsToAngles() to have angles and lengths instead of coordinates for the control points, and converts it BACK into coordinates.\nexport function cpAnglesToCoords(segment, copy) {\n\tvar result = copy ? segment.slice(0) : segment,\n\t\tlength = segment.length,\n\t\trnd = 1000,\n\t\tangle, l, i, j;\n\tfor (i = 0; i < length; i+=6) {\n\t\tangle = segment[i+2];\n\t\tl = segment[i+3]; //length\n\t\tresult[i+2] = (((segment[i] + Math.cos(angle) * l) * rnd) | 0) / rnd;\n\t\tresult[i+3] = (((segment[i+1] + Math.sin(angle) * l) * rnd) | 0) / rnd;\n\t\tangle = segment[i+4];\n\t\tl = segment[i+5]; //length\n\t\tresult[i+4] = (((segment[i+6] - Math.cos(angle) * l) * rnd) | 0) / rnd;\n\t\tresult[i+5] = (((segment[i+7] - Math.sin(angle) * l) * rnd) | 0) / rnd;\n\t}\n\treturn result;\n}\n\n//adds an \"isSmooth\" array to each segment and populates it with a boolean value indicating whether or not it's smooth (the control points have basically the same slope). For any smooth control points, it converts the coordinates into angle (x, in radians) and length (y) and puts them into the same index value in a smoothData array.\nexport function populateSmoothData(rawPath) {\n\tlet j = rawPath.length,\n\t\tsmooth, segment, x, y, x2, y2, i, l, a, a2, isSmooth, smoothData;\n\twhile (--j > -1) {\n\t\tsegment = rawPath[j];\n\t\tisSmooth = segment.isSmooth = segment.isSmooth || [0, 0, 0, 0];\n\t\tsmoothData = segment.smoothData = segment.smoothData || [0, 0, 0, 0];\n\t\tisSmooth.length = 4;\n\t\tl = segment.length - 2;\n\t\tfor (i = 6; i < l; i += 6) {\n\t\t\tx = segment[i] - segment[i - 2];\n\t\t\ty = segment[i + 1] - segment[i - 1];\n\t\t\tx2 = segment[i + 2] - segment[i];\n\t\t\ty2 = segment[i + 3] - segment[i + 1];\n\t\t\ta = _atan2(y, x);\n\t\t\ta2 = _atan2(y2, x2);\n\t\t\tsmooth = (Math.abs(a - a2) < 0.09);\n\t\t\tif (smooth) {\n\t\t\t\tsmoothData[i - 2] = a;\n\t\t\t\tsmoothData[i + 2] = a2;\n\t\t\t\tsmoothData[i - 1] = _sqrt(x * x + y * y);\n\t\t\t\tsmoothData[i + 3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t}\n\t\t\tisSmooth.push(smooth, smooth, 0, 0, smooth, smooth);\n\t\t}\n\t\t//if the first and last points are identical, check to see if there's a smooth transition. We must handle this a bit differently due to their positions in the array.\n\t\tif (segment[l] === segment[0] && segment[l+1] === segment[1]) {\n\t\t\tx = segment[0] - segment[l-2];\n\t\t\ty = segment[1] - segment[l-1];\n\t\t\tx2 = segment[2] - segment[0];\n\t\t\ty2 = segment[3] - segment[1];\n\t\t\ta = _atan2(y, x);\n\t\t\ta2 = _atan2(y2, x2);\n\t\t\tif (Math.abs(a - a2) < 0.09) {\n\t\t\t\tsmoothData[l-2] = a;\n\t\t\t\tsmoothData[2] = a2;\n\t\t\t\tsmoothData[l-1] = _sqrt(x * x + y * y);\n\t\t\t\tsmoothData[3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t\tisSmooth[l-2] = isSmooth[l-1] = true; //don't change indexes 2 and 3 because we'll trigger everything from the END, and this will optimize file size a bit.\n\t\t\t}\n\t\t}\n\t}\n\treturn rawPath;\n}\nexport function pointToScreen(svgElement, point) {\n\tif (arguments.length < 2) { //by default, take the first set of coordinates in the path as the point\n\t\tlet rawPath = getRawPath(svgElement);\n\t\tpoint = svgElement.ownerSVGElement.createSVGPoint();\n\t\tpoint.x = rawPath[0][0];\n\t\tpoint.y = rawPath[0][1];\n\t}\n\treturn point.matrixTransform(svgElement.getScreenCTM());\n}\n// takes a <path> and normalizes all of its coordinates to values between 0 and 1\nexport function normalizePath(path) {\n  path = gsap.utils.toArray(path);\n  if (!path[0].hasAttribute(\"d\")) {\n    path = gsap.utils.toArray(path[0].children);\n  }\n  if (path.length > 1) {\n    path.forEach(normalizePath);\n    return path;\n  }\n  let _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n      _scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\n      d = path[0].getAttribute(\"d\"),\n      a = d.replace(_scientific, m => { let n = +m; return (n < 0.0001 && n > -0.0001) ? 0 : n; }).match(_svgPathExp),\n      nums = a.filter(n => !isNaN(n)).map(n => +n),\n      normalize = gsap.utils.normalize(Math.min(...nums), Math.max(...nums)),\n      finals = a.map(val => isNaN(val) ? val : normalize(+val)),\n      s = \"\",\n      prevWasCommand;\n  finals.forEach((value, i) => {\n    let isCommand = isNaN(value)\n    s += (isCommand && i ? \" \" : prevWasCommand || !i ? \"\" : \",\") + value;\n    prevWasCommand = isCommand;\n  });\n  path[0].setAttribute(\"d\", s);\n}\n*/", "/*!\n * matrix 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _doc, _win, _doc<PERSON><PERSON>, _body,\t_div<PERSON><PERSON>r, _svg<PERSON><PERSON>r, _identityMatrix, _gEl,\n\t_transformProp = \"transform\",\n\t_transformOriginProp = _transformProp + \"Origin\",\n\t_hasOffsetBug,\n\t_setDoc = element => {\n\t\tlet doc = element.ownerDocument || element;\n\t\tif (!(_transformProp in element.style) && \"msTransform\" in element.style) { //to improve compatibility with old Microsoft browsers\n\t\t\t_transformProp = \"msTransform\";\n\t\t\t_transformOriginProp = _transformProp + \"Origin\";\n\t\t}\n\t\twhile (doc.parentNode && (doc = doc.parentNode)) {\t}\n\t\t_win = window;\n\t\t_identityMatrix = new Matrix2D();\n\t\tif (doc) {\n\t\t\t_doc = doc;\n\t\t\t_docElement = doc.documentElement;\n\t\t\t_body = doc.body;\n\t\t\t_gEl = _doc.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n\t\t\t// prevent any existing CSS from transforming it\n\t\t\t_gEl.style.transform = \"none\";\n\t\t\t// now test for the offset reporting bug. Use feature detection instead of browser sniffing to make things more bulletproof and future-proof. Hopefully Safari will fix their bug soon.\n\t\t\tlet d1 = doc.createElement(\"div\"),\n\t\t\t\td2 = doc.createElement(\"div\"),\n\t\t\t\troot = doc && (doc.body || doc.firstElementChild);\n\t\t\tif (root && root.appendChild) {\n\t\t\t\troot.appendChild(d1);\n\t\t\t\td1.appendChild(d2);\n\t\t\t\td1.setAttribute(\"style\", \"position:static;transform:translate3d(0,0,1px)\");\n\t\t\t\t_hasOffsetBug = (d2.offsetParent !== d1);\n\t\t\t\troot.removeChild(d1);\n\t\t\t}\n\t\t}\n\t\treturn doc;\n\t},\n\t_forceNonZeroScale = e => { // walks up the element's ancestors and finds any that had their scale set to 0 via GSAP, and changes them to 0.0001 to ensure that measurements work. Firefox has a bug that causes it to incorrectly report getBoundingClientRect() when scale is 0.\n\t\tlet a, cache;\n\t\twhile (e && e !== _body) {\n\t\t\tcache = e._gsap;\n\t\t\tcache && cache.uncache && cache.get(e, \"x\"); // force re-parsing of transforms if necessary\n\t\t\tif (cache && !cache.scaleX && !cache.scaleY && cache.renderTransform) {\n\t\t\t\tcache.scaleX = cache.scaleY = 1e-4;\n\t\t\t\tcache.renderTransform(1, cache);\n\t\t\t\ta ? a.push(cache) : (a = [cache]);\n\t\t\t}\n\t\t\te = e.parentNode;\n\t\t}\n\t\treturn a;\n\t},\n\t// possible future addition: pass an element to _forceDisplay() and it'll walk up all its ancestors and make sure anything with display: none is set to display: block, and if there's no parentNode, it'll add it to the body. It returns an Array that you can then feed to _revertDisplay() to have it revert all the changes it made.\n\t// _forceDisplay = e => {\n\t// \tlet a = [],\n\t// \t\tparent;\n\t// \twhile (e && e !== _body) {\n\t// \t\tparent = e.parentNode;\n\t// \t\t(_win.getComputedStyle(e).display === \"none\" || !parent) && a.push(e, e.style.display, parent) && (e.style.display = \"block\");\n\t// \t\tparent || _body.appendChild(e);\n\t// \t\te = parent;\n\t// \t}\n\t// \treturn a;\n\t// },\n\t// _revertDisplay = a => {\n\t// \tfor (let i = 0; i < a.length; i+=3) {\n\t// \t\ta[i+1] ? (a[i].style.display = a[i+1]) : a[i].style.removeProperty(\"display\");\n\t// \t\ta[i+2] || a[i].parentNode.removeChild(a[i]);\n\t// \t}\n\t// },\n\t_svgTemps = [], //we create 3 elements for SVG, and 3 for other DOM elements and cache them for performance reasons. They get nested in _divContainer and _svgContainer so that just one element is added to the DOM on each successive attempt. Again, performance is key.\n\t_divTemps = [],\n\t_getDocScrollTop = () => _win.pageYOffset  || _doc.scrollTop || _docElement.scrollTop || _body.scrollTop || 0,\n\t_getDocScrollLeft = () => _win.pageXOffset || _doc.scrollLeft || _docElement.scrollLeft || _body.scrollLeft || 0,\n\t_svgOwner = element => element.ownerSVGElement || ((element.tagName + \"\").toLowerCase() === \"svg\" ? element : null),\n\t_isFixed = element => {\n\t\tif (_win.getComputedStyle(element).position === \"fixed\") {\n\t\t\treturn true;\n\t\t}\n\t\telement = element.parentNode;\n\t\tif (element && element.nodeType === 1) { // avoid document fragments which will throw an error.\n\t\t\treturn _isFixed(element);\n\t\t}\n\t},\n\t_createSibling = (element, i) => {\n\t\tif (element.parentNode && (_doc || _setDoc(element))) {\n\t\t\tlet svg = _svgOwner(element),\n\t\t\t\tns = svg ? (svg.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\") : \"http://www.w3.org/1999/xhtml\",\n\t\t\t\ttype = svg ? (i ? \"rect\" : \"g\") : \"div\",\n\t\t\t\tx = i !== 2 ? 0 : 100,\n\t\t\t\ty = i === 3 ? 100 : 0,\n\t\t\t\tcss = \"position:absolute;display:block;pointer-events:none;margin:0;padding:0;\",\n\t\t\t\te = _doc.createElementNS ? _doc.createElementNS(ns.replace(/^https/, \"http\"), type) : _doc.createElement(type);\n\t\t\tif (i) {\n\t\t\t\tif (!svg) {\n\t\t\t\t\tif (!_divContainer) {\n\t\t\t\t\t\t_divContainer = _createSibling(element);\n\t\t\t\t\t\t_divContainer.style.cssText = css;\n\t\t\t\t\t}\n\t\t\t\t\te.style.cssText = css + \"width:0.1px;height:0.1px;top:\" + y + \"px;left:\" + x + \"px\";\n\t\t\t\t\t_divContainer.appendChild(e);\n\n\t\t\t\t} else {\n\t\t\t\t\t_svgContainer || (_svgContainer = _createSibling(element));\n\t\t\t\t\te.setAttribute(\"width\", 0.01);\n\t\t\t\t\te.setAttribute(\"height\", 0.01);\n\t\t\t\t\te.setAttribute(\"transform\", \"translate(\" + x + \",\" + y + \")\");\n\t\t\t\t\t_svgContainer.appendChild(e);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn e;\n\t\t}\n\t\tthrow \"Need document and parent.\";\n\t},\n\t_consolidate = m => { // replaces SVGTransformList.consolidate() because a bug in Firefox causes it to break pointer events. See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\t\tlet c = new Matrix2D(),\n\t\t\ti = 0;\n\t\tfor (; i < m.numberOfItems; i++) {\n\t\t\tc.multiply(m.getItem(i).matrix);\n\t\t}\n\t\treturn c;\n\t},\n\t_getCTM = svg => {\n\t\tlet m = svg.getCTM(),\n\t\t\ttransform;\n\t\tif (!m) { // Firefox returns null for getCTM() on root <svg> elements, so this is a workaround using a <g> that we temporarily append.\n\t\t\ttransform = svg.style[_transformProp];\n\t\t\tsvg.style[_transformProp] = \"none\"; // a bug in Firefox causes css transforms to contaminate the getCTM()\n\t\t\tsvg.appendChild(_gEl);\n\t\t\tm = _gEl.getCTM();\n\t\t\tsvg.removeChild(_gEl);\n\t\t\ttransform ? (svg.style[_transformProp] = transform) : svg.style.removeProperty(_transformProp.replace(/([A-Z])/g, \"-$1\").toLowerCase());\n\t\t}\n\t\treturn m || _identityMatrix.clone(); // Firefox will still return null if the <svg> has a width/height of 0 in the browser.\n\t},\n\t_placeSiblings = (element, adjustGOffset) => {\n\t\tlet svg = _svgOwner(element),\n\t\t\tisRootSVG = element === svg,\n\t\t\tsiblings = svg ? _svgTemps : _divTemps,\n\t\t\tparent = element.parentNode,\n\t\t\tappendToEl = parent && !svg && parent.shadowRoot && parent.shadowRoot.appendChild ? parent.shadowRoot : parent,\n\t\t\tcontainer, m, b, x, y, cs;\n\t\tif (element === _win) {\n\t\t\treturn element;\n\t\t}\n\t\tsiblings.length || siblings.push(_createSibling(element, 1), _createSibling(element, 2), _createSibling(element, 3));\n\t\tcontainer = svg ? _svgContainer : _divContainer;\n\t\tif (svg) {\n\t\t\tif (isRootSVG) {\n\t\t\t\tb = _getCTM(element);\n\t\t\t\tx = -b.e / b.a;\n\t\t\t\ty = -b.f / b.d;\n\t\t\t\tm = _identityMatrix;\n\t\t\t} else if (element.getBBox) {\n\t\t\t\tb = element.getBBox();\n\t\t\t\tm = element.transform ? element.transform.baseVal : {}; // IE11 doesn't follow the spec.\n\t\t\t\tm = !m.numberOfItems ? _identityMatrix : m.numberOfItems > 1 ? _consolidate(m) : m.getItem(0).matrix; // don't call m.consolidate().matrix because a bug in Firefox makes pointer events not work when consolidate() is called on the same tick as getBoundingClientRect()! See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\t\t\t\tx = m.a * b.x + m.c * b.y;\n\t\t\t\ty = m.b * b.x + m.d * b.y;\n\t\t\t} else { // may be a <mask> which has no getBBox() so just use defaults instead of throwing errors.\n\t\t\t\tm = new Matrix2D();\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\tif (adjustGOffset && element.tagName.toLowerCase() === \"g\") {\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\t(isRootSVG ? svg : parent).appendChild(container);\n\t\t\tcontainer.setAttribute(\"transform\", \"matrix(\" + m.a + \",\" + m.b + \",\" + m.c + \",\" + m.d + \",\" + (m.e + x) + \",\" + (m.f + y) + \")\");\n\t\t} else {\n\t\t\tx = y = 0;\n\t\t\tif (_hasOffsetBug) { // some browsers (like Safari) have a bug that causes them to misreport offset values. When an ancestor element has a transform applied, it's supposed to treat it as if it's position: relative (new context). Safari botches this, so we need to find the closest ancestor (between the element and its offsetParent) that has a transform applied and if one is found, grab its offsetTop/Left and subtract them to compensate.\n\t\t\t\tm = element.offsetParent;\n\t\t\t\tb = element;\n\t\t\t\twhile (b && (b = b.parentNode) && b !== m && b.parentNode) {\n\t\t\t\t\tif ((_win.getComputedStyle(b)[_transformProp] + \"\").length > 4) {\n\t\t\t\t\t\tx = b.offsetLeft;\n\t\t\t\t\t\ty = b.offsetTop;\n\t\t\t\t\t\tb = 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tcs = _win.getComputedStyle(element);\n\t\t\tif (cs.position !== \"absolute\" && cs.position !== \"fixed\") {\n\t\t\t\tm = element.offsetParent;\n\t\t\t\twhile (parent && parent !== m) { // if there's an ancestor element between the element and its offsetParent that's scrolled, we must factor that in.\n\t\t\t\t\tx += parent.scrollLeft || 0;\n\t\t\t\t\ty += parent.scrollTop || 0;\n\t\t\t\t\tparent = parent.parentNode;\n\t\t\t\t}\n\t\t\t}\n\t\t\tb = container.style;\n\t\t\tb.top = (element.offsetTop - y) + \"px\";\n\t\t\tb.left = (element.offsetLeft - x) + \"px\";\n\t\t\tb[_transformProp] = cs[_transformProp];\n\t\t\tb[_transformOriginProp] = cs[_transformOriginProp];\n\t\t\t// b.border = m.border;\n\t\t\t// b.borderLeftStyle = m.borderLeftStyle;\n\t\t\t// b.borderTopStyle = m.borderTopStyle;\n\t\t\t// b.borderLeftWidth = m.borderLeftWidth;\n\t\t\t// b.borderTopWidth = m.borderTopWidth;\n\t\t\tb.position = cs.position === \"fixed\" ? \"fixed\" : \"absolute\";\n\t\t\tappendToEl.appendChild(container);\n\t\t}\n\t\treturn container;\n\t},\n\t_setMatrix = (m, a, b, c, d, e, f) => {\n\t\tm.a = a;\n\t\tm.b = b;\n\t\tm.c = c;\n\t\tm.d = d;\n\t\tm.e = e;\n\t\tm.f = f;\n\t\treturn m;\n\t};\n\nexport class Matrix2D {\n\tconstructor(a=1, b=0, c=0, d=1, e=0, f=0) {\n\t\t_setMatrix(this, a, b, c, d, e, f);\n\t}\n\n\tinverse() {\n\t\tlet {a, b, c, d, e, f} = this,\n\t\t\tdeterminant = (a * d - b * c) || 1e-10;\n\t\treturn _setMatrix(\n\t\t\tthis,\n\t\t\td / determinant,\n\t\t\t-b / determinant,\n\t\t\t-c / determinant,\n\t\t\ta / determinant,\n\t\t\t(c * f - d * e) / determinant,\n\t\t\t-(a * f - b * e) / determinant\n\t\t);\n\t}\n\n\tmultiply(matrix) {\n\t\tlet {a, b, c, d, e, f} = this,\n\t\t\ta2 = matrix.a,\n\t\t\tb2 = matrix.c,\n\t\t\tc2 = matrix.b,\n\t\t\td2 = matrix.d,\n\t\t\te2 = matrix.e,\n\t\t\tf2 = matrix.f;\n\t\treturn _setMatrix(this,\n\t\t\ta2 * a + c2 * c,\n\t\t\ta2 * b + c2 * d,\n\t\t\tb2 * a + d2 * c,\n\t\t\tb2 * b + d2 * d,\n\t\t\te + e2 * a + f2 * c,\n\t\t\tf + e2 * b + f2 * d);\n\t}\n\n\tclone() {\n\t\treturn new Matrix2D(this.a, this.b, this.c, this.d, this.e, this.f);\n\t}\n\n\tequals(matrix) {\n\t\tlet {a, b, c, d, e, f} = this;\n\t\treturn (a === matrix.a && b === matrix.b && c === matrix.c && d === matrix.d && e === matrix.e && f === matrix.f);\n\t}\n\n\tapply(point, decoratee={}) {\n\t\tlet {x, y} = point,\n\t\t\t{a, b, c, d, e, f} = this;\n\t\tdecoratee.x = (x * a + y * c + e) || 0;\n\t\tdecoratee.y = (x * b + y * d + f) || 0;\n\t\treturn decoratee;\n\t}\n\n}\n\n// Feed in an element and it'll return a 2D matrix (optionally inverted) so that you can translate between coordinate spaces.\n// Inverting lets you translate a global point into a local coordinate space. No inverting lets you go the other way.\n// We needed this to work around various browser bugs, like Firefox doesn't accurately report getScreenCTM() when there\n// are transforms applied to ancestor elements.\n// The matrix math to convert any x/y coordinate is as follows, which is wrapped in a convenient apply() method of Matrix2D above:\n//     tx = m.a * x + m.c * y + m.e\n//     ty = m.b * x + m.d * y + m.f\nexport function getGlobalMatrix(element, inverse, adjustGOffset, includeScrollInFixed) { // adjustGOffset is typically used only when grabbing an element's PARENT's global matrix, and it ignores the x/y offset of any SVG <g> elements because they behave in a special way.\n\tif (!element || !element.parentNode || (_doc || _setDoc(element)).documentElement === element) {\n\t\treturn new Matrix2D();\n\t}\n\tlet zeroScales = _forceNonZeroScale(element),\n\t\tsvg = _svgOwner(element),\n\t\ttemps = svg ? _svgTemps : _divTemps,\n\t\tcontainer = _placeSiblings(element, adjustGOffset),\n\t\tb1 = temps[0].getBoundingClientRect(),\n\t\tb2 = temps[1].getBoundingClientRect(),\n\t\tb3 = temps[2].getBoundingClientRect(),\n\t\tparent = container.parentNode,\n\t\tisFixed = !includeScrollInFixed && _isFixed(element),\n\t\tm = new Matrix2D(\n\t\t\t(b2.left - b1.left) / 100,\n\t\t\t(b2.top - b1.top) / 100,\n\t\t\t(b3.left - b1.left) / 100,\n\t\t\t(b3.top - b1.top) / 100,\n\t\t\tb1.left + (isFixed ? 0 : _getDocScrollLeft()),\n\t\t\tb1.top + (isFixed ? 0 : _getDocScrollTop())\n\t\t);\n\tparent.removeChild(container);\n\tif (zeroScales) {\n\t\tb1 = zeroScales.length;\n\t\twhile (b1--) {\n\t\t\tb2 = zeroScales[b1];\n\t\t\tb2.scaleX = b2.scaleY = 0;\n\t\t\tb2.renderTransform(1, b2);\n\t\t}\n\t}\n\treturn inverse ? m.inverse() : m;\n}\n\nexport { _getDocScrollTop, _getDocScrollLeft, _setDoc, _isFixed, _getCTM };\n\n// export function getMatrix(element) {\n// \t_doc || _setDoc(element);\n// \tlet m = (_win.getComputedStyle(element)[_transformProp] + \"\").substr(7).match(/[-.]*\\d+[.e\\-+]*\\d*[e\\-\\+]*\\d*/g),\n// \t\tis2D = m && m.length === 6;\n// \treturn !m || m.length < 6 ? new Matrix2D() : new Matrix2D(+m[0], +m[1], +m[is2D ? 2 : 4], +m[is2D ? 3 : 5], +m[is2D ? 4 : 12], +m[is2D ? 5 : 13]);\n// }", "/*!\n * PathEditor 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { stringToRawPath, rawPathToString, bezierToPoints, simplifyPoints, pointsToSegment, subdivideSegment, getClosestData, copyRawPath, transformRawPath } from \"./paths.js\";\nimport { getGlobalMatrix, Matrix2D } from \"./matrix.js\";\n\nlet _numbersExp = /(?:(-)?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n\t_doc, _supportsPointer, _win, _body, gsap, _context,\n\t_selectionColor = \"#4e7fff\",\n\t_minimumMovement = 1,\n\t_DEG2RAD = Math.PI / 180,\n\t_getTime = Date.now || (() => new Date().getTime()),\n\t_lastInteraction = 0,\n\t_isPressed = 0,\n\t_emptyFunc = () => false,\n\t_interacted = () => _lastInteraction = _getTime(),\n\t_CTRL, _ALT, _SHIFT, _CMD,\n\t_recentlyAddedAnchor,\n\t_editingAxis = {}, //stores the x/y of the most recently-selected anchor point's x and y axis. We tap into this for snapping horizontally and vertically.\n\t_history = [],\n\t_point = {}, //reuse to minimize memory and maximize performance (mostly for snapping)\n\t_temp = [], //reuse this in places like getNormalizedSVG() to conserve memory\n\t_comma = \",\",\n\t_selectedPaths = [],\n\t_preventDefault = event => {\n\t\tif (event.preventDefault) {\n\t\t\tevent.preventDefault();\n\t\t\tif (event.preventManipulation) {\n\t\t\t\tevent.preventManipulation();  //for some Microsoft browsers\n\t\t\t}\n\t\t}\n\t},\n\t_createElement = type => _doc.createElementNS ? _doc.createElementNS(\"http://www.w3.org/1999/xhtml\", type) : _doc.createElement(type),\n\t_createSVG = (type, container, attributes) => {\n\t\tlet element = _doc.createElementNS(\"http://www.w3.org/2000/svg\", type),\n\t\t\treg = /([a-z])([A-Z])/g,\n\t\t\tp;\n\t\tattributes = attributes || {};\n\t\tattributes.class = attributes.class || \"path-editor\";\n\t\tfor (p in attributes) {\n\t\t\tif (element.style[p] !== undefined) {\n\t\t\t\telement.style[p] = attributes[p];\n\t\t\t} else {\n\t\t\t\telement.setAttributeNS(null, p.replace(reg, \"$1-$2\").toLowerCase(), attributes[p]);\n\t\t\t}\n\t\t}\n\t\tcontainer.appendChild(element);\n\t\treturn element;\n\t},\n\t_identityMatrixObject = {matrix:new Matrix2D()},\n\t_getConsolidatedMatrix = target => ((target.transform && target.transform.baseVal.consolidate()) || _identityMatrixObject).matrix,\n\t_getConcatenatedTransforms = target => {\n\t\tlet m = _getConsolidatedMatrix(target),\n\t\t\towner = target.ownerSVGElement;\n\t\twhile ((target = target.parentNode) && target.ownerSVGElement === owner) {\n\t\t\tm.multiply(_getConsolidatedMatrix(target));\n\t\t}\n\t\treturn \"matrix(\" + m.a + \",\" + m.b + \",\" + m.c + \",\" + m.d + \",\" + m.e + \",\" + m.f + \")\";\n\t},\n\t_addHistory = pathEditor => {\n\t\tlet selectedIndexes = [],\n\t\t\ta = pathEditor._selectedAnchors,\n\t\t\ti;\n\t\tfor (i = 0; i < a.length; i++) {\n\t\t\tselectedIndexes[i] = a[i].i;\n\t\t}\n\t\t_history.unshift({path:pathEditor, d:pathEditor.path.getAttribute(\"d\"), transform:pathEditor.path.getAttribute(\"transform\") || \"\", selectedIndexes:selectedIndexes});\n\t\tif (_history.length > 30) {\n\t\t\t_history.length = 30;\n\t\t}\n\t},\n\t_round = value =>  ~~(value * 1000 + (value < 0 ? -.5 : .5)) / 1000,\n\t_getSquarePathData = size => {\n\t\tsize = _round(size);\n\t\treturn [\"M-\" + size, -size, size, -size, size, size, -size, size + \"z\"].join(_comma);\n\t},\n\t_getCirclePathData = size => {\n\t\tlet circ = 0.552284749831,\n\t\t\trcirc = _round(size * circ);\n\t\tsize = _round(size);\n\t\treturn \"M\" + size + \",0C\" + [size, rcirc, rcirc, size, 0, size,  -rcirc, size, -size, rcirc, -size, 0, -size, -rcirc, -rcirc, -size, 0, -size, rcirc, -size, size, -rcirc, size, 0].join(_comma) + \"z\";\n\t},\n\t_checkDeselect = function(e) {\n\t\tif (!e.target._gsSelection && !_isPressed && _getTime() - _lastInteraction > 100) {\n\t\t\tlet i = _selectedPaths.length;\n\t\t\twhile (--i > -1) {\n\t\t\t\t_selectedPaths[i].deselect();\n\t\t\t}\n\t\t\t_selectedPaths.length = 0;\n\t\t}\n\t},\n\t_tempDiv, _touchEventLookup,\n\t_isMultiTouching = 0,\n\t_addListener = (element, type, func, capture) => {\n\t\tif (element.addEventListener) {\n\t\t\tlet touchType = _touchEventLookup[type];\n\t\t\tcapture = capture || {passive:false};\n\t\t\telement.addEventListener(touchType || type, func, capture);\n\t\t\tif (touchType && type !== touchType && touchType.substr(0, 7) !== \"pointer\") { //some browsers actually support both, so must we. But pointer events cover all.\n\t\t\t\telement.addEventListener(type, func, capture);\n\t\t\t}\n\t\t} else if (element.attachEvent) {\n\t\t\telement.attachEvent(\"on\" + type, func);\n\t\t}\n\t},\n\t_removeListener = (element, type, func) => {\n\t\tif (element.removeEventListener) {\n\t\t\tlet touchType = _touchEventLookup[type];\n\t\t\telement.removeEventListener(touchType || type, func);\n\t\t\tif (touchType && type !== touchType && touchType.substr(0, 7) !== \"pointer\") {\n\t\t\t\telement.removeEventListener(type, func);\n\t\t\t}\n\t\t} else if (element.detachEvent) {\n\t\t\telement.detachEvent(\"on\" + type, func);\n\t\t}\n\t},\n\t_hasTouchID = (list, ID) => {\n\t\tlet i = list.length;\n\t\twhile (--i > -1) {\n\t\t\tif (list[i].identifier === ID) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t},\n\t_onMultiTouchDocumentEnd = e => {\n\t\t_isMultiTouching = (e.touches && _dragCount < e.touches.length);\n\t\t_removeListener(e.target, \"touchend\", _onMultiTouchDocumentEnd);\n\t},\n\t_onMultiTouchDocument = e => {\n\t\t_isMultiTouching = (e.touches && _dragCount < e.touches.length);\n\t\t_addListener(e.target, \"touchend\", _onMultiTouchDocumentEnd);\n\t},\n\t_bind = (func, scope) => e => func.call(scope, e),\n\t_callback = (type, self, param) => {\n\t\tlet callback = self.vars[type];\n\t\tif (callback) {\n\t\t\tcallback.call(self.vars.callbackScope || self, param || self);\n\t\t}\n\t\treturn self;\n\t},\n\t_copyElement,\n\t_resetSelection = () => {\n\t\t_copyElement.style.display = \"block\";\n\t\t_copyElement.select();\n\t\t_copyElement.style.display = \"none\";\n\t},\n\t_coreInitted,\n\t_initCore = (core) => {\n\t\t_doc = document;\n\t\t_win = window;\n\t\t_body = _doc.body;\n\t\tgsap = gsap || core || _win.gsap || console.warn(\"Please gsap.registerPlugin(PathEditor)\");\n\t\t_context = (gsap && gsap.core.context) || function() {};\n\t\t_tempDiv = _createElement(\"div\");\n\t\t_copyElement = _createElement(\"textarea\");\n\t\t_copyElement.style.display = \"none\";\n\t\t_body && _body.appendChild(_copyElement);\n\t\t_touchEventLookup = (function(types) { //we create an object that makes it easy to translate touch event types into their \"pointer\" counterparts if we're in a browser that uses those instead. Like IE10 uses \"MSPointerDown\" instead of \"touchstart\", for example.\n\t\t\tlet standard = types.split(\",\"),\n\t\t\t\tconverted = ((_tempDiv.onpointerdown !== undefined) ? \"pointerdown,pointermove,pointerup,pointercancel\" : (_tempDiv.onmspointerdown !== undefined) ? \"MSPointerDown,MSPointerMove,MSPointerUp,MSPointerCancel\" : types).split(\",\"),\n\t\t\t\tobj = {},\n\t\t\t\ti = 4;\n\t\t\twhile (--i > -1) {\n\t\t\t\tobj[standard[i]] = converted[i];\n\t\t\t\tobj[converted[i]] = standard[i];\n\t\t\t}\n\t\t\treturn obj;\n\t\t}(\"touchstart,touchmove,touchend,touchcancel\"));\n\t\tSVGElement.prototype.getTransformToElement = SVGElement.prototype.getTransformToElement || function(e) { //adds Chrome support\n\t\t\treturn e.getScreenCTM().inverse().multiply(this.getScreenCTM());\n\t\t};\n\t\t_doc.addEventListener(\"keydown\", function(e) {\n\t\t\tlet key = e.keyCode || e.which,\n\t\t\t\tkeyString = e.key || key,\n\t\t\t\ti, state, a, path;\n\t\t\tif (keyString === \"Shift\" || key === 16) {\n\t\t\t\t_SHIFT = true;\n\t\t\t} else if (keyString === \"Control\" || key === 17) {\n\t\t\t\t_CTRL = true;\n\t\t\t} else if (keyString === \"Meta\" || key === 91) {\n\t\t\t\t_CMD = true;\n\t\t\t} else if (keyString === \"Alt\" || key === 18) {\n\t\t\t\t_ALT = true;\n\t\t\t\ti = _selectedPaths.length;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t_selectedPaths[i]._onPressAlt();\n\t\t\t\t}\n\t\t\t} else if ((keyString === \"z\" || key === 90) && (_CTRL || _CMD) && _history.length > 1) { //UNDO\n\t\t\t\t_history.shift();\n\t\t\t\tstate = _history[0];\n\t\t\t\tif (state) {\n\t\t\t\t\tpath = state.path;\n\t\t\t\t\tpath.path.setAttribute(\"d\", state.d);\n\t\t\t\t\tpath.path.setAttribute(\"transform\", state.transform);\n\t\t\t\t\tpath.init();\n\t\t\t\t\ta = path._anchors;\n\t\t\t\t\tfor (i = 0; i < a.length; i++) {\n\t\t\t\t\t\tif (state.selectedIndexes.indexOf(a[i].i) !== -1) {\n\t\t\t\t\t\t\tpath._selectedAnchors.push(a[i]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tpath._updateAnchors();\n\t\t\t\t\tpath.update();\n\t\t\t\t\tif (path.vars.onUndo) {\n\t\t\t\t\t\tpath.vars.onUndo.call(path);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (keyString === \"Delete\" || keyString === \"Backspace\" || key === 8 || key === 46 || key === 63272 || (key === \"d\" && (_CTRL || _CMD))) { //DELETE\n\t\t\t\ti = _selectedPaths.length;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t_selectedPaths[i]._deleteSelectedAnchors();\n\t\t\t\t}\n\t\t\t} else if ((keyString === \"a\" || key === 65) && (_CMD || _CTRL)) { //SELECT ALL\n\t\t\t\ti = _selectedPaths.length;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t_selectedPaths[i].select(true);\n\t\t\t\t}\n\t\t\t}\n\t\t}, true);\n\t\t_doc.addEventListener(\"keyup\", function(e) {\n\t\t\tlet key = e.key || e.keyCode || e.which;\n\t\t\tif (key === \"Shift\" || key === 16) {\n\t\t\t\t_SHIFT = false;\n\t\t\t} else if (key === \"Control\" || key === 17) {\n\t\t\t\t_CTRL = false;\n\t\t\t} else if (key === \"Meta\" || key === 91) {\n\t\t\t\t_CMD = false;\n\t\t\t} else if (key === \"Alt\" || key === 18) {\n\t\t\t\t_ALT = false;\n\t\t\t\tlet i = _selectedPaths.length;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t_selectedPaths[i]._onReleaseAlt();\n\t\t\t\t}\n\t\t\t}\n\t\t}, true);\n\t\t_supportsPointer = !!_win.PointerEvent;\n\t\t_addListener(_doc, \"mouseup\", _checkDeselect);\n\t\t_addListener(_doc, \"touchend\", _checkDeselect);\n\t\t_addListener(_doc, \"touchcancel\", _emptyFunc); //some older Android devices intermittently stop dispatching \"touchmove\" events if we don't listen for \"touchcancel\" on the document. Very strange indeed.\n\t\t_addListener(_win, \"touchmove\", _emptyFunc); //works around Safari bugs that still allow the page to scroll even when we preventDefault() on the touchmove event.\n\t\t_body && _body.addEventListener(\"touchstart\", _emptyFunc); //works around Safari bug: https://gsap.com/forums/topic/21450-draggable-in-iframe-on-mobile-is-buggy/\n\t\t_coreInitted = 1;\n\t},\n\t_onPress = function(e) {\n\t\tlet self = this,\n\t\t\tctm = getGlobalMatrix(self.target.parentNode, true), //previously used self.target.parentNode.getScreenCTM().inverse() but there's a major bug in Firefox that prevents it from working properly when there's an ancestor with a transform applied, so we bootstrapped our own solution that seems to work great across all browsers.\n\t\t\ttouchEventTarget, temp;\n\t\tthis._matrix = this.target.transform.baseVal.getItem(0).matrix;\n\t\tthis._ctm = ctm;\n\t\tif (_touchEventLookup[e.type]) { //note: on iOS, BOTH touchmove and mousemove are dispatched, but the mousemove has pageY and pageX of 0 which would mess up the calculations and needlessly hurt performance.\n\t\t\ttouchEventTarget = (e.type.indexOf(\"touch\") !== -1) ? (e.currentTarget || e.target) : _doc; //pointer-based touches (for Microsoft browsers) don't remain locked to the original target like other browsers, so we must use the document instead. The event type would be \"MSPointerDown\" or \"pointerdown\".\n\t\t\t_addListener(touchEventTarget, \"touchend\", self._onRelease);\n\t\t\t_addListener(touchEventTarget, \"touchmove\", self._onMove);\n\t\t\t_addListener(touchEventTarget, \"touchcancel\", self._onRelease);\n\t\t\t_addListener(_doc, \"touchstart\", _onMultiTouchDocument);\n\t\t\t_addListener(_win, \"touchforcechange\", _preventDefault); //otherwise iOS will scroll when dragging.\n\t\t} else {\n\t\t\ttouchEventTarget = null;\n\t\t\t_addListener(_doc, \"mousemove\", self._onMove); //attach these to the document instead of the box itself so that if the user's mouse moves too quickly (and off of the box), things still work.\n\t\t}\n\t\tif (!_supportsPointer) {\n\t\t\t_addListener(_doc, \"mouseup\", self._onRelease);\n\t\t}\n\t\t_preventDefault(e);\n\t\t_resetSelection(); // when a PathEditor is in an iframe in an environment like codepen, this helps avoid situations where the DELETE key won't actually work because the parent frame is intercepting the event.\n\t\tif (e.changedTouches) { //touch events store the data slightly differently\n\t\t\te = self.touch = e.changedTouches[0];\n\t\t\tself.touchID = e.identifier;\n\t\t} else if (e.pointerId) {\n\t\t\tself.touchID = e.pointerId; //for some Microsoft browsers\n\t\t} else {\n\t\t\tself.touch = self.touchID = null;\n\t\t}\n\t\tself._startPointerY = self.pointerY = e.pageY; //record the starting x and y so that we can calculate the movement from the original in _onMouseMove\n\t\tself._startPointerX = self.pointerX = e.pageX;\n\t\tself._startElementX = self._matrix.e;\n\t\tself._startElementY = self._matrix.f;\n\n\t\tif (this._ctm.a === 1 && this._ctm.b === 0 && this._ctm.c === 0 && this._ctm.d === 1) {\n\t\t\tthis._ctm = null;\n\t\t} else {\n\t\t\ttemp = self._startPointerX * this._ctm.a + self._startPointerY * this._ctm.c + this._ctm.e;\n\t\t\tself._startPointerY = self._startPointerX * this._ctm.b + self._startPointerY * this._ctm.d + this._ctm.f;\n\t\t\tself._startPointerX = temp;\n\t\t}\n\n\t\tself.isPressed = _isPressed = true;\n\t\tself.touchEventTarget = touchEventTarget;\n\t\tif (self.vars.onPress) {\n\t\t\tself.vars.onPress.call(self.vars.callbackScope || self, self.pointerEvent);\n\t\t}\n\t},\n\t_onMove = function(e) {\n\t\tlet self = this,\n\t\t\toriginalEvent = e,\n\t\t\ttouches, i;\n\t\tif (!self._enabled || _isMultiTouching || !self.isPressed || !e) {\n\t\t\treturn;\n\t\t}\n\t\tself.pointerEvent = e;\n\t\ttouches = e.changedTouches;\n\t\tif (touches) { //touch events store the data slightly differently\n\t\t\te = touches[0];\n\t\t\tif (e !== self.touch && e.identifier !== self.touchID) { //Usually changedTouches[0] will be what we're looking for, but in case it's not, look through the rest of the array...(and Android browsers don't reuse the event like iOS)\n\t\t\t\ti = touches.length;\n\t\t\t\twhile (--i > -1 && (e = touches[i]).identifier !== self.touchID) {}\n\t\t\t\tif (i < 0) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (e.pointerId && self.touchID && e.pointerId !== self.touchID) { //for some Microsoft browsers, we must attach the listener to the doc rather than the trigger so that when the finger moves outside the bounds of the trigger, things still work. So if the event we're receiving has a pointerId that doesn't match the touchID, ignore it (for multi-touch)\n\t\t\treturn;\n\t\t}\n\t\t_preventDefault(originalEvent);\n\t\tself.setPointerPosition(e.pageX, e.pageY);\n\t\tif (self.vars.onDrag) {\n\t\t\tself.vars.onDrag.call(self.vars.callbackScope || self, self.pointerEvent);\n\t\t}\n\t},\n\t_onRelease = function(e, force) {\n\t\tlet self = this;\n\t\tif (!self._enabled || !self.isPressed || (e && self.touchID != null && !force && ((e.pointerId && e.pointerId !== self.touchID) || (e.changedTouches && !_hasTouchID(e.changedTouches, self.touchID))))) {  //for some Microsoft browsers, we must attach the listener to the doc rather than the trigger so that when the finger moves outside the bounds of the trigger, things still work. So if the event we're receiving has a pointerId that doesn't match the touchID, ignore it (for multi-touch)\n\t\t\treturn;\n\t\t}\n\t\t_interacted();\n\t\tself.isPressed = _isPressed = false; //TODO: if we want to accommodate multi-touch, we'd need to introduce a counter to track how many touches there are and only toggle this when they're all off.\n\t\tlet originalEvent = e,\n\t\t\twasDragging = self.isDragging,\n\t\t\ttouchEventTarget = self.touchEventTarget,\n\t\t\ttouches, i;\n\t\tif (touchEventTarget) {\n\t\t\t_removeListener(touchEventTarget, \"touchend\", self._onRelease);\n\t\t\t_removeListener(touchEventTarget, \"touchmove\", self._onMove);\n\t\t\t_removeListener(touchEventTarget, \"touchcancel\", self._onRelease);\n\t\t\t_removeListener(_doc, \"touchstart\", _onMultiTouchDocument);\n\t\t} else {\n\t\t\t_removeListener(_doc, \"mousemove\", self._onMove);\n\t\t}\n\t\tif (!_supportsPointer) {\n\t\t\t_removeListener(_doc, \"mouseup\", self._onRelease);\n\t\t\tif (e && e.target) {\n\t\t\t\t_removeListener(e.target, \"mouseup\", self._onRelease);\n\t\t\t}\n\t\t}\n\t\tif (wasDragging) {\n\t\t\tself.isDragging = false;\n\t\t} else if (self.vars.onClick) {\n\t\t\tself.vars.onClick.call(self.vars.callbackScope || self, originalEvent);\n\t\t}\n\t\tif (e) {\n\t\t\ttouches = e.changedTouches;\n\t\t\tif (touches) { //touch events store the data slightly differently\n\t\t\t\te = touches[0];\n\t\t\t\tif (e !== self.touch && e.identifier !== self.touchID) { //Usually changedTouches[0] will be what we're looking for, but in case it's not, look through the rest of the array...(and Android browsers don't reuse the event like iOS)\n\t\t\t\t\ti = touches.length;\n\t\t\t\t\twhile (--i > -1 && (e = touches[i]).identifier !== self.touchID) {}\n\t\t\t\t\tif (i < 0) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tself.pointerEvent = originalEvent;\n\t\t\tself.pointerX = e.pageX;\n\t\t\tself.pointerY = e.pageY;\n\t\t}\n\t\tif (originalEvent && !wasDragging && self.vars.onDragRelease) {\n\t\t\tself.vars.onDragRelease.call(self, self.pointerEvent);\n\n\t\t} else {\n\t\t\tif (originalEvent) {\n\t\t\t\t_preventDefault(originalEvent);\n\t\t\t}\n\t\t\tif (self.vars.onRelease) {\n\t\t\t\tself.vars.onRelease.call(self.vars.callbackScope || self, self.pointerEvent);\n\t\t\t}\n\t\t}\n\t\tif (wasDragging && self.vars.onDragEnd) {\n\t\t\tself.vars.onDragEnd.call(self.vars.callbackScope || self, self.pointerEvent);\n\t\t}\n\t\treturn true;\n\t},\n\t_createSegmentAnchors = (rawPath, j, editor, vars) => {\n\t\tlet segment = rawPath[j],\n\t\t\tl = segment.length - (segment.closed ? 6 : 0),\n\t\t\ta = [],\n\t\t\ti;\n\t\tfor (i = 0; i < l; i+=6) {\n\t\t\ta.push(new Anchor(editor, rawPath, j, i, vars));\n\t\t}\n\t\tsegment.closed && (a[0].isClosedStart = true);\n\t\treturn a;\n\t},\n\t_getLength = (segment, i, i2) => { //i is the starting index, and it'll return the length to the next x/y pair. So if you're looking for the length to handle1, you'd feed in the index of the handle control point x whereas if you're looking for the length to handle2, i would be the x of the anchor.\n\t\tlet x = segment[i2] - segment[i],\n\t\t\ty = segment[i2+1] - segment[i+1];\n\t\treturn Math.sqrt(x * x + y * y);\n\t};\n\n\nclass DraggableSVG {\n\n\tconstructor(target, vars) {\n\t\tthis.target = (typeof(target) === \"string\") ? _doc.querySelectorAll(target)[0] : target;\n\t\tthis.vars = vars || {};\n\t\tthis._onPress = _bind(_onPress, this);\n\t\tthis._onMove = _bind(_onMove, this);\n\t\tthis._onRelease = _bind(_onRelease, this);\n\t\tthis.target.setAttribute(\"transform\", (this.target.getAttribute(\"transform\") || \"\") + \" translate(0,0)\");\n\t\tthis._matrix = _getConsolidatedMatrix(this.target);\n\t\tthis.x = this._matrix.e;\n\t\tthis.y = this._matrix.f;\n\t\tthis.snap = vars.snap;\n\t\tif (!isNaN(vars.maxX) || !isNaN(vars.minX)) {\n\t\t\tthis._bounds = 1;\n\t\t\tthis.maxX = +vars.maxX;\n\t\t\tthis.minX = +vars.minX;\n\t\t} else {\n\t\t\tthis._bounds = 0;\n\t\t}\n\t\tthis.enabled(true);\n\t}\n\n\tsetPointerPosition(pointerX, pointerY) {\n\t\tlet rnd = 1000,\n\t\t\txChange, yChange, x, y, temp;\n\t\tthis.pointerX = pointerX;\n\t\tthis.pointerY = pointerY;\n\t\tif (this._ctm) {\n\t\t\ttemp = pointerX * this._ctm.a + pointerY * this._ctm.c + this._ctm.e;\n\t\t\tpointerY = pointerX * this._ctm.b + pointerY * this._ctm.d + this._ctm.f;\n\t\t\tpointerX = temp;\n\t\t}\n\t\tyChange = (pointerY - this._startPointerY);\n\t\txChange = (pointerX - this._startPointerX);\n\t\tif (yChange < _minimumMovement && yChange > -_minimumMovement) {\n\t\t\tyChange = 0;\n\t\t}\n\t\tif (xChange < _minimumMovement && xChange > -_minimumMovement) {\n\t\t\txChange = 0;\n\t\t}\n\t\tx = (((this._startElementX + xChange) * rnd) | 0) / rnd;\n\t\ty = (((this._startElementY + yChange) * rnd) | 0) / rnd;\n\t\tif (this.snap && !_SHIFT) {\n\t\t\t_point.x = x;\n\t\t\t_point.y = y;\n\t\t\tthis.snap.call(this, _point);\n\t\t\tx = _point.x;\n\t\t\ty = _point.y;\n\t\t}\n\t\tif (this.x !== x || this.y !== y) {\n\t\t\tthis._matrix.f = this.y = y;\n\t\t\tthis._matrix.e = this.x = x;\n\t\t\tif (!this.isDragging && this.isPressed) {\n\t\t\t\tthis.isDragging = true;\n\t\t\t\t_callback(\"onDragStart\", this, this.pointerEvent);\n\t\t\t}\n\t\t}\n\t}\n\n\tenabled(enabled) {\n\t\tif (!arguments.length) {\n\t\t\treturn this._enabled;\n\t\t}\n\t\tlet dragging;\n\t\tthis._enabled = enabled;\n\t\tif (enabled) {\n\t\t\tif (!_supportsPointer) {\n\t\t\t\t_addListener(this.target, \"mousedown\", this._onPress);\n\t\t\t}\n\t\t\t_addListener(this.target, \"touchstart\", this._onPress);\n\t\t\t_addListener(this.target, \"click\", this._onClick, true); //note: used to pass true for capture but it prevented click-to-play-video functionality in Firefox.\n\t\t} else {\n\t\t\tdragging = this.isDragging;\n\t\t\t_removeListener(this.target, \"mousedown\", this._onPress);\n\t\t\t_removeListener(this.target, \"touchstart\", this._onPress);\n\t\t\t_removeListener(_win, \"touchforcechange\", _preventDefault);\n\t\t\t_removeListener(this.target, \"click\", this._onClick);\n\t\t\tif (this.touchEventTarget) {\n\t\t\t\t_removeListener(this.touchEventTarget, \"touchcancel\", this._onRelease);\n\t\t\t\t_removeListener(this.touchEventTarget, \"touchend\", this._onRelease);\n\t\t\t\t_removeListener(this.touchEventTarget, \"touchmove\", this._onMove);\n\t\t\t}\n\t\t\t_removeListener(_doc, \"mouseup\", this._onRelease);\n\t\t\t_removeListener(_doc, \"mousemove\", this._onMove);\n\t\t\tthis.isDragging = this.isPressed = false;\n\t\t\tif (dragging) {\n\t\t\t\t_callback(\"onDragEnd\", this, this.pointerEvent);\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t}\n\n\tendDrag(e) {\n\t\tthis._onRelease(e);\n\t}\n\n}\n\n\n\n\n\nclass Anchor {\n\n\tconstructor(editor, rawPath, j, i, vars) {\n\t\tthis.editor = editor;\n\t\tthis.element = _createSVG(\"path\", editor._selection, {fill:_selectionColor, stroke:_selectionColor, strokeWidth:2, vectorEffect:\"non-scaling-stroke\"});\n\t\tthis.update(rawPath, j, i);\n\t\tthis.element._gsSelection = true;\n\t\tthis.vars = vars || {};\n\t\tthis._draggable = new DraggableSVG(this.element, {callbackScope:this, onDrag:this.onDrag, snap:this.vars.snap, onPress:this.onPress, onRelease:this.onRelease, onClick:this.onClick, onDragEnd:this.onDragEnd});\n\t}\n\n\tonPress() {\n\t\t_callback(\"onPress\", this);\n\t}\n\n\tonClick() {\n\t\t_callback(\"onClick\", this);\n\t}\n\n\tonDrag() {\n\t\tlet s = this.segment;\n\t\tthis.vars.onDrag.call(this.vars.callbackScope || this, this, this._draggable.x - s[this.i], this._draggable.y - s[this.i+1]);\n\t}\n\n\tonDragEnd() {\n\t\t_callback(\"onDragEnd\", this);\n\t}\n\n\tonRelease() {\n\t\t_callback(\"onRelease\", this);\n\t}\n\n\tupdate(rawPath, j, i) {\n\t\tif (rawPath) {\n\t\t\tthis.rawPath = rawPath;\n\t\t}\n\t\tif (arguments.length <= 1) {\n\t\t\tj = this.j;\n\t\t\ti = this.i;\n\t\t} else {\n\t\t\tthis.j = j;\n\t\t\tthis.i = i;\n\t\t}\n\t\tlet prevSmooth = this.smooth,\n\t\t\tsegment = this.rawPath[j],\n\t\t\tpi = i === 0 && segment.closed ? segment.length - 4 : i - 2;\n\t\tthis.segment = segment;\n\t\tthis.smooth = (i > 0 && i < segment.length - 2 && Math.abs(Math.atan2(segment[i+1] - segment[pi+1], segment[i] - segment[pi]) - Math.atan2(segment[i+3] - segment[i+1], segment[i+2] - segment[i])) < 0.09) ? 2 : 0; //0: corner, 1: smooth but not mirrored, 2: smooth and mirrored.\n\t\tif (this.smooth !== prevSmooth) {\n\t\t\tthis.element.setAttribute(\"d\", this.smooth ? this.editor._circleHandle : this.editor._squareHandle);\n\t\t}\n\t\tthis.element.setAttribute(\"transform\", \"translate(\" + segment[i] + \",\" + segment[i+1] + \")\");\n\t}\n}\n\n\n\n\nexport class PathEditor {\n\n\tconstructor(target, vars) {\n\t\tvars = vars || {};\n\t\t_coreInitted || _initCore();\n\t\tthis.vars = vars;\n\t\tthis.path = (typeof(target) === \"string\") ? _doc.querySelectorAll(target)[0] : target;\n\t\tthis._g = _createSVG(\"g\", this.path.ownerSVGElement, {class:\"path-editor-g path-editor\"});\n\t\tthis._selectionHittest = _createSVG(\"path\", this._g, {stroke:\"transparent\", strokeWidth:16, fill:\"none\", vectorEffect:\"non-scaling-stroke\"});\n\t\tthis._selection = vars._selection || _createSVG(\"g\", this._g, {class:\"path-editor-selection path-editor\"});\n\t\tthis._selectionPath = _createSVG(\"path\", this._selection, {stroke:_selectionColor, strokeWidth:2, fill:\"none\", vectorEffect:\"non-scaling-stroke\"});\n\t\tthis._selectedAnchors = [];\n\t\tthis._line1 = _createSVG(\"polyline\", this._selection, {stroke:_selectionColor, strokeWidth:2, vectorEffect:\"non-scaling-stroke\"});\n\t\tthis._line2 = _createSVG(\"polyline\", this._selection, {stroke:_selectionColor, strokeWidth:2, vectorEffect:\"non-scaling-stroke\"});\n\t\tthis._line1.style.pointerEvents = this._line2.style.pointerEvents = this._selectionPath.style.pointerEvents = \"none\";\n\t\tthis._enabled = true;\n\t\tlet ctm = this.path.parentNode.getScreenCTM().inverse(),\n\t\t\tsize = (ctm.a + ctm.d) / 2 * (vars.handleSize || 5);\n\t\tthis._squareHandle = _getSquarePathData(size);\n\t\tthis._circleHandle = _getCirclePathData(size * 1.15);\n\t\tthis._handle1 = _createSVG(\"path\", this._selection, {d:this._squareHandle, fill:_selectionColor, stroke:\"transparent\", strokeWidth:6});\n\t\tthis._handle2 = _createSVG(\"path\", this._selection, {d:this._squareHandle, fill:_selectionColor, stroke:\"transparent\", strokeWidth:6});\n\t\tthis._handle1._draggable = new DraggableSVG(this._handle1, {onDrag:this._onDragHandle1, callbackScope:this, onPress:this._onPressHandle1, onRelease:this._onReleaseHandle, onClick:this._onClickHandle1, snap:vars.handleSnap});\n\t\tthis._handle2._draggable = new DraggableSVG(this._handle2, {onDrag:this._onDragHandle2, callbackScope:this, onPress:this._onPressHandle2, onRelease:this._onReleaseHandle, onClick:this._onClickHandle2, snap:vars.handleSnap});\n\t\tthis._handle1.style.visibility = this._handle2.style.visibility = \"hidden\";\n\t\tlet selectionItems = [this._handle1, this._handle2, this._line1, this._line2, this._selection, this._selectionPath, this._selectionHittest],\n\t\t\ti = selectionItems.length;\n\t\twhile (--i > -1) {\n\t\t\tselectionItems[i]._gsSelection = true; //just a flag we can check in the _checkDeselect() method to detect clicks on things that are selection-related.\n\t\t}\n\t\tif (vars.draggable !== false) {\n\t\t\tthis._draggable = new DraggableSVG(this._selectionHittest, {callbackScope:this, onPress:this.select, onRelease:this._onRelease, onDrag:this._onDragPath, onDragEnd:this._saveState, maxX:this.vars.maxX, minX:this.vars.minX});\n\t\t}\n\t\tthis.init();\n\t\tthis._selection.style.visibility = (vars.selected === false) ? \"hidden\" : \"visible\";\n\t\tif (vars.selected !== false) {\n\t\t\tthis.path._gsSelection = true;\n\t\t\t_selectedPaths.push(this);\n\t\t}\n\t\tthis._saveState();\n\t\tif (!_supportsPointer) {\n\t\t\t_addListener(this._selectionHittest, \"mousedown\", _bind(this._onClickSelectionPath, this));\n\t\t\t_addListener(this._selectionHittest, \"mouseup\", _bind(this._onRelease, this));\n\t\t}\n\t\t_addListener(this._selectionHittest, \"touchstart\", _bind(this._onClickSelectionPath, this));\n\t\t_addListener(this._selectionHittest, \"touchend\", _bind(this._onRelease, this));\n\t\t_context(this);\n\t}\n\n\t_onRelease(e) {\n\t\tlet anchor = this._editingAnchor;\n\t\tif (anchor) {\n\t\t\t_editingAxis.x = anchor.segment[anchor.i];\n\t\t\t_editingAxis.y = anchor.segment[anchor.i+1];\n\t\t}\n\t\t_removeListener(_win, \"touchforcechange\", _preventDefault); //otherwise iOS will scroll when dragging.\n\t\t_callback(\"onRelease\", this, e);\n\t}\n\n\tinit() {\n\t\tlet pathData = this.path.getAttribute(\"d\"),\n\t\t\trawPath = stringToRawPath(pathData),\n\t\t\ttransform = this.path.getAttribute(\"transform\") || \"translate(0,0)\",\n\t\t\tcreateAnchors = (!this._rawPath || rawPath.totalPoints !== this._rawPath.totalPoints || rawPath.length !== this._rawPath.length),\n\t\t\tanchorVars = {callbackScope:this, snap:this.vars.anchorSnap, onDrag:this._onDragAnchor, onPress:this._onPressAnchor, onRelease:this._onRelease, onClick:this._onClickAnchor, onDragEnd:this._onDragEndAnchor, maxX:this.vars.maxX, minX:this.vars.minX},\n\t\t\tl, i;\n\n\t\tif (createAnchors && this._anchors && this._anchors.length) {\n\t\t\tfor (i = 0; i < this._anchors.length; i++) {\n\t\t\t\tthis._anchors[i].element.parentNode.removeChild(this._anchors[i].element);\n\t\t\t\tthis._anchors[i]._draggable.enabled(false);\n\t\t\t}\n\t\t\tthis._selectedAnchors.length = 0;\n\t\t}\n\t\tthis._rawPath = rawPath;\n\t\tif (createAnchors) {\n\t\t\tthis._anchors = _createSegmentAnchors(rawPath, 0, this, anchorVars);\n\t\t\tl = rawPath.length;\n\t\t\tif (l > 1) {\n\t\t\t\tfor (i = 1; i < l; i++) {\n\t\t\t\t\tthis._anchors = this._anchors.concat(_createSegmentAnchors(rawPath, i, this, anchorVars));\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\ti = this._anchors.length;\n\t\t\twhile (--i > -1) {\n\t\t\t\tthis._anchors[i].update(rawPath);\n\t\t\t}\n\t\t}\n\n\t\tthis._selection.appendChild(this._handle1); //for stacking order (handles should always be on top)\n\t\tthis._selection.appendChild(this._handle2);\n\t\t//\t\tthis._selectedAnchors.length = 0;\n\t\tthis._selectionPath.setAttribute(\"d\", pathData);\n\t\tthis._selectionHittest.setAttribute(\"d\", pathData);\n\t\tthis._g.setAttribute(\"transform\", _getConcatenatedTransforms(this.path.parentNode) || \"translate(0,0)\");\n\t\tthis._selection.setAttribute(\"transform\", transform);\n\t\tthis._selectionHittest.setAttribute(\"transform\", transform);\n\t\tthis._updateAnchors();\n\t\treturn this;\n\t}\n\n\t_saveState() {\n\t\t_addHistory(this);\n\t}\n\n\t_onClickSelectionPath(e) {\n\t\tif (this._selection.style.visibility === \"hidden\") {\n\t\t\tthis.select();\n\t\t} else if (_ALT || (e && e.altKey)) {\n\t\t\tlet anchorVars = {callbackScope:this, snap:this.vars.anchorSnap, onDrag:this._onDragAnchor, onPress:this._onPressAnchor, onRelease:this._onRelease, onClick:this._onClickAnchor, onDragEnd:this._onDragEndAnchor, maxX:this.vars.maxX, minX:this.vars.minX},\n\t\t\t\tctm = this._selection.getScreenCTM().inverse(),\n\t\t\t\tnewIndex, i, anchor, x, y, closestData;\n\t\t\tif (this._draggable) {\n\t\t\t\tthis._draggable._onRelease(e); //otherwise, ALT-click/dragging on a path would create a new anchor AND drag the entire path.\n\t\t\t}\n\t\t\tif (ctm) {\n\t\t\t\tx = e.clientX * ctm.a + e.clientY * ctm.c + ctm.e;\n\t\t\t\ty = e.clientX * ctm.b + e.clientY * ctm.d + ctm.f;\n\t\t\t}\n\t\t\t//DEBUG: _createSVG(\"circle\", this._selection, {fill:\"red\", r:5, cx:x, cy:y});\n\t\t\tclosestData = getClosestData(this._rawPath, x, y);\n\t\t\tsubdivideSegment(this._rawPath[closestData.j], closestData.i, closestData.t);\n\t\t\tnewIndex = closestData.i + 6;\n\t\t\tfor (i = 0; i < this._anchors.length; i++) {\n\t\t\t\tif (this._anchors[i].i >= newIndex && this._anchors[i].j === closestData.j) {\n\t\t\t\t\tthis._anchors[i].i += 6;\n\t\t\t\t}\n\t\t\t}\n\t\t\tanchor = new Anchor(this, this._rawPath, closestData.j, newIndex, anchorVars);\n\t\t\tthis._selection.appendChild(this._handle1); //for stacking order (handles should always be on top)\n\t\t\tthis._selection.appendChild(this._handle2);\n\t\t\tanchor._draggable._onPress(e);\n\t\t\t_recentlyAddedAnchor = anchor;\n\t\t\tthis._anchors.push(anchor);\n\t\t\tthis._selectedAnchors.length = 0;\n\t\t\tthis._selectedAnchors.push(anchor);\n\t\t\tthis._updateAnchors();\n\t\t\tthis.update();\n\t\t\tthis._saveState();\n\t\t}\n\t\t_resetSelection();\n\t\t_addListener(_win, \"touchforcechange\", _preventDefault); //otherwise iOS will scroll when dragging.\n\t\t_callback(\"onPress\", this);\n\t}\n\n\t_onClickHandle1() {\n\t\tlet anchor = this._editingAnchor,\n\t\t\ti = anchor.i,\n\t\t\ts = anchor.segment,\n\t\t\tpi = anchor.isClosedStart ? s.length - 4 : i - 2;\n\t\tif (_ALT && Math.abs(s[i] - s[pi]) < 5 && Math.abs(s[i+1] - s[pi+1]) < 5) {\n\t\t\tthis._onClickAnchor(anchor);\n\t\t}\n\t}\n\n\t_onClickHandle2() {\n\t\tlet anchor = this._editingAnchor,\n\t\t\ti = anchor.i,\n\t\t\ts = anchor.segment;\n\t\tif (_ALT && Math.abs(s[i] - s[i+2]) < 5 && Math.abs(s[i+1] - s[i+3]) < 5) {\n\t\t\tthis._onClickAnchor(anchor);\n\t\t}\n\t}\n\n\t_onDragEndAnchor(e) {\n\t\t_recentlyAddedAnchor = null;\n\t\tthis._saveState();\n\t}\n\n\tisSelected() {\n\t\treturn (this._selectedAnchors.length > 0 || this._selection.style.visibility === \"visible\");\n\t}\n\n\tselect(allAnchors) {\n\t\tthis._selection.style.visibility = \"visible\";\n\t\tthis._editingAnchor = null;\n\t\tthis.path._gsSelection = true;\n\t\tif (allAnchors === true) {\n\t\t\tlet i = this._anchors.length;\n\t\t\twhile (--i > -1) {\n\t\t\t\tthis._selectedAnchors[i] = this._anchors[i];\n\t\t\t}\n\t\t}\n\t\tif (_selectedPaths.indexOf(this) === -1) {\n\t\t\t_selectedPaths.push(this);\n\t\t}\n\t\tthis._updateAnchors();\n\t\treturn this;\n\t}\n\n\tdeselect() {\n\t\tthis._selection.style.visibility = \"hidden\";\n\t\tthis._selectedAnchors.length = 0;\n\t\tthis._editingAnchor = null;\n\t\tthis.path._gsSelection = false;\n\t\t_selectedPaths.splice(_selectedPaths.indexOf(this), 1);\n\t\tthis._updateAnchors();\n\t\treturn this;\n\t}\n\n\t_onDragPath(e) {\n\t\tlet transform = this._selectionHittest.getAttribute(\"transform\") || \"translate(0,0)\";\n\t\tthis._selection.setAttribute(\"transform\", transform);\n\t\tthis.path.setAttribute(\"transform\", transform);\n\t}\n\n\t_onPressAnchor(anchor) {\n\t\tif (this._selectedAnchors.indexOf(anchor) === -1) { //if it isn't already selected...\n\t\t\tif (!_SHIFT) {\n\t\t\t\tthis._selectedAnchors.length = 0;\n\t\t\t}\n\t\t\tthis._selectedAnchors.push(anchor);\n\t\t} else if (_SHIFT) {\n\t\t\tthis._selectedAnchors.splice(this._selectedAnchors.indexOf(anchor), 1);\n\t\t\tanchor._draggable.endDrag();\n\t\t}\n\t\t_editingAxis.x = anchor.segment[anchor.i];\n\t\t_editingAxis.y = anchor.segment[anchor.i+1];\n\t\tthis._updateAnchors();\n\t\t_callback(\"onPress\", this);\n\t}\n\n\t_deleteSelectedAnchors() {\n\t\tlet anchors = this._selectedAnchors,\n\t\t\ti = anchors.length,\n\t\t\tanchor, index, j, jIndex;\n\t\twhile (--i > -1) {\n\t\t\tanchor = anchors[i];\n\t\t\tanchor.element.parentNode.removeChild(anchor.element);\n\t\t\tanchor._draggable.enabled(false);\n\t\t\tindex = anchor.i;\n\t\t\tjIndex = anchor.j;\n\t\t\tif (!index) { //first\n\t\t\t\tanchor.segment.splice(index, 6);\n\t\t\t} else if (index < anchor.segment.length - 2) {\n\t\t\t\tanchor.segment.splice(index-2, 6);\n\t\t\t} else { //last\n\t\t\t\tanchor.segment.splice(index-4, 6);\n\t\t\t}\n\t\t\tanchors.splice(i, 1);\n\t\t\tthis._anchors.splice(this._anchors.indexOf(anchor), 1);\n\t\t\tfor (j = 0; j < this._anchors.length; j++) {\n\t\t\t\tif (this._anchors[j].i >= index && this._anchors[j].j === jIndex) {\n\t\t\t\t\tthis._anchors[j].i -= 6;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tthis._updateAnchors();\n\t\tthis.update();\n\t\tthis._saveState();\n\t\tif (this.vars.onDeleteAnchor) {\n\t\t\tthis.vars.onDeleteAnchor.call(this.vars.callbackScope || this);\n\t\t}\n\t}\n\n\t_onClickAnchor(anchor) {\n\t\tlet i = anchor.i,\n\t\t\tsegment = anchor.segment,\n\t\t\tpi = anchor.isClosedStart ? segment.length - 4 : i - 2,\n\t\t\trnd = 1000,\n\t\t\tisEnd = (!i || i >= segment.length - 2),\n\t\t\tangle1, angle2, length1, length2, sin, cos;\n\t\tif (_ALT && _recentlyAddedAnchor !== anchor && this._editingAnchor) {\n\t\t\tanchor.smooth = !anchor.smooth;\n\t\t\tif (isEnd && !anchor.isClosedStart) { //the very ends can't be \"smooth\"\n\t\t\t\tanchor.smooth = false;\n\t\t\t}\n\t\t\tanchor.element.setAttribute(\"d\", anchor.smooth ? this._circleHandle : this._squareHandle);\n\t\t\tif (anchor.smooth && (!isEnd || anchor.isClosedStart)) {\n\t\t\t\tangle1 = Math.atan2(segment[i+1] - segment[pi+1], segment[i] - segment[pi]);\n\t\t\t\tangle2 = Math.atan2(segment[i+3] - segment[i+1], segment[i+2] - segment[i]);\n\t\t\t\tangle1 = (angle1 + angle2) / 2;\n\t\t\t\tlength1 = _getLength(segment, pi, i);\n\t\t\t\tlength2 = _getLength(segment, i, i+2);\n\t\t\t\tif (length1 < 0.2) {\n\t\t\t\t\tlength1 = (_getLength(segment, i, pi-4) / 4);\n\t\t\t\t\tangle1 = angle2 || Math.atan2(segment[i+7] - segment[pi-3], segment[i+6] - segment[pi-4]);\n\t\t\t\t}\n\t\t\t\tif (length2 < 0.2) {\n\t\t\t\t\tlength2 = (_getLength(segment, i, i+6) / 4);\n\t\t\t\t\tangle2 = angle1 || Math.atan2(segment[i+7] - segment[pi-3], segment[i+6] - segment[pi-4]);\n\t\t\t\t}\n\t\t\t\tsin = Math.sin(angle1);\n\t\t\t\tcos = Math.cos(angle1);\n\t\t\t\tif (Math.abs(angle2 - angle1) < Math.PI / 2) {\n\t\t\t\t\tsin = -sin;\n\t\t\t\t\tcos = -cos;\n\t\t\t\t}\n\t\t\t\tsegment[pi] = (((segment[i] + cos * length1) * rnd) | 0) / rnd;\n\t\t\t\tsegment[pi+1] = (((segment[i+1] + sin * length1) * rnd) | 0) / rnd;\n\t\t\t\tsegment[i+2] = (((segment[i] - cos * length2) * rnd) | 0) / rnd;\n\t\t\t\tsegment[i+3] = (((segment[i+1] - sin * length2) * rnd) | 0) / rnd;\n\t\t\t\tthis._updateAnchors();\n\t\t\t\tthis.update();\n\t\t\t\tthis._saveState();\n\t\t\t} else if (!anchor.smooth && (!isEnd || anchor.isClosedStart)) {\n\t\t\t\tif (i || anchor.isClosedStart) {\n\t\t\t\t\tsegment[pi] = segment[i];\n\t\t\t\t\tsegment[pi+1] = segment[i+1];\n\t\t\t\t}\n\t\t\t\tif (i < segment.length - 2) {\n\t\t\t\t\tsegment[i+2] = segment[i];\n\t\t\t\t\tsegment[i+3] = segment[i+1];\n\t\t\t\t}\n\t\t\t\tthis._updateAnchors();\n\t\t\t\tthis.update();\n\t\t\t\tthis._saveState();\n\t\t\t}\n\t\t} else if (!_SHIFT) {\n\t\t\tthis._selectedAnchors.length = 0;\n\t\t\tthis._selectedAnchors.push(anchor);\n\t\t}\n\t\t_recentlyAddedAnchor = null;\n\t\tthis._updateAnchors();\n\t}\n\n\t_updateAnchors() {\n\t\tlet anchor = (this._selectedAnchors.length === 1) ? this._selectedAnchors[0] : null,\n\t\t\tsegment = anchor ? anchor.segment : null,\n\t\t\ti, x, y;\n\t\tthis._editingAnchor = anchor;\n\t\tfor (i = 0; i < this._anchors.length; i++) {\n\t\t\tthis._anchors[i].element.style.fill = (this._selectedAnchors.indexOf(this._anchors[i]) !== -1) ? _selectionColor : \"white\";\n\t\t\t//this._anchors[i].element.setAttribute(\"fill\", (this._selectedAnchors.indexOf(this._anchors[i]) !== -1) ? _selectionColor : \"white\");\n\t\t}\n\t\tif (anchor) {\n\t\t\tthis._handle1.setAttribute(\"d\", anchor.smooth ? this._circleHandle : this._squareHandle);\n\t\t\tthis._handle2.setAttribute(\"d\", anchor.smooth ? this._circleHandle : this._squareHandle);\n\t\t}\n\t\ti = anchor ? anchor.i : 0;\n\t\tif (anchor && (i || anchor.isClosedStart)) {\n\t\t\tx = anchor.isClosedStart ? segment[segment.length-4] : segment[i-2];\n\t\t\ty = anchor.isClosedStart ? segment[segment.length-3] : segment[i-1]; //TODO: if they equal the anchor coordinates, just hide it.\n\t\t\tthis._handle1.style.visibility = this._line1.style.visibility = (!_ALT && x === segment[i] && y === segment[i+1]) ? \"hidden\" : \"visible\";\n\t\t\tthis._handle1.setAttribute(\"transform\", \"translate(\" + x + _comma + y + \")\");\n\t\t\tthis._line1.setAttribute(\"points\",  x + _comma + y + _comma + segment[i] + _comma + segment[i+1]);\n\t\t} else {\n\t\t\tthis._handle1.style.visibility = this._line1.style.visibility = \"hidden\";\n\t\t}\n\t\tif (anchor && i < segment.length - 2) {\n\t\t\tx = segment[i+2];\n\t\t\ty = segment[i+3];\n\t\t\tthis._handle2.style.visibility = this._line2.style.visibility = (!_ALT && x === segment[i] && y === segment[i+1]) ? \"hidden\" : \"visible\";\n\t\t\tthis._handle2.setAttribute(\"transform\", \"translate(\" + x + _comma + y + \")\");\n\t\t\tthis._line2.setAttribute(\"points\",  segment[i] + _comma + segment[i+1] + _comma + x + _comma + y);\n\n\t\t} else {\n\t\t\tthis._handle2.style.visibility = this._line2.style.visibility = \"hidden\";\n\t\t}\n\t}\n\n\t_onPressAlt() {\n\t\tlet anchor = this._editingAnchor;\n\t\tif (anchor) {\n\t\t\tif (anchor.i || anchor.isClosedStart) {\n\t\t\t\tthis._handle1.style.visibility = this._line1.style.visibility = \"visible\";\n\t\t\t}\n\t\t\tif (anchor.i < anchor.segment.length - 2) {\n\t\t\t\tthis._handle2.style.visibility = this._line2.style.visibility = \"visible\";\n\t\t\t}\n\t\t}\n\t}\n\n\t_onReleaseAlt() {\n\t\tlet anchor = this._editingAnchor,\n\t\t\ts, i, pi;\n\t\tif (anchor) {\n\t\t\ts = anchor.segment;\n\t\t\ti = anchor.i;\n\t\t\tpi = anchor.isClosedStart ? s.length - 4 : i - 2;\n\t\t\tif (s[i] === s[pi] && s[i+1] === s[pi+1]) {\n\t\t\t\tthis._handle1.style.visibility = this._line1.style.visibility = \"hidden\";\n\t\t\t}\n\t\t\tif (s[i] === s[i+2] && s[i+1] === s[i+3]) {\n\t\t\t\tthis._handle2.style.visibility = this._line2.style.visibility = \"hidden\";\n\t\t\t}\n\t\t}\n\t}\n\n\t_onPressHandle1() {\n\t\tif (this._editingAnchor.smooth) {\n\t\t\tthis._oppositeHandleLength = _getLength(this._editingAnchor.segment, this._editingAnchor.i, this._editingAnchor.i+2);\n\t\t}\n\t\t_callback(\"onPress\", this);\n\t}\n\n\t_onPressHandle2() {\n\t\tif (this._editingAnchor.smooth) {\n\t\t\tthis._oppositeHandleLength = _getLength(this._editingAnchor.segment, this._editingAnchor.isClosedStart ? this._editingAnchor.segment.length - 4 : this._editingAnchor.i-2, this._editingAnchor.i);\n\t\t}\n\t\t_callback(\"onPress\", this);\n\t}\n\n\t_onReleaseHandle(e) {\n\t\tthis._onRelease(e);\n\t\tthis._saveState();\n\t}\n\n\t_onDragHandle1() {\n\t\tlet anchor = this._editingAnchor,\n\t\t\ts = anchor.segment,\n\t\t\ti = anchor.i,\n\t\t\tpi = anchor.isClosedStart ? s.length-4 : i-2,\n\t\t\trnd = 1000,\n\t\t\tx = this._handle1._draggable.x,\n\t\t\ty = this._handle1._draggable.y,\n\t\t\tangle;\n\t\ts[pi] = x = ((x * rnd) | 0) / rnd;\n\t\ts[pi+1] = y = ((y * rnd) | 0) / rnd;\n\t\tif (anchor.smooth) {\n\t\t\tif (_ALT) {\n\t\t\t\tanchor.smooth = false;\n\t\t\t\tanchor.element.setAttribute(\"d\", this._squareHandle);\n\t\t\t\tthis._handle1.setAttribute(\"d\", this._squareHandle);\n\t\t\t\tthis._handle2.setAttribute(\"d\", this._squareHandle);\n\t\t\t} else {\n\t\t\t\tangle = Math.atan2(s[i+1] - y, s[i] - x);\n\t\t\t\tx = this._oppositeHandleLength * Math.cos(angle);\n\t\t\t\ty = this._oppositeHandleLength * Math.sin(angle);\n\t\t\t\ts[i+2] = (((s[i] + x) * rnd) | 0) / rnd;\n\t\t\t\ts[i+3] = (((s[i+1] + y) * rnd) | 0) / rnd;\n\t\t\t}\n\n\t\t}\n\t\tthis.update();\n\t}\n\n\t_onDragHandle2() {\n\t\tlet anchor = this._editingAnchor,\n\t\t\ts = anchor.segment,\n\t\t\ti = anchor.i,\n\t\t\tpi = anchor.isClosedStart ? s.length-4 : i-2,\n\t\t\trnd = 1000,\n\t\t\tx = this._handle2._draggable.x,\n\t\t\ty = this._handle2._draggable.y,\n\t\t\tangle;\n\t\ts[i+2] = x = ((x * rnd) | 0) / rnd;\n\t\ts[i+3] = y = ((y * rnd) | 0) / rnd;\n\t\tif (anchor.smooth) {\n\t\t\tif (_ALT) {\n\t\t\t\tanchor.smooth = false;\n\t\t\t\tanchor.element.setAttribute(\"d\", this._squareHandle);\n\t\t\t\tthis._handle1.setAttribute(\"d\", this._squareHandle);\n\t\t\t\tthis._handle2.setAttribute(\"d\", this._squareHandle);\n\t\t\t} else {\n\t\t\t\tangle = Math.atan2(s[i+1] - y, s[i] - x);\n\t\t\t\tx = this._oppositeHandleLength * Math.cos(angle);\n\t\t\t\ty = this._oppositeHandleLength * Math.sin(angle);\n\t\t\t\ts[pi] = (((s[i] + x) * rnd) | 0) / rnd;\n\t\t\t\ts[pi+1] = (((s[i+1] + y) * rnd) | 0) / rnd;\n\t\t\t}\n\n\t\t}\n\t\tthis.update();\n\t}\n\n\t_onDragAnchor(anchor, changeX, changeY) {\n\t\tlet anchors = this._selectedAnchors,\n\t\t\tl = anchors.length,\n\t\t\trnd = 1000,\n\t\t\ti, j, s, a, pi;\n\t\tfor (j = 0; j < l; j++) {\n\t\t\ta = anchors[j];\n\t\t\ti = a.i;\n\t\t\ts = a.segment;\n\t\t\tif (i) {\n\t\t\t\ts[i-2] = (((s[i-2] + changeX) * rnd) | 0) / rnd;\n\t\t\t\ts[i-1] = (((s[i-1] + changeY) * rnd) | 0) / rnd;\n\t\t\t} else if (a.isClosedStart) {\n\t\t\t\tpi = s.length-2;\n\t\t\t\ts[pi] = _round(s[pi] + changeX);\n\t\t\t\ts[pi+1] = _round(s[pi+1] + changeY);\n\t\t\t\ts[pi-2] = _round(s[pi-2] + changeX);\n\t\t\t\ts[pi-1] = _round(s[pi-1] + changeY);\n\t\t\t}\n\t\t\ts[i] = (((s[i] + changeX) * rnd) | 0) / rnd;\n\t\t\ts[i+1] = (((s[i+1] + changeY) * rnd) | 0) / rnd;\n\t\t\tif (i < s.length - 2) {\n\t\t\t\ts[i+2] = (((s[i+2] + changeX) * rnd) | 0) / rnd;\n\t\t\t\ts[i+3] = (((s[i+3] + changeY) * rnd) | 0) / rnd;\n\t\t\t}\n\t\t\tif (a !== anchor) {\n\t\t\t\ta.element.setAttribute(\"transform\", \"translate(\" + (s[i]) + _comma + (s[i+1]) + \")\");\n\t\t\t}\n\t\t}\n\t\tthis.update();\n\t}\n\n\tenabled(enabled) {\n\t\tif (!arguments.length) {\n\t\t\treturn this._enabled;\n\t\t}\n\t\tlet i = this._anchors.length;\n\t\twhile (--i > -1) {\n\t\t\tthis._anchors[i]._draggable.enabled(enabled);\n\t\t}\n\t\tthis._enabled = enabled;\n\t\tthis._handle1._draggable.enabled(enabled);\n\t\tthis._handle2._draggable.enabled(enabled);\n\t\tif (this._draggable) {\n\t\t\tthis._draggable.enabled(enabled);\n\t\t}\n\t\tif (!enabled) {\n\t\t\tthis.deselect();\n\t\t\tthis._selectionHittest.parentNode && this._selectionHittest.parentNode.removeChild(this._selectionHittest);\n\t\t\tthis._selection.parentNode && this._selection.parentNode.removeChild(this._selection);\n\t\t} else if (!this._selection.parentNode) {\n\t\t\tthis.path.ownerSVGElement.appendChild(this._selectionHittest);\n\t\t\tthis.path.ownerSVGElement.appendChild(this._selection);\n\t\t\tthis.init();\n\t\t\tthis._saveState();\n\t\t}\n\t\tthis._updateAnchors();\n\t\treturn this.update();\n\t}\n\n\tupdate(readPath) {\n\t\tlet d = \"\",\n\t\t\tanchor = this._editingAnchor,\n\t\t\ti, s, x, y, pi;\n\t\tif (readPath) {\n\t\t\tthis.init();\n\t\t}\n\t\tif (anchor) {\n\t\t\ti = anchor.i;\n\t\t\ts = anchor.segment;\n\t\t\tif (i || anchor.isClosedStart) {\n\t\t\t\tpi = anchor.isClosedStart ? s.length-4 : i-2;\n\t\t\t\tx = s[pi];\n\t\t\t\ty = s[pi+1];\n\t\t\t\tthis._handle1.setAttribute(\"transform\", \"translate(\" + x + _comma + y + \")\");\n\t\t\t\tthis._line1.setAttribute(\"points\", x + _comma + y + _comma + s[i] + _comma + s[i+1]);\n\t\t\t}\n\t\t\tif (i < s.length - 2) {\n\t\t\t\tx = s[i+2];\n\t\t\t\ty = s[i+3];\n\t\t\t\tthis._handle2.setAttribute(\"transform\", \"translate(\" + (x) + _comma + (y) + \")\");\n\t\t\t\tthis._line2.setAttribute(\"points\", s[i] + _comma + s[i+1] + _comma + x + _comma + y);\n\t\t\t}\n\t\t}\n\n\t\tif (readPath) {\n\t\t\td = this.path.getAttribute(\"d\");\n\t\t} else {\n\t\t\tfor (i = 0; i < this._rawPath.length; i++) {\n\t\t\t\ts = this._rawPath[i];\n\t\t\t\tif (s.length > 7) {\n\t\t\t\t\td += \"M\" + s[0] + _comma + s[1] + \"C\" + s.slice(2).join(_comma);\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.path.setAttribute(\"d\", d);\n\t\t\tthis._selectionPath.setAttribute(\"d\", d);\n\t\t\tthis._selectionHittest.setAttribute(\"d\", d);\n\t\t}\n\n\t\tif (this.vars.onUpdate && this._enabled) {\n\t\t\t_callback(\"onUpdate\", this, d);\n\t\t}\n\t\treturn this;\n\t}\n\n\tgetRawPath(applyTransforms, offsetX, offsetY) {\n\t\tif (applyTransforms) {\n\t\t\tlet m = _getConsolidatedMatrix(this.path);\n\t\t\treturn transformRawPath(copyRawPath(this._rawPath), 1, 0, 0, 1, m.e + (offsetX || 0), m.f + (offsetY || 0));\n\t\t}\n\t\treturn this._rawPath;\n\t}\n\n\tgetString(applyTransforms, offsetX, offsetY) {\n\t\tif (applyTransforms) {\n\t\t\tlet m = _getConsolidatedMatrix(this.path);\n\t\t\treturn rawPathToString(transformRawPath(copyRawPath(this._rawPath), 1, 0, 0, 1, m.e + (offsetX || 0), m.f + (offsetY || 0)));\n\t\t}\n\t\treturn this.path.getAttribute(\"d\");\n\t}\n\n\tgetNormalizedSVG(height, originY, shorten, onEaseError) {\n\t\tlet s = this._rawPath[0],\n\t\t\ttx = s[0] * -1,\n\t\t\tty = (originY === 0) ? 0 : -(originY || s[1]),\n\t\t\tl = s.length,\n\t\t\tsx = 1 / (s[l-2] + tx),\n\t\t\tsy = -height || (s[l-1] + ty),\n\t\t\trnd = 1000,\n\t\t\tpoints, i, x1, y1, x2, y2;\n\t\t_temp.length = 0;\n\t\tif (sy) { //typically y ends at 1 (so that the end values are reached)\n\t\t\tsy = 1 / sy;\n\t\t} else { //in case the ease returns to its beginning value, scale everything proportionally\n\t\t\tsy = -sx;\n\t\t}\n\t\tsx *= rnd;\n\t\tsy *= rnd;\n\t\tfor (i = 0; i < l; i += 2) {\n\t\t\t_temp[i] = (((s[i] + tx) * sx) | 0) / rnd;\n\t\t\t_temp[i+1] = (((s[i+1] + ty) * sy) | 0) / rnd;\n\t\t}\n\n\t\tif (onEaseError) {\n\t\t\tpoints = [];\n\t\t\tl = _temp.length;\n\t\t\tfor (i = 2; i < l; i+=6) {\n\t\t\t\tx1 = _temp[i-2];\n\t\t\t\ty1 = _temp[i-1];\n\t\t\t\tx2 = _temp[i+4];\n\t\t\t\ty2 = _temp[i+5];\n\t\t\t\tpoints.push(x1, y1, x2, y2);\n\t\t\t\tbezierToPoints(x1, y1, _temp[i], _temp[i+1], _temp[i+2], _temp[i+3], x2, y2, 0.001, points, points.length - 2);\n\t\t\t}\n\t\t\tx1 = points[0];\n\t\t\tl = points.length;\n\t\t\tfor (i = 2; i < l; i+=2) {\n\t\t\t\tx2 = points[i];\n\t\t\t\tif (x2 < x1 || x2 > 1 || x2 < 0) {\n\t\t\t\t\tonEaseError();\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tx1 = x2;\n\t\t\t}\n\t\t}\n\n\t\tif (shorten && l === 8 && _temp[0] === 0 && _temp[1] === 0 && _temp[l-2] === 1 && _temp[l-1] === 1) {\n\t\t\treturn _temp.slice(2, 6).join(\",\");\n\t\t}\n\t\t_temp[2] = \"C\" + _temp[2];\n\t\treturn \"M\" + _temp.join(\",\");\n\t}\n\n\tkill() {\n\t\tthis.enabled(false);\n\t\tthis._g.parentNode && this._g.parentNode.removeChild(this._g);\n\t}\n\n\trevert() {\n\t\tthis.kill();\n\t}\n\n}\n\n\n\n\n\nPathEditor.simplifyPoints = simplifyPoints;\nPathEditor.pointsToSegment = pointsToSegment;\nPathEditor.simplifySVG = (data, vars) => {  //takes a <path> element or data string and simplifies it according to whatever tolerance you set (default:1, the bigger the number the more variance there can be). vars: {tolerance:1, cornerThreshold:degrees, curved:true}\n\tlet element, points, i, x1, x2, y1, y2, bezier, precision, tolerance, l, cornerThreshold;\n\tvars = vars || {};\n\ttolerance = vars.tolerance || 1;\n\tprecision = vars.precision || (1 / tolerance);\n\tcornerThreshold = (vars.cornerThreshold === undefined ? 18 : +vars.cornerThreshold) * _DEG2RAD;\n\tif (typeof(data) !== \"string\") { //element\n\t\telement = data;\n\t\tdata = element.getAttribute(\"d\");\n\t}\n\tif (data.charAt(0) === \"#\" || data.charAt(0) === \".\") { //selector text\n\t\telement = _doc.querySelector(data);\n\t\tif (element) {\n\t\t\tdata = element.getAttribute(\"d\");\n\t\t}\n\t}\n\tpoints = (vars.curved === false && !/[achqstvz]/ig.test(data)) ? data.match(_numbersExp) : stringToRawPath(data)[0];\n\tif (vars.curved !== false) {\n\t\tbezier = points;\n\t\tpoints = [];\n\t\tl = bezier.length;\n\t\tfor (i = 2; i < l; i+=6) {\n\t\t\tx1 = +bezier[i-2];\n\t\t\ty1 = +bezier[i-1];\n\t\t\tx2 = +bezier[i+4];\n\t\t\ty2 = +bezier[i+5];\n\t\t\tpoints.push(_round(x1), _round(y1), _round(x2), _round(y2));\n\t\t\tbezierToPoints(x1, y1, +bezier[i], +bezier[i+1], +bezier[i+2], +bezier[i+3], x2, y2, 1 / (precision * 200000), points, points.length - 2);\n\t\t}\n\t\tpoints = pointsToSegment(simplifyPoints(points, tolerance), vars.curviness, cornerThreshold);\n\t\tpoints[2] = \"C\" + points[2];\n\t} else {\n\t\tpoints = simplifyPoints(points, tolerance);\n\t}\n\tdata = \"M\" + points.join(\",\");\n\tif (element) {\n\t\telement.setAttribute(\"d\", data);\n\t}\n\treturn data;\n};\n\nPathEditor.create = (target, vars) => new PathEditor(target, vars);\n\nPathEditor.editingAxis = _editingAxis;\n\nPathEditor.getSnapFunction = (vars) => { //{gridSize, radius, x, y, width, height}\n\tlet r = vars.radius || 2,\n\t\tbig = 1e20,\n\t\tminX = (vars.x || vars.x === 0) ? vars.x : vars.width ? 0 : -big,\n\t\tminY = (vars.y || vars.y === 0) ? vars.y : vars.height ? 0 : -big,\n\t\tmaxX = minX + (vars.width || big*big),\n\t\tmaxY = minY + (vars.height || big*big),\n\t\tcontainX = (vars.containX !== false),\n\t\tcontainY = (vars.containY !== false),\n\t\taxis = vars.axis,\n\t\tgrid = vars.gridSize;\n\tr *= r;\n\treturn p => {\n\t\tlet x = p.x,\n\t\t\ty = p.y,\n\t\t\tgridX, gridY, dx, dy;\n\t\tif ((containX && x < minX) || (dx = x - minX) * dx < r) {\n\t\t\tx = minX;\n\t\t} else if ((containX && x > maxX) || (dx = maxX - x) * dx < r) {\n\t\t\tx = maxX;\n\t\t}\n\t\tif ((containY && y < minY) || (dy = y - minY) * dy < r) {\n\t\t\ty = minY;\n\t\t} else if ((containY && y > maxY) || (dy = maxY - y) * dy < r) {\n\t\t\ty = maxY;\n\t\t}\n\t\tif (axis) {\n\t\t\tdx = x - axis.x;\n\t\t\tdy = y - axis.y;\n\t\t\tif (dx * dx < r) {\n\t\t\t\tx = axis.x;\n\t\t\t}\n\t\t\tif (dy * dy < r) {\n\t\t\t\ty = axis.y;\n\t\t\t}\n\t\t}\n\t\tif (grid) {\n\t\t\tgridX = minX + Math.round((x - minX) / grid) * grid; //closest grid slot on x-axis\n\t\t\tdx = gridX - x;\n\t\t\tgridY = minY + Math.round((y - minY) / grid) * grid; //closest grid slot on y-axis\n\t\t\tdy = gridY - y;\n\t\t\tif (dx * dx + dy * dy < r) {\n\t\t\t\tx = gridX;\n\t\t\t\ty = gridY;\n\t\t\t}\n\t\t}\n\t\tp.x = x;\n\t\tp.y = y;\n\t};\n};\n\nPathEditor.version = \"3.13.0\";\n\nPathEditor.register = _initCore;\n\nexport { PathEditor as default };", "/*!\n * MotionPathHelper 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport PathEditor from \"./utils/PathEditor.js\";\n\nlet gsap, _win, _doc, _docEl, _body, MotionPathPlugin,  _arrayToRawPath, _rawPathToString, _context,\n\t_bonusValidated = 1, //<name>MotionPathHelper</name>\n\t_selectorExp = /(^[#\\.][a-z]|[a-y][a-z])/i,\n\t_isString = value => typeof(value) === \"string\",\n\t_createElement = (type, ns) => {\n\t\tlet e = _doc.createElementNS ? _doc.createElementNS((ns || \"http://www.w3.org/1999/xhtml\").replace(/^https/, \"http\"), type) : _doc.createElement(type); //some servers swap in https for http in the namespace which can break things, making \"style\" inaccessible.\n\t\treturn e.style ? e : _doc.createElement(type); //some environments won't allow access to the element's style when created with a namespace in which case we default to the standard createElement() to work around the issue. Also note that when GSAP is embedded directly inside an SVG file, createElement() won't allow access to the style object in Firefox (see https://gsap.com/forums/topic/20215-problem-using-tweenmax-in-standalone-self-containing-svg-file-err-cannot-set-property-csstext-of-undefined/).\n\t},\n\t_getPositionOnPage = target => {\n\t\tlet bounds = target.getBoundingClientRect(),\n\t\t\twindowOffsetY = _docEl.clientTop - (_win.pageYOffset || _docEl.scrollTop || _body.scrollTop || 0),\n\t\t\twindowOffsetX = _docEl.clientLeft - (_win.pageXOffset || _docEl.scrollLeft || _body.scrollLeft || 0);\n\t\treturn {left:bounds.left + windowOffsetX, top:bounds.top + windowOffsetY, right: bounds.right + windowOffsetX, bottom: bounds.bottom + windowOffsetY};\n\t},\n\t_getInitialPath = (x, y) => {\n\t\tlet coordinates = [0,31,8,58,24,75,40,90,69,100,100,100],\n\t\t\ti;\n\t\tfor (i = 0; i < coordinates.length; i+=2) {\n\t\t\tcoordinates[i] += x;\n\t\t\tcoordinates[i+1] += y;\n\t\t}\n\t\treturn \"M\" + x + \",\" + y + \"C\" + coordinates.join(\",\");\n\t},\n\t_getGlobalTime = animation => {\n\t\tlet time = animation.totalTime();\n\t\twhile (animation) {\n\t\t\ttime = animation.startTime() + time / (animation.timeScale() || 1);\n\t\t\tanimation = animation.parent;\n\t\t}\n\t\treturn time;\n\t},\n\t_copyElement,\n\t_initCopyToClipboard = () => {\n\t\t_copyElement = _createElement(\"textarea\");\n\t\t_copyElement.style.display = \"none\";\n\t\t_body.appendChild(_copyElement);\n\t},\n\t_parsePath = (path, target, vars) => (_isString(path) && _selectorExp.test(path)) ? _doc.querySelector(path) : Array.isArray(path) ? _rawPathToString(_arrayToRawPath([{x:gsap.getProperty(target, \"x\"), y:gsap.getProperty(target, \"y\")}, ...path], vars)) : (_isString(path) || path && (path.tagName + \"\").toLowerCase() === \"path\") ? path : 0,\n\t_addCopyToClipboard = (target, getter, onComplete) => {\n\t\ttarget.addEventListener('click', e => {\n\t\t\tif (e.target._gsHelper) {\n\t\t\t\tlet c = getter(e.target);\n\t\t\t\t_copyElement.value = c;\n\t\t\t\tif (c && _copyElement.select) {\n\t\t\t\t\tconsole.log(c);\n\t\t\t\t\t_copyElement.style.display = \"block\";\n\t\t\t\t\t_copyElement.select();\n\t\t\t\t\ttry {\n\t\t\t\t\t\t_doc.execCommand('copy');\n\t\t\t\t\t\t_copyElement.blur();\n\t\t\t\t\t\tonComplete && onComplete(target);\n\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\tconsole.warn(\"Copy didn't work; this browser doesn't permit that.\");\n\t\t\t\t\t}\n\t\t\t\t\t_copyElement.style.display = \"none\";\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t},\n\t_identityMatrixObject = {matrix:{a:1, b:0, c:0, d:1, e:0, f:0}},\n\t_getConsolidatedMatrix = target => (target.transform.baseVal.consolidate() || _identityMatrixObject).matrix,\n\t_findMotionPathTween = target => {\n\t\tlet tweens = gsap.getTweensOf(target),\n\t\t\ti = 0;\n\t\tfor (; i < tweens.length; i++) {\n\t\t\tif (tweens[i].vars.motionPath) {\n\t\t\t\treturn tweens[i];\n\t\t\t} else if (tweens[i].timeline) {\n\t\t\t\ttweens.push(...tweens[i].timeline.getChildren());\n\t\t\t}\n\t\t}\n\t},\n\t_initCore = (core, required) => {\n\t\tlet message = \"Please gsap.registerPlugin(MotionPathPlugin)\";\n\t\t_win = window;\n\t\tgsap = gsap || core || _win.gsap || console.warn(message);\n\t\tgsap && PathEditor.register(gsap);\n\t\t_doc = document;\n\t\t_body = _doc.body;\n\t\t_docEl = _doc.documentElement;\n\t\tif (gsap) {\n\t\t\tMotionPathPlugin = gsap.plugins.motionPath;\n\t\t\tMotionPathHelper.PathEditor = PathEditor;\n\t\t\t_context = gsap.core.context || function() {};\n\t\t}\n\t\tif (!MotionPathPlugin) {\n\t\t\t(required === true) && console.warn(message);\n\t\t} else {\n\t\t\t_initCopyToClipboard();\n\t\t\t_arrayToRawPath = MotionPathPlugin.arrayToRawPath;\n\t\t\t_rawPathToString = MotionPathPlugin.rawPathToString;\n\t\t}\n\t};\n\nexport class MotionPathHelper {\n\n\tconstructor(targetOrTween, vars = {}) {\n\t\tif (!MotionPathPlugin) {\n\t\t\t_initCore(vars.gsap, 1);\n\t\t}\n\t\tlet copyButton = _createElement(\"div\"),\n\t\t\tself = this,\n\t\t\toffset = {x:0, y:0},\n\t\t\ttarget, path, isSVG, startX, startY, position, svg, animation, svgNamespace, temp, matrix, refreshPath, animationToScrub, createdSVG;\n\t\tif (targetOrTween instanceof gsap.core.Tween) {\n\t\t\tanimation = targetOrTween;\n\t\t\ttarget = animation.targets()[0];\n\t\t} else {\n\t\t\ttarget = gsap.utils.toArray(targetOrTween)[0];\n\t\t\tanimation = _findMotionPathTween(target);\n\t\t}\n\t\tpath = _parsePath(vars.path, target, vars);\n\t\tthis.offset = offset;\n\t\tposition = _getPositionOnPage(target);\n\t\tstartX = parseFloat(gsap.getProperty(target, \"x\", \"px\"));\n\t\tstartY = parseFloat(gsap.getProperty(target, \"y\", \"px\"));\n\t\tisSVG = (target.getCTM && target.tagName.toLowerCase() !== \"svg\");\n\t\tif (animation && !path) {\n\t\t\tpath = _parsePath(animation.vars.motionPath.path || animation.vars.motionPath, target, animation.vars.motionPath);\n\t\t}\n\t\tcopyButton.setAttribute(\"class\", \"copy-motion-path\");\n\t\tcopyButton.style.cssText = \"border-radius:8px; background-color:rgba(85, 85, 85, 0.7); color:#fff; cursor:pointer; padding:6px 12px; font-family:Signika Negative, Arial, sans-serif; position:fixed; left:50%; transform:translate(-50%, 0); font-size:19px; bottom:10px\";\n\t\tcopyButton.innerText = \"COPY MOTION PATH\";\n\t\tcopyButton._gsHelper = self;\n\t\t(gsap.utils.toArray(vars.container)[0] || _body).appendChild(copyButton);\n\t\t_addCopyToClipboard(copyButton, () => self.getString(), () => gsap.fromTo(copyButton, {backgroundColor:\"white\"}, {duration:0.5, backgroundColor:\"rgba(85, 85, 85, 0.6)\"}));\n\t\tsvg = path && path.ownerSVGElement;\n\t\tif (!svg) {\n\t\t\tsvgNamespace = (isSVG && target.ownerSVGElement && target.ownerSVGElement.getAttribute(\"xmlns\")) || \"http://www.w3.org/2000/svg\";\n\t\t\tif (isSVG) {\n\t\t\t\tsvg = target.ownerSVGElement;\n\t\t\t\ttemp = target.getBBox();\n\t\t\t\tmatrix = _getConsolidatedMatrix(target);\n\t\t\t\tstartX = matrix.e;\n\t\t\t\tstartY = matrix.f;\n\t\t\t\toffset.x = temp.x;\n\t\t\t\toffset.y = temp.y;\n\t\t\t} else {\n\t\t\t\tsvg = _createElement(\"svg\", svgNamespace);\n\t\t\t\tcreatedSVG = true;\n\t\t\t\t_body.appendChild(svg);\n\t\t\t\tsvg.setAttribute(\"viewBox\", \"0 0 100 100\");\n\t\t\t\tsvg.setAttribute(\"class\", \"motion-path-helper\");\n\t\t\t\tsvg.style.cssText = \"overflow:visible; background-color: transparent; position:absolute; z-index:5000; width:100px; height:100px; top:\" + (position.top - startY) + \"px; left:\" + (position.left - startX) + \"px;\";\n\t\t\t}\n\t\t\ttemp = _isString(path) && !_selectorExp.test(path) ? path : _getInitialPath(startX, startY);\n\t\t\tpath = _createElement(\"path\", svgNamespace);\n\t\t\tpath.setAttribute(\"d\", temp);\n\t\t\tpath.setAttribute(\"vector-effect\", \"non-scaling-stroke\");\n\t\t\tpath.style.cssText = \"fill:transparent; stroke-width:\" + (vars.pathWidth || 3) + \"; stroke:\" + (vars.pathColor || \"#555\") + \"; opacity:\" + (vars.pathOpacity || 0.6);\n\t\t\tsvg.appendChild(path);\n\t\t} else {\n\t\t\tvars.pathColor && gsap.set(path, {stroke: vars.pathColor});\n\t\t\tvars.pathWidth && gsap.set(path, {strokeWidth: vars.pathWidth});\n\t\t\tvars.pathOpacity && gsap.set(path, {opacity: vars.pathOpacity});\n\t\t}\n\n\t\tif (offset.x || offset.y) {\n\t\t\tgsap.set(path, {x:offset.x, y:offset.y});\n\t\t}\n\n\t\tif (!(\"selected\" in vars)) {\n\t\t\tvars.selected = true;\n\t\t}\n\t\tif (!(\"anchorSnap\" in vars)) {\n\t\t\tvars.anchorSnap = p => {\n\t\t\t\tif (p.x * p.x + p.y * p.y < 16) {\n\t\t\t\t\tp.x = p.y = 0;\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\n\t\tanimationToScrub = animation && animation.parent && animation.parent.data === \"nested\" ? animation.parent.parent : animation;\n\n\t\tvars.onPress = () => {\n\t\t\tanimationToScrub.pause(0);\n\t\t};\n\n\t\trefreshPath = () => {\n\t\t\t//let m = _getConsolidatedMatrix(path);\n\t\t\t//animation.vars.motionPath.offsetX = m.e - offset.x;\n\t\t\t//animation.vars.motionPath.offsetY = m.f - offset.y;\n\t\t\tanimation.invalidate();\n\t\t\tanimationToScrub.restart();\n\t\t};\n\t\tvars.onRelease = vars.onDeleteAnchor = refreshPath;\n\n\t\tthis.editor = PathEditor.create(path, vars);\n\t\tif (vars.center) {\n\t\t\tgsap.set(target, {transformOrigin:\"50% 50%\", xPercent:-50, yPercent:-50});\n\t\t}\n\t\tif (animation) {\n\t\t\tif (animation.vars.motionPath.path) {\n\t\t\t\tanimation.vars.motionPath.path = path;\n\t\t\t} else {\n\t\t\t\tanimation.vars.motionPath = {path:path};\n\t\t\t}\n\t\t\tif (animationToScrub.parent !== gsap.globalTimeline) {\n\t\t\t\tgsap.globalTimeline.add(animationToScrub, _getGlobalTime(animationToScrub) - animationToScrub.delay());\n\t\t\t}\n\t\t\tanimationToScrub.repeat(-1).repeatDelay(1);\n\n\t\t} else {\n\t\t\tanimation = animationToScrub = gsap.to(target, {\n\t\t\t\tmotionPath: {\n\t\t\t\t\tpath: path,\n\t\t\t\t\tstart: vars.start || 0,\n\t\t\t\t\tend: (\"end\" in vars) ? vars.end : 1,\n\t\t\t\t\tautoRotate: (\"autoRotate\" in vars) ? vars.autoRotate : false,\n\t\t\t\t\talign: path,\n\t\t\t\t\talignOrigin: vars.alignOrigin\n\t\t\t\t},\n\t\t\t\tduration: vars.duration || 5,\n\t\t\t\tease: vars.ease || \"power1.inOut\",\n\t\t\t\trepeat:-1,\n\t\t\t\trepeatDelay:1,\n\t\t\t\tpaused:!vars.path\n\t\t\t});\n\t\t}\n\t\tthis.animation = animation;\n\n\t\t_context(this);\n\t\tthis.kill = this.revert = () => {\n\t\t\tthis.editor.kill();\n\t\t\tcopyButton.parentNode && copyButton.parentNode.removeChild(copyButton);\n\t\t\tcreatedSVG && svg.parentNode && svg.parentNode.removeChild(svg);\n\t\t\tanimationToScrub && animationToScrub.revert();\n\t\t}\n\t}\n\n\tgetString() {\n\t\treturn this.editor.getString(true, -this.offset.x, -this.offset.y);\n\t}\n\n}\n\nMotionPathHelper.register = _initCore;\nMotionPathHelper.create = (target, vars) => new MotionPathHelper(target, vars);\nMotionPathHelper.editPath = (path, vars) => PathEditor.create(path, vars);\nMotionPathHelper.version = \"3.13.0\";\n\nexport { MotionPathHelper as default };"], "names": ["_round", "value", "Math", "round", "_copyMetaData", "source", "copy", "totalLength", "samples", "slice", "lookup", "<PERSON><PERSON><PERSON><PERSON>", "resolution", "totalPoints", "_bestDistance", "_svgPathExp", "_scientific", "_DEG2RAD", "PI", "_sin", "sin", "_cos", "cos", "_abs", "abs", "_sqrt", "sqrt", "_largeNum", "copyRawPath", "rawPath", "a", "i", "length", "transformRawPath", "b", "c", "d", "tx", "ty", "segment", "l", "x", "y", "j", "_dirty", "arcToSegment", "lastX", "lastY", "rx", "ry", "angle", "largeArcFlag", "sweepFlag", "angleRad", "cosAngle", "sinAngle", "TWOPI", "dx2", "dy2", "x1", "y1", "x1_sq", "y1_sq", "radiiCheck", "rx_sq", "ry_sq", "sq", "coef", "cx1", "cy1", "cx", "cy", "ux", "uy", "vx", "vy", "temp", "angleStart", "acos", "angleExtent", "isNaN", "segments", "ceil", "angleIncrement", "controlLength", "ma", "mb", "mc", "md", "push", "stringToRawPath", "line", "sx", "sy", "ex", "ey", "difX", "difY", "command", "isRelative", "startX", "startY", "beziers", "prevCommand", "flag1", "flag2", "replace", "m", "n", "match", "path", "relativeX", "relativeY", "elements", "points", "errorMessage", "console", "log", "toUpperCase", "closed", "substr", "char<PERSON>t", "pop", "bezierToPoints", "x2", "y2", "x3", "y3", "x4", "y4", "threshold", "index", "x12", "y12", "x23", "y23", "x34", "y34", "x123", "y123", "x234", "y234", "x1234", "y1234", "dx", "dy", "d2", "d3", "splice", "pointsToSegment", "curviness", "prevX", "prevY", "dx1", "dy1", "r1", "r2", "tl", "mx1", "mx2", "mxm", "my1", "my2", "mym", "nextX", "nextY", "unshift", "simplifyPoints", "tolerance", "result", "last", "parseFloat", "simplifyStep", "first", "simplified", "t", "maxSqDist", "firstX", "firstY", "getClosestProgressOnBezier", "iterations", "px", "py", "start", "end", "slices", "x0", "y0", "inv", "inc", "best", "max", "min", "_setDoc", "element", "doc", "ownerDocument", "_transformProp", "style", "_transformOriginProp", "parentNode", "_win", "window", "_identityMatrix", "Matrix2D", "_doc<PERSON>lement", "_doc", "documentElement", "_body", "body", "_gEl", "createElementNS", "transform", "d1", "createElement", "root", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "setAttribute", "_hasOffsetBug", "offsetParent", "<PERSON><PERSON><PERSON><PERSON>", "_svgOwner", "ownerSVGElement", "tagName", "toLowerCase", "_createSibling", "svg", "ns", "getAttribute", "type", "css", "e", "_svgContainer", "_divContainer", "cssText", "_placeSiblings", "adjustGOffset", "container", "cs", "isRootSVG", "siblings", "_svgTemps", "_divTemps", "parent", "appendToEl", "shadowRoot", "_getCTM", "getCTM", "removeProperty", "clone", "f", "getBBox", "baseVal", "numberOfItems", "_consolidate", "multiply", "getItem", "matrix", "getComputedStyle", "offsetLeft", "offsetTop", "position", "scrollLeft", "scrollTop", "top", "left", "_setMatrix", "inverse", "this", "determinant", "a2", "b2", "c2", "e2", "f2", "equals", "apply", "point", "decoratee", "getGlobalMatrix", "includeScrollInFixed", "zeroScales", "_forceNonZeroScale", "cache", "_gsap", "uncache", "get", "scaleX", "scaleY", "renderTransform", "temps", "b1", "getBoundingClientRect", "b3", "isFixed", "_isFixed", "nodeType", "_getDocScrollLeft", "pageXOffset", "_getDocScrollTop", "pageYOffset", "_emptyFunc", "_preventDefault", "event", "preventDefault", "preventManipulation", "_createElement", "_createSVG", "attributes", "p", "reg", "undefined", "setAttributeNS", "_getConsolidatedMatrix", "target", "consolidate", "_identityMatrixObject", "_checkDeselect", "_gsSelection", "_isPressed", "_getTime", "_lastInteraction", "_selectedPaths", "deselect", "_addListener", "func", "capture", "addEventListener", "touchType", "_touchEventLookup", "passive", "attachEvent", "_removeListener", "removeEventListener", "detachEvent", "_onMultiTouchDocumentEnd", "_isMultiTouching", "touches", "_dragCount", "_onMultiTouchDocument", "_bind", "scope", "call", "_callback", "self", "param", "callback", "vars", "callbackScope", "_resetSelection", "_copyElement", "display", "select", "_initCore", "core", "document", "gsap", "warn", "_context", "context", "_tempDiv", "types", "standard", "split", "converted", "onpointerdown", "onmspointerdown", "obj", "SVGElement", "prototype", "getTransformToElement", "getScreenCTM", "state", "key", "keyCode", "which", "keyString", "_SHIFT", "_CTRL", "_CMD", "_ALT", "_onPressAlt", "_history", "shift", "init", "_anchors", "selectedIndexes", "indexOf", "_selectedAnchors", "_updateAnchors", "update", "onUndo", "_deleteSelectedAnchors", "_onReleaseAlt", "_supportsPointer", "PointerEvent", "_coreInitted", "_onPress", "touchEventTarget", "ctm", "_matrix", "_ctm", "currentTarget", "_onRelease", "_onMove", "changedTouches", "touch", "touchID", "identifier", "pointerId", "_startPointerY", "pointerY", "pageY", "_startPointerX", "pointerX", "pageX", "_startElementX", "_startElementY", "isPressed", "onPress", "pointerEvent", "originalEvent", "_enabled", "setPointerPosition", "onDrag", "force", "_hasTouchID", "list", "ID", "_interacted", "wasDragging", "isDragging", "onClick", "onDragRelease", "onRelease", "onDragEnd", "_createSegmentAnchors", "editor", "<PERSON><PERSON>", "isClosedStart", "_get<PERSON>ength", "i2", "_recentlyAddedAnchor", "_numbersExp", "_selectionColor", "Date", "now", "getTime", "_editingAxis", "_point", "_temp", "_comma", "DraggableSVG", "xChange", "yChange", "snap", "enabled", "arguments", "_onClick", "dragging", "endDrag", "querySelectorAll", "maxX", "minX", "_bounds", "s", "_draggable", "prevSmooth", "smooth", "pi", "atan2", "_<PERSON><PERSON><PERSON>le", "_square<PERSON><PERSON>le", "_selection", "fill", "stroke", "strokeWidth", "vectorEffect", "PathEditor", "anchor", "_editingAnchor", "pathData", "createAnchors", "_rawPath", "anchorVars", "anchorSnap", "_onDragAnchor", "_onPressAnchor", "_onClickAnchor", "_onDragEndAnchor", "concat", "_handle1", "_handle2", "_selectionPath", "_selectionHittest", "_g", "_getConcatenatedTransforms", "owner", "_saveState", "_addHistory", "pathEditor", "_onClickSelectionPath", "visibility", "altKey", "newIndex", "closestData", "clientX", "clientY", "getClosestData", "closest", "bestDistance", "subdivideSegment", "ax", "ay", "cp1x", "cp1y", "cp2x", "cp2y", "x1a", "y1a", "x2a", "y2a", "_onClickHandle1", "_onClickHandle2", "isSelected", "allAnchors", "_onDragPath", "jIndex", "anchors", "onDeleteAnchor", "angle1", "angle2", "length1", "length2", "rnd", "isEnd", "_line1", "_line2", "_onPressHandle1", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_onPressHandle2", "_onReleaseHandle", "_onDragHandle1", "_onDragHandle2", "changeX", "changeY", "readPath", "join", "onUpdate", "getRawPath", "applyTransforms", "offsetX", "offsetY", "getString", "rawPathToString", "_isNumber", "sl", "getNormalizedSVG", "height", "originY", "shorten", "onEaseError", "kill", "revert", "pointerEvents", "size", "handleSize", "_getSquarePathData", "_getCirclePathData", "rcirc", "handleSnap", "selectionItems", "draggable", "selected", "simplifySVG", "data", "bezier", "precision", "cornerThreshold", "querySelector", "curved", "test", "create", "editingAxis", "getSnapFunction", "r", "radius", "big", "width", "minY", "maxY", "containX", "containY", "axis", "grid", "gridSize", "gridX", "gridY", "version", "register", "_isString", "_parsePath", "_selectorExp", "Array", "isArray", "_rawPathToString", "_arrayToRawPath", "getProperty", "required", "message", "_docEl", "MotionPathPlugin", "plugins", "motionPath", "MotionPathHelper", "_initCopyToClipboard", "arrayToRawPath", "_bonusValidated", "offset", "targetOrTween", "isSVG", "animation", "svgNamespace", "refreshPath", "animationToScrub", "createdSVG", "copyButton", "Tween", "targets", "utils", "toArray", "_findMotionPathTween", "tweens", "getTweensOf", "timeline", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_getPositionOnPage", "bounds", "windowOffsetY", "clientTop", "windowOffsetX", "clientLeft", "right", "bottom", "innerText", "_gs<PERSON><PERSON><PERSON>", "_addCopyToClipboard", "getter", "onComplete", "execCommand", "blur", "err", "fromTo", "backgroundColor", "duration", "pathColor", "set", "pathWidth", "pathOpacity", "opacity", "_getInitialPath", "coordinates", "pause", "invalidate", "restart", "center", "transform<PERSON><PERSON>in", "xPercent", "yPercent", "globalTimeline", "add", "_getGlobalTime", "time", "totalTime", "startTime", "timeScale", "delay", "repeat", "repeatDelay", "to", "autoRotate", "align", "align<PERSON><PERSON>in", "ease", "paused", "_this", "editPath"], "mappings": ";;;;;;;;;6MA6BU,SAATA,EAASC,UAAUC,KAAKC,MAFT,IAEeF,GAFf,KAEwD,EA8BvD,SAAhBG,EAAiBC,EAAQC,UACxBA,EAAKC,YAAcF,EAAOE,YACtBF,EAAOG,SACVF,EAAKE,QAAUH,EAAOG,QAAQC,MAAM,GACpCH,EAAKI,OAASL,EAAOK,OAAOD,MAAM,GAClCH,EAAKK,UAAYN,EAAOM,UACxBL,EAAKM,WAAaP,EAAOO,YACfP,EAAOQ,cACjBP,EAAKO,YAAcR,EAAOQ,aAEpBP,MAaRQ,EAxEGC,EAAc,mDAEjBC,EAAc,gCAEdC,EAAWf,KAAKgB,GAAK,IAErBC,EAAOjB,KAAKkB,IACZC,EAAOnB,KAAKoB,IACZC,EAAOrB,KAAKsB,IACZC,EAAQvB,KAAKwB,KAEbC,EAAY,IAuFN,SAASC,YAAYC,WACvBC,EAAI,GACPC,EAAI,EACEA,EAAIF,EAAQG,OAAQD,IAC1BD,EAAEC,GAAK3B,EAAcyB,EAAQE,GAAIF,EAAQE,GAAGtB,MAAM,WAE5CL,EAAcyB,EAASC,GAkcxB,SAASG,iBAAiBJ,EAASC,EAAGI,EAAGC,EAAGC,EAAGC,EAAIC,WAExDC,EAASC,EAAGT,EAAGU,EAAGC,EADfC,EAAId,EAAQG,QAEF,IAALW,OAERH,GADAD,EAAUV,EAAQc,IACNX,OACPD,EAAI,EAAGA,EAAIS,EAAGT,GAAK,EACvBU,EAAIF,EAAQR,GACZW,EAAIH,EAAQR,EAAE,GACdQ,EAAQR,GAAKU,EAAIX,EAAIY,EAAIP,EAAIE,EAC7BE,EAAQR,EAAE,GAAKU,EAAIP,EAAIQ,EAAIN,EAAIE,SAGjCT,EAAQe,OAAS,EACVf,EAMR,SAASgB,aAAaC,EAAOC,EAAOC,EAAIC,EAAIC,EAAOC,EAAcC,EAAWX,EAAGC,MAC1EI,IAAUL,GAAKM,IAAUL,GAG7BM,EAAKzB,EAAKyB,GACVC,EAAK1B,EAAK0B,OACNI,EAAYH,EAAQ,IAAOjC,EAC9BqC,EAAWjC,EAAKgC,GAChBE,EAAWpC,EAAKkC,GAChBnC,EAAKhB,KAAKgB,GACVsC,EAAa,EAALtC,EACRuC,GAAOX,EAAQL,GAAK,EACpBiB,GAAOX,EAAQL,GAAK,EACpBiB,EAAML,EAAWG,EAAMF,EAAWG,EAClCE,GAAOL,EAAWE,EAAMH,EAAWI,EACnCG,EAAQF,EAAKA,EACbG,EAAQF,EAAKA,EACbG,EAAaF,GAASb,EAAKA,GAAMc,GAASb,EAAKA,GAC/B,EAAbc,IACHf,EAAKvB,EAAMsC,GAAcf,EACzBC,EAAKxB,EAAMsC,GAAcd,OAEtBe,EAAQhB,EAAKA,EAChBiB,EAAQhB,EAAKA,EACbiB,GAAOF,EAAQC,EAAUD,EAAQF,EAAUG,EAAQJ,IAAYG,EAAQF,EAAUG,EAAQJ,GACtFK,EAAK,IACRA,EAAK,OAEFC,GAAShB,IAAiBC,GAAc,EAAI,GAAK3B,EAAMyC,GAC1DE,EAAepB,EAAKY,EAAMX,EAApBkB,EACNE,GAAgBpB,EAAKU,EAAMX,EAArBmB,EAGNG,EAAYhB,EAAWc,EAAMb,EAAWc,GAFjCvB,EAAQL,GAAK,EAGpB8B,EAAYhB,EAAWa,EAAMd,EAAWe,GAFjCtB,EAAQL,GAAK,EAGpB8B,GAAMb,EAAKS,GAAOpB,EAClByB,GAAMb,EAAKS,GAAOpB,EAClByB,IAAOf,EAAKS,GAAOpB,EACnB2B,IAAOf,EAAKS,GAAOpB,EACnB2B,EAAOJ,EAAKA,EAAKC,EAAKA,EACtBI,GAAeJ,EAAK,GAAM,EAAI,GAAKvE,KAAK4E,KAAKN,EAAK/C,EAAMmD,IACxDG,GAAgBP,EAAKG,EAAKF,EAAKC,EAAK,GAAM,EAAI,GAAKxE,KAAK4E,MAAMN,EAAKE,EAAKD,EAAKE,GAAMlD,EAAMmD,GAAQF,EAAKA,EAAKC,EAAKA,KACjHK,MAAMD,KAAiBA,EAAc7D,IAChCkC,GAA2B,EAAd2B,EACjBA,GAAevB,EACLJ,GAAa2B,EAAc,IACrCA,GAAevB,GAEhBqB,GAAcrB,EACduB,GAAevB,MASdzB,EARGkD,EAAW/E,KAAKgF,KAAK3D,EAAKwD,IAAgBvB,EAAQ,IACrD3B,EAAU,GACVsD,EAAiBJ,EAAcE,EAC/BG,EAAgB,EAAI,EAAIjE,EAAKgE,EAAiB,IAAM,EAAI9D,EAAK8D,EAAiB,IAC9EE,EAAK/B,EAAWN,EAChBsC,EAAK/B,EAAWP,EAChBuC,EAAKhC,GAAYN,EACjBuC,EAAKlC,EAAWL,MAEZlB,EAAI,EAAGA,EAAIkD,EAAUlD,IAEzB4B,EAAKtC,EADL6B,EAAQ2B,EAAa9C,EAAIoD,GAEzBvB,EAAKzC,EAAK+B,GACVsB,EAAKnD,EAAK6B,GAASiC,GACnBV,EAAKtD,EAAK+B,GACVrB,EAAQ4D,KAAK9B,EAAKyB,EAAgBxB,EAAIA,EAAKwB,EAAgBzB,EAAIa,EAAKY,EAAgBX,EAAIA,EAAKW,EAAgBZ,EAAIA,EAAIC,OAGjH1C,EAAI,EAAGA,EAAIF,EAAQG,OAAQD,GAAG,EAClC4B,EAAK9B,EAAQE,GACb6B,EAAK/B,EAAQE,EAAE,GACfF,EAAQE,GAAK4B,EAAK0B,EAAKzB,EAAK2B,EAAKjB,EACjCzC,EAAQE,EAAE,GAAK4B,EAAK2B,EAAK1B,EAAK4B,EAAKjB,SAEpC1C,EAAQE,EAAE,GAAKU,EACfZ,EAAQE,EAAE,GAAKW,EACRb,GAID,SAAS6D,gBAAgBtD,GAUvB,SAAPuD,GAAgBC,EAAIC,EAAIC,EAAIC,GAC3BC,GAAQF,EAAKF,GAAM,EACnBK,GAAQF,EAAKF,GAAM,EACnBtD,EAAQkD,KAAKG,EAAKI,EAAMH,EAAKI,EAAMH,EAAKE,EAAMD,EAAKE,EAAMH,EAAIC,OAJ9DhE,EAAGY,EAAGF,EAAGC,EAAGwD,EAASC,EAAY5D,EAAS6D,EAAQC,EAAQL,EAAMC,EAAMK,EAASC,EAAaC,EAAOC,EARhG3E,GAAKM,EAAI,IAAIsE,QAAQ1F,EAAa,SAAA2F,OAAWC,GAAKD,SAAWC,EAAI,OAAe,KAALA,EAAe,EAAIA,IAAMC,MAAM9F,IAAgB,GAC7H+F,EAAO,GACPC,EAAY,EACZC,EAAY,EAEZC,EAAWnF,EAAEE,OACbkF,EAAS,EACTC,EAAe,0BAA4B/E,MAOvCA,IAAM4C,MAAMlD,EAAE,KAAOkD,MAAMlD,EAAE,WACjCsF,QAAQC,IAAIF,GACLL,MAEH/E,EAAI,EAAGA,EAAIkF,EAAUlF,OACzBwE,EAAcL,EACVlB,MAAMlD,EAAEC,IAEXoE,GADAD,EAAUpE,EAAEC,GAAGuF,iBACWxF,EAAEC,GAE5BA,IAEDU,GAAKX,EAAEC,EAAI,GACXW,GAAKZ,EAAEC,EAAI,GACPoE,IACH1D,GAAKsE,EACLrE,GAAKsE,GAEDjF,IACJqE,EAAS3D,EACT4D,EAAS3D,GAIM,MAAZwD,EACC3D,IACCA,EAAQP,OAAS,IACpB8E,EAAK9E,OAELkF,GAAU3E,EAAQP,QAGpB+E,EAAYX,EAAS3D,EACrBuE,EAAYX,EAAS3D,EACrBH,EAAU,CAACE,EAAGC,GACdoE,EAAKrB,KAAKlD,GACVR,GAAK,EACLmE,EAAU,SAGJ,GAAgB,MAAZA,EAILC,IACJY,EAAYC,EAAY,IAHxBzE,EADIA,GACM,CAAC,EAAG,IAMPkD,KAAKhD,EAAGC,EAAGqE,EAAuB,EAAXjF,EAAEC,EAAI,GAAQiF,EAAuB,EAAXlF,EAAEC,EAAI,GAASgF,GAAwB,EAAXjF,EAAEC,EAAI,GAAUiF,GAAwB,EAAXlF,EAAEC,EAAI,IACxHA,GAAK,OAGC,GAAgB,MAAZmE,EACVF,EAAOe,EACPd,EAAOe,EACa,MAAhBT,GAAuC,MAAhBA,IAC1BP,GAAQe,EAAYxE,EAAQA,EAAQP,OAAS,GAC7CiE,GAAQe,EAAYzE,EAAQA,EAAQP,OAAS,IAEzCmE,IACJY,EAAYC,EAAY,GAEzBzE,EAAQkD,KAAKO,EAAMC,EAAMxD,EAAGC,EAAIqE,GAAwB,EAAXjF,EAAEC,EAAI,GAAUiF,GAAwB,EAAXlF,EAAEC,EAAI,IAChFA,GAAK,OAGC,GAAgB,MAAZmE,EACVF,EAAOe,EA7EI,EAAI,GA6EKtE,EAAIsE,GACxBd,EAAOe,EA9EI,EAAI,GA8EKtE,EAAIsE,GACnBb,IACJY,EAAYC,EAAY,GAEzBD,GAAwB,EAAXjF,EAAEC,EAAI,GACnBiF,GAAwB,EAAXlF,EAAEC,EAAI,GACnBQ,EAAQkD,KAAKO,EAAMC,EAAMc,EApFd,EAAI,GAoFuBtE,EAAIsE,GAAwBC,EApFvD,EAAI,GAoFgEtE,EAAIsE,GAAwBD,EAAWC,GACtHjF,GAAK,OAGC,GAAgB,MAAZmE,EACVF,EAAOe,EAAYxE,EAAQA,EAAQP,OAAS,GAC5CiE,EAAOe,EAAYzE,EAAQA,EAAQP,OAAS,GAC5CO,EAAQkD,KAAKsB,EAAYf,EAAMgB,EAAYf,EAAMxD,EA3FtC,EAAI,GA2FwCsE,EAAmB,IAAPf,EAAcvD,GAAgBC,EA3FtF,EAAI,GA2FwFsE,EAAmB,IAAPf,EAAcvD,GAAiBqE,EAAYtE,EAAKuE,EAAYtE,GAC/KX,GAAK,OAGC,GAAgB,MAAZmE,EACVP,GAAKoB,EAAWC,EAAYD,EAAYtE,EAAIuE,GAC5CjF,GAAK,OAGC,GAAgB,MAAZmE,EAEVP,GAAKoB,EAAWC,EAAWD,EAAYC,EAAYvE,GAAK0D,EAAaa,EAAYD,EAAY,IAC7FhF,GAAK,OAGC,GAAgB,MAAZmE,GAA+B,MAAZA,EACb,MAAZA,IACHzD,EAAI2D,EACJ1D,EAAI2D,EACJ9D,EAAQgF,QAAS,IAEF,MAAZrB,GAAyC,GAAtB3E,EAAKwF,EAAYtE,IAAkC,GAAtBlB,EAAKyF,EAAYtE,MACpEiD,GAAKoB,EAAWC,EAAWvE,EAAGC,GACd,MAAZwD,IACHnE,GAAK,IAGPgF,EAAYtE,EACZuE,EAAYtE,OAGN,GAAgB,MAAZwD,EAAiB,IAC3BM,EAAQ1E,EAAEC,EAAE,GACZ0E,EAAQ3E,EAAEC,EAAE,GACZiE,EAAOlE,EAAEC,EAAE,GACXkE,EAAOnE,EAAEC,EAAE,GACXY,EAAI,EACe,EAAf6D,EAAMxE,SACLwE,EAAMxE,OAAS,GAClBiE,EAAOD,EACPA,EAAOS,EACP9D,MAEAsD,EAAOQ,EACPT,EAAOQ,EAAMgB,OAAO,GACpB7E,GAAG,GAEJ8D,EAAQD,EAAMiB,OAAO,GACrBjB,EAAQA,EAAMiB,OAAO,IAEtBnB,EAAUzD,aAAakE,EAAWC,GAAYlF,EAAEC,EAAE,IAAKD,EAAEC,EAAE,IAAKD,EAAEC,EAAE,IAAKyE,GAAQC,GAAQN,EAAaY,EAAY,GAAU,EAALf,GAASG,EAAaa,EAAY,GAAU,EAALf,GAC9JlE,GAAKY,EACD2D,MACE3D,EAAI,EAAGA,EAAI2D,EAAQtE,OAAQW,IAC/BJ,EAAQkD,KAAKa,EAAQ3D,IAGvBoE,EAAYxE,EAAQA,EAAQP,OAAO,GACnCgF,EAAYzE,EAAQA,EAAQP,OAAO,QAGnCoF,QAAQC,IAAIF,UAGdpF,EAAIQ,EAAQP,QACJ,GACP8E,EAAKY,MACL3F,EAAI,GACMQ,EAAQ,KAAOA,EAAQR,EAAE,IAAMQ,EAAQ,KAAOA,EAAQR,EAAE,KAClEQ,EAAQgF,QAAS,GAElBT,EAAKjG,YAAcqG,EAASnF,EACrB+E,EAID,SAASa,eAAehE,EAAIC,EAAIgE,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAWhB,EAAQiB,OAiBhFnG,EAhBGoG,GAAOzE,EAAKiE,GAAM,EACrBS,GAAOzE,EAAKiE,GAAM,EAClBS,GAAOV,EAAKE,GAAM,EAClBS,GAAOV,EAAKE,GAAM,EAClBS,GAAOV,EAAKE,GAAM,EAClBS,GAAOV,EAAKE,GAAM,EAClBS,GAAQN,EAAME,GAAO,EACrBK,GAAQN,EAAME,GAAO,EACrBK,GAAQN,EAAME,GAAO,EACrBK,GAAQN,EAAME,GAAO,EACrBK,GAASJ,EAAOE,GAAQ,EACxBG,GAASJ,EAAOE,GAAQ,EACxBG,EAAKhB,EAAKrE,EACVsF,EAAKhB,EAAKrE,EACVsF,EAAK3H,GAAMqG,EAAKI,GAAMiB,GAAMpB,EAAKI,GAAMe,GACvCG,EAAK5H,GAAMuG,EAAKE,GAAMiB,GAAMlB,EAAKE,GAAMe,UAEnC9B,IACJA,EAAS,CAACvD,EAAIC,EAAIoE,EAAIC,GACtBE,EAAQ,GAETjB,EAAOkC,OAAOjB,GAASjB,EAAOlF,OAAS,EAAG,EAAG8G,EAAOC,GACxBb,GAAac,EAAKA,EAAKC,EAAKA,IAAnDC,EAAKC,IAAOD,EAAKC,KACrBnH,EAASkF,EAAOlF,OAChB2F,eAAehE,EAAIC,EAAIwE,EAAKC,EAAKK,EAAMC,EAAMG,EAAOC,EAAOb,EAAWhB,EAAQiB,GAC9ER,eAAemB,EAAOC,EAAOH,EAAMC,EAAML,EAAKC,EAAKT,EAAIC,EAAIC,EAAWhB,EAAQiB,EAAQ,GAAKjB,EAAOlF,OAASA,KAErGkF,EAsCD,SAASmC,gBAAgBnC,EAAQoC,GAEvC/H,EAAK2F,EAAO,GAAKA,EAAO,IAAM,MAAQ3F,EAAK2F,EAAO,GAAKA,EAAO,IAAM,OAASA,EAASA,EAAOzG,MAAM,QAUlG8I,EAAOC,EAAOzH,EAAG0H,EAAKC,EAAKC,EAAIC,EAAQC,EAAIC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EATjE3H,EAAI0E,EAAOlF,OAAO,EACrBS,GAAKyE,EAAO,GACZxE,GAAKwE,EAAO,GACZkD,GAASlD,EAAO,GAChBmD,GAASnD,EAAO,GAChB3E,EAAU,CAACE,EAAGC,EAAGD,EAAGC,GACpBe,EAAM2G,EAAQ3H,EACdiB,EAAM2G,EAAQ3H,EACd6E,EAASrH,KAAKsB,IAAI0F,EAAO1E,GAAKC,GAAK,MAASvC,KAAKsB,IAAI0F,EAAO1E,EAAE,GAAKE,GAAK,SAErE6E,IACHL,EAAOzB,KAAK2E,EAAOC,GACnBD,EAAQ3H,EACR4H,EAAQ3H,EACRD,EAAIyE,EAAO1E,EAAE,GACbE,EAAIwE,EAAO1E,EAAE,GACb0E,EAAOoD,QAAQ7H,EAAGC,GAClBF,GAAG,GAEJ8G,EAAaA,GAA2B,IAAdA,GAAoBA,EAAY,EACrDvH,EAAI,EAAGA,EAAIS,EAAGT,GAAG,EACrBwH,EAAQ9G,EACR+G,EAAQ9G,EACRD,EAAI2H,EACJ1H,EAAI2H,EACJD,GAASlD,EAAOnF,EAAE,GAClBsI,GAASnD,EAAOnF,EAAE,GACdU,IAAM2H,GAAS1H,IAAM2H,IAGzBZ,EAAMhG,EACNiG,EAAMhG,EACND,EAAM2G,EAAQ3H,EACdiB,EAAM2G,EAAQ3H,EAIdmH,IAHAF,EAAKlI,EAAMgI,EAAMA,EAAMC,EAAMA,KAC7BE,EAAKnI,EAAMgC,EAAMA,EAAMC,EAAMA,KAEX4F,EAAY,IADxB7H,EAAMvB,SAACuD,EAAMmG,EAAKH,EAAME,EAAO,YAAKjG,EAAMkG,EAAKF,EAAMC,EAAO,IAIlEK,EAAMvH,IAFNqH,EAAMrH,GAAKA,EAAI8G,IAAUI,EAAKE,EAAKF,EAAK,OACxCI,EAAMtH,GAAK2H,EAAQ3H,IAAMmH,EAAKC,EAAKD,EAAK,IACdE,IAAc,EAALH,GAAUA,EAAKC,GAAO,IAAO,GAAM,IAGtEO,EAAMzH,IAFNuH,EAAMvH,GAAKA,EAAI8G,IAAUG,EAAKE,EAAKF,EAAK,OACxCO,EAAMxH,GAAK2H,EAAQ3H,IAAMkH,EAAKC,EAAKD,EAAK,IACdK,IAAc,EAALN,GAAUA,EAAKC,GAAO,IAAO,GAAM,IAClEnH,IAAM8G,GAAS7G,IAAM8G,GACxBjH,EAAQkD,KACPzF,EAAO8J,EAAME,GACbhK,EAAOiK,EAAME,GACbnK,EAAOyC,GACPzC,EAAO0C,GACP1C,EAAO+J,EAAMC,GACbhK,EAAOkK,EAAMC,YAIhB1H,IAAM2H,GAAS1H,IAAM2H,GAAS9H,EAAQP,OAAS,EAAIO,EAAQkD,KAAKzF,EAAOoK,GAAQpK,EAAOqK,GAAQrK,EAAOoK,GAAQpK,EAAOqK,IAAW9H,EAAQP,QAAU,EAC1H,IAAnBO,EAAQP,OACXO,EAAQkD,KAAKhD,EAAGC,EAAGD,EAAGC,EAAGD,EAAGC,GAClB6E,IACVhF,EAAQ6G,OAAO,EAAG,GAClB7G,EAAQP,OAASO,EAAQP,OAAS,GAE5BO,EA2CD,SAASgI,eAAerD,EAAQsD,OAKrCzI,EAAGU,EAAGC,EAAGsG,EAAIC,EAAIwB,EAAQC,EAJtBnB,EAAQoB,WAAWzD,EAAO,IAC7BsC,EAAQmB,WAAWzD,EAAO,IAC1BtC,EAAO,CAAC2E,EAAOC,GACfhH,EAAI0E,EAAOlF,OAAS,MAErBwI,WAAaA,GAAa,EAAM,GAC3BzI,EAAI,EAAGA,EAAIS,EAAGT,GAAK,EAKCyI,GAFxBxB,EAAKO,GAFL9G,EAAIkI,WAAWzD,EAAOnF,MAIbiH,GADTC,EAAKO,GAFL9G,EAAIiI,WAAWzD,EAAOnF,EAAE,MAGLkH,IAClBrE,EAAKa,KAAKhD,EAAGC,GACb6G,EAAQ9G,EACR+G,EAAQ9G,UAGVkC,EAAKa,KAAKkF,WAAWzD,EAAO1E,IAAKmI,WAAWzD,EAAS,EAAF1E,KAxCpD,SAASoI,aAAa1D,EAAQ2D,EAAOH,EAAMF,EAAWM,OAMpD3C,EAAOpG,EAAGK,EAvBYK,EAAGC,EAAGiB,EAAIC,EAAIgE,EAAIC,EAGxCkD,EAFG/B,EACHC,EAgBG+B,EAAYR,EACfS,EAAS/D,EAAO2D,GAChBK,EAAShE,EAAO2D,EAAM,GACtB/H,EAAQoE,EAAOwD,GACf3H,EAAQmE,EAAOwD,EAAK,OAEhB3I,EAAI8I,EAAQ,EAAG9I,EAAI2I,EAAM3I,GAAK,EAxBZU,EAyBHyE,EAAOnF,GAzBDW,EAyBKwE,EAAOnF,EAAE,GAtBvCgJ,OAAAA,EADA9B,GAFwCpB,EAyB0B9E,IAzBlCa,EAyBmBsH,KAxBhDlC,GADiCpB,EAyBuB9E,IAzB/Ba,EAyBesH,KArBlChC,KAED,GADR8B,IAAMtI,EAAIkB,GAAMqF,GAAMtG,EAAIkB,GAAMqF,IAAOD,EAAKA,EAAKC,EAAKA,KAErDtF,EAAKiE,EACLhE,EAAKiE,GACS,EAAJkD,IACVpH,GAAMqF,EAAK+B,EACXnH,GAAMqF,EAAK8B,IAeJC,GADR5I,EAXMlC,SAACuC,EAAIkB,EAAO,YAAKjB,EAAIkB,EAAO,MAajCuE,EAAQpG,EACRiJ,EAAY5I,GAGEoI,EAAZQ,IACa,EAAhB7C,EAAQ0C,GAAaD,aAAa1D,EAAQ2D,EAAO1C,EAAOqC,EAAWM,GACnEA,EAAWrF,KAAKyB,EAAOiB,GAAQjB,EAAOiB,EAAM,IAC7B,EAAfuC,EAAOvC,GAAayC,aAAa1D,EAAQiB,EAAOuC,EAAMF,EAAWM,IA0BlEF,CAAahG,EAAM,EAFnB8F,EAAO9F,EAAK5C,OAAS,EAEOwI,EAD5BC,EAAS,CAAC7F,EAAK,GAAIA,EAAK,KAExB6F,EAAOhF,KAAKb,EAAK8F,GAAO9F,EAAU,EAAL8F,IACtBD,EAGR,SAASU,2BAA2BC,EAAYC,EAAIC,EAAIC,EAAOC,EAAKC,EAAQC,EAAIC,EAAIhI,EAAIC,EAAIgE,EAAIC,EAAIC,EAAIC,OAIhG3F,EAAG4G,EAAIC,EAAI2C,EAHdC,GAAOL,EAAMD,GAASE,EACzBK,EAAO,EACPf,EAAIQ,MAELzK,EAAgBa,EACToJ,GAAKS,IAMXpJ,GAFA4G,GAHA4C,EAAM,EAAIb,GACAa,EAAMA,EAAMF,EAAK,EAAIE,EAAMA,EAAMb,EAAIpH,EAAK,EAAIiI,EAAMb,EAAIA,EAAInD,EAAKmD,EAAIA,EAAIA,EAAIjD,EAE9EuD,GAEArC,GADTC,EAFI2C,EAAMA,EAAMA,EAAMD,EAAK,EAAIC,EAAMA,EAAMb,EAAInH,EAAK,EAAIgI,EAAMb,EAAIA,EAAIlD,EAAKkD,EAAIA,EAAIA,EAAIhD,EAE9EuD,GACUrC,GACXnI,IACPA,EAAgBsB,EAChB0J,EAAOf,GAERA,GAAKc,SAEe,EAAbT,EAAkBD,2BAA2BC,EAAa,EAAGC,EAAIC,EAAIpL,KAAK6L,IAAID,EAAOD,EAAK,GAAI3L,KAAK8L,IAAIF,EAAOD,EAAK,GAAIJ,EAAQC,EAAIC,EAAIhI,EAAIC,EAAIgE,EAAIC,EAAIC,EAAIC,GAAM+D,ECjhC/J,SAAVG,EAAUC,OACLC,EAAMD,EAAQE,eAAiBF,IAC7BG,KAAkBH,EAAQI,QAAU,gBAAiBJ,EAAQI,QAElEC,GADAF,EAAiB,eACuB,eAElCF,EAAIK,aAAeL,EAAMA,EAAIK,iBACpCC,EAAOC,OACPC,EAAkB,IAAIC,EAClBT,EAAK,CAERU,GADAC,EAAOX,GACWY,gBAClBC,EAAQb,EAAIc,MACZC,EAAOJ,EAAKK,gBAAgB,6BAA8B,MAErDb,MAAMc,UAAY,WAEnBC,EAAKlB,EAAImB,cAAc,OAC1BpE,EAAKiD,EAAImB,cAAc,OACvBC,EAAOpB,IAAQA,EAAIc,MAAQd,EAAIqB,mBAC5BD,GAAQA,EAAKE,cAChBF,EAAKE,YAAYJ,GACjBA,EAAGI,YAAYvE,GACfmE,EAAGK,aAAa,QAAS,kDACzBC,EAAiBzE,EAAG0E,eAAiBP,EACrCE,EAAKM,YAAYR,WAGZlB,EAsCI,SAAZ2B,EAAY5B,UAAWA,EAAQ6B,kBAA6D,SAAxC7B,EAAQ8B,QAAU,IAAIC,cAA0B/B,EAAU,MAU7F,SAAjBgC,EAAkBhC,EAASnK,MACtBmK,EAAQM,aAAeM,GAAQb,EAAQC,IAAW,KACjDiC,EAAML,EAAU5B,GACnBkC,EAAKD,EAAOA,EAAIE,aAAa,UAAY,6BAAgC,+BACzEC,EAAOH,EAAOpM,EAAI,OAAS,IAAO,MAClCU,EAAU,IAANV,EAAU,EAAI,IAClBW,EAAU,IAANX,EAAU,IAAM,EACpBwM,EAAM,0EACNC,EAAI1B,EAAKK,gBAAkBL,EAAKK,gBAAgBiB,EAAG1H,QAAQ,SAAU,QAAS4H,GAAQxB,EAAKQ,cAAcgB,UACtGvM,IACEoM,GAScM,EAAlBA,GAAkCP,EAAehC,GACjDsC,EAAEd,aAAa,QAAS,KACxBc,EAAEd,aAAa,SAAU,KACzBc,EAAEd,aAAa,YAAa,aAAejL,EAAI,IAAMC,EAAI,KACzD+L,EAAchB,YAAYe,KAZrBE,KACJA,EAAgBR,EAAehC,IACjBI,MAAMqC,QAAUJ,GAE/BC,EAAElC,MAAMqC,QAAUJ,EAAM,gCAAkC7L,EAAI,WAAaD,EAAI,KAC/EiM,EAAcjB,YAAYe,KAUrBA,OAEF,4BAuBU,SAAjBI,EAAkB1C,EAAS2C,OAMzBC,EAAWnI,EAAGzE,EAAGO,EAAGC,EAAGqM,EALpBZ,EAAML,EAAU5B,GACnB8C,EAAY9C,IAAYiC,EACxBc,EAAWd,EAAMe,EAAYC,EAC7BC,EAASlD,EAAQM,WACjB6C,EAAaD,IAAWjB,GAAOiB,EAAOE,YAAcF,EAAOE,WAAW7B,YAAc2B,EAAOE,WAAaF,KAErGlD,IAAYO,SACRP,KAER+C,EAASjN,QAAUiN,EAASxJ,KAAKyI,EAAehC,EAAS,GAAIgC,EAAehC,EAAS,GAAIgC,EAAehC,EAAS,IACjH4C,EAAYX,EAAMM,EAAgBC,EAC9BP,EACCa,GAEHvM,IADAP,EA3BO,SAAVqN,QAAUpB,OAERf,EADGzG,EAAIwH,EAAIqB,gBAEP7I,IACJyG,EAAYe,EAAI7B,MAAMD,GACtB8B,EAAI7B,MAAMD,GAAkB,OAC5B8B,EAAIV,YAAYP,GAChBvG,EAAIuG,EAAKsC,SACTrB,EAAIN,YAAYX,GAChBE,EAAae,EAAI7B,MAAMD,GAAkBe,EAAae,EAAI7B,MAAMmD,eAAepD,EAAe3F,QAAQ,WAAY,OAAOuH,gBAEnHtH,GAAKgG,EAAgB+C,QAgBtBH,CAAQrD,IACLsC,EAAItM,EAAEJ,EACbY,GAAKR,EAAEyN,EAAIzN,EAAEE,EACbuE,EAAIgG,GACMT,EAAQ0D,SAClB1N,EAAIgK,EAAQ0D,UAGZnN,GADAkE,GADAA,EAAIuF,EAAQkB,UAAYlB,EAAQkB,UAAUyC,QAAU,IAC7CC,cAAoD,EAAlBnJ,EAAEmJ,cA1C/B,SAAfC,aAAepJ,WACVxE,EAAI,IAAIyK,EACX7K,EAAI,EACEA,EAAI4E,EAAEmJ,cAAe/N,IAC3BI,EAAE6N,SAASrJ,EAAEsJ,QAAQlO,GAAGmO,eAElB/N,EAoC0D4N,CAAapJ,GAAKA,EAAEsJ,QAAQ,GAAGC,OAAvEvD,GACjB7K,EAAII,EAAEO,EAAIkE,EAAExE,EAAID,EAAEQ,EACxBA,EAAIiE,EAAEzE,EAAIA,EAAEO,EAAIkE,EAAEvE,EAAIF,EAAEQ,IAExBiE,EAAI,IAAIiG,EACRnK,EAAIC,EAAI,GAELmM,GAAmD,MAAlC3C,EAAQ8B,QAAQC,gBACpCxL,EAAIC,EAAI,IAERsM,EAAYb,EAAMiB,GAAQ3B,YAAYqB,GACvCA,EAAUpB,aAAa,YAAa,UAAY/G,EAAE7E,EAAI,IAAM6E,EAAEzE,EAAI,IAAMyE,EAAExE,EAAI,IAAMwE,EAAEvE,EAAI,KAAOuE,EAAE6H,EAAI/L,GAAK,KAAOkE,EAAEgJ,EAAIjN,GAAK,SACxH,IACND,EAAIC,EAAI,EACJiL,MACHhH,EAAIuF,EAAQ0B,aACZ1L,EAAIgK,GACShK,EAANA,GAAUA,EAAEsK,aAAetK,IAAMyE,GAAKzE,EAAEsK,YACe,GAAxDC,EAAK0D,iBAAiBjO,GAAGmK,GAAkB,IAAIrK,SACnDS,EAAIP,EAAEkO,WACN1N,EAAIR,EAAEmO,UACNnO,EAAI,MAKa,cADpB6M,EAAKtC,EAAK0D,iBAAiBjE,IACpBoE,UAA2C,UAAhBvB,EAAGuB,aACpC3J,EAAIuF,EAAQ0B,aACLwB,GAAUA,IAAWzI,GAC3BlE,GAAK2M,EAAOmB,YAAc,EAC1B7N,GAAK0M,EAAOoB,WAAa,EACzBpB,EAASA,EAAO5C,YAGlBtK,EAAI4M,EAAUxC,OACZmE,IAAOvE,EAAQmE,UAAY3N,EAAK,KAClCR,EAAEwO,KAAQxE,EAAQkE,WAAa3N,EAAK,KACpCP,EAAEmK,GAAkB0C,EAAG1C,GACvBnK,EAAEqK,GAAwBwC,EAAGxC,GAM7BrK,EAAEoO,SAA2B,UAAhBvB,EAAGuB,SAAuB,QAAU,WACjDjB,EAAW5B,YAAYqB,UAEjBA,EAEK,SAAb6B,EAAchK,EAAG7E,EAAGI,EAAGC,EAAGC,EAAGoM,EAAGmB,UAC/BhJ,EAAE7E,EAAIA,EACN6E,EAAEzE,EAAIA,EACNyE,EAAExE,EAAIA,EACNwE,EAAEvE,EAAIA,EACNuE,EAAE6H,EAAIA,EACN7H,EAAEgJ,EAAIA,EACChJ,EAhNT,IAAImG,EAAML,EAAMI,EAAaG,EAAO0B,EAAeD,EAAe9B,EAAiBO,EAGlFS,IAFAtB,EAAiB,YACjBE,EAAuBF,EAAiB,SAgExC6C,EAAY,GACZC,EAAY,GAgJAvC,0BAKZgE,QAAA,uBACM9O,EAAoB+O,KAApB/O,EAAGI,EAAiB2O,KAAjB3O,EAAGC,EAAc0O,KAAd1O,EAAGC,EAAWyO,KAAXzO,EAAGoM,EAAQqC,KAARrC,EAAGmB,EAAKkB,KAALlB,EACnBmB,EAAehP,EAAIM,EAAIF,EAAIC,GAAM,aAC3BwO,EACNE,KACAzO,EAAI0O,GACH5O,EAAI4O,GACJ3O,EAAI2O,EACLhP,EAAIgP,GACH3O,EAAIwN,EAAIvN,EAAIoM,GAAKsC,IAChBhP,EAAI6N,EAAIzN,EAAIsM,GAAKsC,MAIrBd,SAAA,kBAASE,OACHpO,EAAoB+O,KAApB/O,EAAGI,EAAiB2O,KAAjB3O,EAAGC,EAAc0O,KAAd1O,EAAGC,EAAWyO,KAAXzO,EAAGoM,EAAQqC,KAARrC,EAAGmB,EAAKkB,KAALlB,EACnBoB,EAAKb,EAAOpO,EACZkP,EAAKd,EAAO/N,EACZ8O,EAAKf,EAAOhO,EACZgH,EAAKgH,EAAO9N,EACZ8O,EAAKhB,EAAO1B,EACZ2C,EAAKjB,EAAOP,SACNgB,EAAWE,KACjBE,EAAKjP,EAAImP,EAAK9O,EACd4O,EAAK7O,EAAI+O,EAAK7O,EACd4O,EAAKlP,EAAIoH,EAAK/G,EACd6O,EAAK9O,EAAIgH,EAAK9G,EACdoM,EAAI0C,EAAKpP,EAAIqP,EAAKhP,EAClBwN,EAAIuB,EAAKhP,EAAIiP,EAAK/O,MAGpBsN,MAAA,wBACQ,IAAI9C,SAASiE,KAAK/O,EAAG+O,KAAK3O,EAAG2O,KAAK1O,EAAG0O,KAAKzO,EAAGyO,KAAKrC,EAAGqC,KAAKlB,MAGlEyB,OAAA,gBAAOlB,OACDpO,EAAoB+O,KAApB/O,EAAGI,EAAiB2O,KAAjB3O,EAAGC,EAAc0O,KAAd1O,EAAGC,EAAWyO,KAAXzO,EAAGoM,EAAQqC,KAARrC,EAAGmB,EAAKkB,KAALlB,SACZ7N,IAAMoO,EAAOpO,GAAKI,IAAMgO,EAAOhO,GAAKC,IAAM+N,EAAO/N,GAAKC,IAAM8N,EAAO9N,GAAKoM,IAAM0B,EAAO1B,GAAKmB,IAAMO,EAAOP,KAGhH0B,MAAA,eAAMC,EAAOC,YAAAA,IAAAA,EAAU,QACjB9O,EAAQ6O,EAAR7O,EAAGC,EAAK4O,EAAL5O,EACNZ,EAAoB+O,KAApB/O,EAAGI,EAAiB2O,KAAjB3O,EAAGC,EAAc0O,KAAd1O,EAAGC,EAAWyO,KAAXzO,EAAGoM,EAAQqC,KAARrC,EAAGmB,EAAKkB,KAALlB,SACjB4B,EAAU9O,EAAKA,EAAIX,EAAIY,EAAIP,EAAIqM,GAAM,EACrC+C,EAAU7O,EAAKD,EAAIP,EAAIQ,EAAIN,EAAIuN,GAAM,EAC9B4B,+BAjDIzP,EAAKI,EAAKC,EAAKC,EAAKoM,EAAKmB,YAAzB7N,IAAAA,EAAE,YAAGI,IAAAA,EAAE,YAAGC,IAAAA,EAAE,YAAGC,IAAAA,EAAE,YAAGoM,IAAAA,EAAE,YAAGmB,IAAAA,EAAE,GACtCgB,EAAWE,KAAM/O,EAAGI,EAAGC,EAAGC,EAAGoM,EAAGmB,GA4D3B,SAAS6B,gBAAgBtF,EAAS0E,EAAS/B,EAAe4C,OAC3DvF,IAAYA,EAAQM,aAAeM,GAAQb,EAAQC,IAAUa,kBAAoBb,SAC9E,IAAIU,MAER8E,EAnPiB,SAArBC,mBAAqBnD,WAChB1M,EAAG8P,EACApD,GAAKA,IAAMxB,IACjB4E,EAAQpD,EAAEqD,QACDD,EAAME,SAAWF,EAAMG,IAAIvD,EAAG,KACnCoD,IAAUA,EAAMI,SAAWJ,EAAMK,QAAUL,EAAMM,kBACpDN,EAAMI,OAASJ,EAAMK,OAAS,KAC9BL,EAAMM,gBAAgB,EAAGN,GACzB9P,EAAIA,EAAE2D,KAAKmM,GAAU9P,EAAI,CAAC8P,IAE3BpD,EAAIA,EAAEhC,kBAEA1K,EAuOS6P,CAAmBzF,GAEnCiG,EADMrE,EAAU5B,GACFgD,EAAYC,EAC1BL,EAAYF,EAAe1C,EAAS2C,GACpCuD,EAAKD,EAAM,GAAGE,wBACdrB,EAAKmB,EAAM,GAAGE,wBACdC,EAAKH,EAAM,GAAGE,wBACdjD,EAASN,EAAUtC,WACnB+F,GAAWd,GAtND,SAAXe,SAAWtG,SACsC,UAA5CO,EAAK0D,iBAAiBjE,GAASoE,YAGnCpE,EAAUA,EAAQM,aACkB,IAArBN,EAAQuG,SACfD,SAAStG,WAgNkBsG,CAAStG,GAC5CvF,EAAI,IAAIiG,GACNoE,EAAGN,KAAO0B,EAAG1B,MAAQ,KACrBM,EAAGP,IAAM2B,EAAG3B,KAAO,KACnB6B,EAAG5B,KAAO0B,EAAG1B,MAAQ,KACrB4B,EAAG7B,IAAM2B,EAAG3B,KAAO,IACpB2B,EAAG1B,MAAQ6B,EAAU,EA9NH,SAApBG,2BAA0BjG,EAAKkG,aAAe7F,EAAKyD,YAAc1D,EAAY0D,YAAcvD,EAAMuD,YAAc,EA8NpFmC,IACzBN,EAAG3B,KAAO8B,EAAU,EAhOH,SAAnBK,0BAAyBnG,EAAKoG,aAAgB/F,EAAK0D,WAAa3D,EAAY2D,WAAaxD,EAAMwD,WAAa,EAgOlFoC,QAE1BxD,EAAOvB,YAAYiB,GACf4C,MACHU,EAAKV,EAAW1P,OACToQ,MACNpB,EAAKU,EAAWU,IACbJ,OAAShB,EAAGiB,OAAS,EACxBjB,EAAGkB,gBAAgB,EAAGlB,UAGjBJ,EAAUjK,EAAEiK,UAAYjK,ECpSlB,SAAbmM,YAAmB,EAUD,SAAlBC,GAAkBC,GACbA,EAAMC,iBACTD,EAAMC,iBACFD,EAAME,qBACTF,EAAME,uBAIQ,SAAjBC,GAAiB7E,UAAQxB,EAAKK,gBAAkBL,EAAKK,gBAAgB,+BAAgCmB,GAAQxB,EAAKQ,cAAcgB,GACnH,SAAb8E,GAAc9E,EAAMQ,EAAWuE,OAG7BC,EAFGpH,EAAUY,EAAKK,gBAAgB,6BAA8BmB,GAChEiF,EAAM,sBAIFD,KAFLD,EAAaA,GAAc,UACRA,SAAoB,cAC7BA,OACgBG,IAArBtH,EAAQI,MAAMgH,GACjBpH,EAAQI,MAAMgH,GAAKD,EAAWC,GAE9BpH,EAAQuH,eAAe,KAAMH,EAAE5M,QAAQ6M,EAAK,SAAStF,cAAeoF,EAAWC,WAGjFxE,EAAUrB,YAAYvB,GACfA,EAGiB,SAAzBwH,GAAyBC,UAAYA,EAAOvG,WAAauG,EAAOvG,UAAUyC,QAAQ+D,eAAkBC,IAAuB3D,OAqBlH,SAATlQ,GAASC,YAAqB,IAARA,GAAgBA,EAAQ,GAAK,GAAK,KAAO,IAW9C,SAAjB6T,GAA0BtF,OACpBA,EAAEmF,OAAOI,eAAiBC,GAA8C,IAAhCC,IAAaC,EAAwB,SAC7EnS,EAAIoS,GAAenS,QACT,IAALD,GACRoS,GAAepS,GAAGqS,WAEnBD,GAAenS,OAAS,GAKX,SAAfqS,GAAgBnI,EAASoC,EAAMgG,EAAMC,MAChCrI,EAAQsI,iBAAkB,KACzBC,EAAYC,EAAkBpG,GAClCiG,EAAUA,GAAW,CAACI,SAAQ,GAC9BzI,EAAQsI,iBAAiBC,GAAanG,EAAMgG,EAAMC,GAC9CE,GAAanG,IAASmG,GAAwC,YAA3BA,EAAUjN,OAAO,EAAG,IAC1D0E,EAAQsI,iBAAiBlG,EAAMgG,EAAMC,QAE5BrI,EAAQ0I,aAClB1I,EAAQ0I,YAAY,KAAOtG,EAAMgG,GAGjB,SAAlBO,GAAmB3I,EAASoC,EAAMgG,MAC7BpI,EAAQ4I,oBAAqB,KAC5BL,EAAYC,EAAkBpG,GAClCpC,EAAQ4I,oBAAoBL,GAAanG,EAAMgG,GAC3CG,GAAanG,IAASmG,GAAwC,YAA3BA,EAAUjN,OAAO,EAAG,IAC1D0E,EAAQ4I,oBAAoBxG,EAAMgG,QAEzBpI,EAAQ6I,aAClB7I,EAAQ6I,YAAY,KAAOzG,EAAMgG,GAYR,SAA3BU,GAA2BxG,GAC1ByG,GAAoBzG,EAAE0G,SAAWC,WAAa3G,EAAE0G,QAAQlT,OACxD6S,GAAgBrG,EAAEmF,OAAQ,WAAYqB,IAEf,SAAxBI,GAAwB5G,GACvByG,GAAoBzG,EAAE0G,SAAWC,WAAa3G,EAAE0G,QAAQlT,OACxDqS,GAAa7F,EAAEmF,OAAQ,WAAYqB,IAE5B,SAARK,GAASf,EAAMgB,UAAU,SAAA9G,UAAK8F,EAAKiB,KAAKD,EAAO9G,IACnC,SAAZgH,GAAalH,EAAMmH,EAAMC,OACpBC,EAAWF,EAAKG,KAAKtH,UACrBqH,GACHA,EAASJ,KAAKE,EAAKG,KAAKC,eAAiBJ,EAAMC,GAASD,GAElDA,EAGU,SAAlBK,KACCC,EAAazJ,MAAM0J,QAAU,QAC7BD,EAAaE,SACbF,EAAazJ,MAAM0J,QAAU,OAGlB,SAAZE,GAAaC,GACZrJ,EAAOsJ,SACP3J,EAAOC,OACPM,EAAQF,EAAKG,KACboJ,EAAOA,GAAQF,GAAQ1J,EAAK4J,MAAQjP,QAAQkP,KAAK,0CACjDC,EAAYF,GAAQA,EAAKF,KAAKK,SAAY,aAC1CC,EAAWtD,GAAe,QAC1B4C,EAAe5C,GAAe,aACjB7G,MAAM0J,QAAU,OAC7BhJ,GAASA,EAAMS,YAAYsI,GAC3BrB,EAAqB,SAASgC,WACzBC,EAAWD,EAAME,MAAM,KAC1BC,QAAyCrD,IAA3BiD,EAASK,cAA+B,uDAAkFtD,IAA7BiD,EAASM,gBAAiC,0DAA4DL,GAAOE,MAAM,KAC9NI,EAAM,GACNjV,EAAI,GACS,IAALA,GACRiV,EAAIL,EAAS5U,IAAM8U,EAAU9U,GAC7BiV,EAAIH,EAAU9U,IAAM4U,EAAS5U,UAEvBiV,EATa,CAUnB,6CACFC,WAAWC,UAAUC,sBAAwBF,WAAWC,UAAUC,uBAAyB,SAAS3I,UAC5FA,EAAE4I,eAAexG,UAAUZ,SAASa,KAAKuG,iBAEjDtK,EAAK0H,iBAAiB,UAAW,SAAShG,OAGxCzM,EAAGsV,EAAOvV,EAAGgF,EAFVwQ,EAAM9I,EAAE+I,SAAW/I,EAAEgJ,MACxBC,EAAYjJ,EAAE8I,KAAOA,KAEJ,UAAdG,GAAiC,KAARH,EAC5BI,GAAS,OACH,GAAkB,YAAdD,GAAmC,KAARH,EACrCK,GAAQ,OACF,GAAkB,SAAdF,GAAgC,KAARH,EAClCM,GAAO,OACD,GAAkB,QAAdH,GAA+B,KAARH,MACjCO,GAAO,EACP9V,EAAIoS,GAAenS,QACL,IAALD,GACRoS,GAAepS,GAAG+V,mBAEb,IAAmB,MAAdL,GAA6B,KAARH,KAAgBK,GAASC,IAA2B,EAAlBG,GAAS/V,WAC3E+V,GAASC,QACTX,EAAQU,GAAS,GACN,MACVjR,EAAOuQ,EAAMvQ,MACRA,KAAK4G,aAAa,IAAK2J,EAAMjV,GAClC0E,EAAKA,KAAK4G,aAAa,YAAa2J,EAAMjK,WAC1CtG,EAAKmR,OACLnW,EAAIgF,EAAKoR,SACJnW,EAAI,EAAGA,EAAID,EAAEE,OAAQD,KACsB,IAA3CsV,EAAMc,gBAAgBC,QAAQtW,EAAEC,GAAGA,IACtC+E,EAAKuR,iBAAiB5S,KAAK3D,EAAEC,IAG/B+E,EAAKwR,iBACLxR,EAAKyR,SACDzR,EAAK8O,KAAK4C,QACb1R,EAAK8O,KAAK4C,OAAOjD,KAAKzO,SAGlB,GAAkB,WAAd2Q,GAAwC,cAAdA,GAAqC,IAARH,GAAqB,KAARA,GAAsB,QAARA,GAA0B,MAARA,IAAgBK,GAASC,OACvI7V,EAAIoS,GAAenS,QACL,IAALD,GACRoS,GAAepS,GAAG0W,8BAEb,IAAmB,MAAdhB,GAA6B,KAARH,KAAgBM,GAAQD,OACxD5V,EAAIoS,GAAenS,QACL,IAALD,GACRoS,GAAepS,GAAGkU,QAAO,KAGzB,GACHnJ,EAAK0H,iBAAiB,QAAS,SAAShG,OACnC8I,EAAM9I,EAAE8I,KAAO9I,EAAE+I,SAAW/I,EAAEgJ,SACtB,UAARF,GAA2B,KAARA,EACtBI,GAAS,OACH,GAAY,YAARJ,GAA6B,KAARA,EAC/BK,GAAQ,OACF,GAAY,SAARL,GAA0B,KAARA,EAC5BM,GAAO,OACD,GAAY,QAARN,GAAyB,KAARA,EAAY,CACvCO,GAAO,UACH9V,EAAIoS,GAAenS,QACT,IAALD,GACRoS,GAAepS,GAAG2W,mBAGlB,GACHC,IAAqBlM,EAAKmM,aAC1BvE,GAAavH,EAAM,UAAWgH,IAC9BO,GAAavH,EAAM,WAAYgH,IAC/BO,GAAavH,EAAM,cAAegG,IAClCuB,GAAa5H,EAAM,YAAaqG,IAChC9F,GAASA,EAAMwH,iBAAiB,aAAc1B,IAC9C+F,EAAe,EAEL,SAAXC,GAAoBtK,OAGlBuK,EAAkBnU,EAFf6Q,EAAO5E,KACVmI,EAAMxH,gBAAgBiE,EAAK9B,OAAOnH,YAAY,QAE1CyM,QAAUpI,KAAK8C,OAAOvG,UAAUyC,QAAQI,QAAQ,GAAGC,YACnDgJ,KAAOF,EACRtE,EAAkBlG,EAAEF,OACvByK,GAAiD,IAA7BvK,EAAEF,KAAK8J,QAAQ,SAAoB5J,EAAE2K,eAAiB3K,EAAEmF,OAAU7G,EACtFuH,GAAa0E,EAAkB,WAAYtD,EAAK2D,YAChD/E,GAAa0E,EAAkB,YAAatD,EAAK4D,SACjDhF,GAAa0E,EAAkB,cAAetD,EAAK2D,YACnD/E,GAAavH,EAAM,aAAcsI,IACjCf,GAAa5H,EAAM,mBAAoBsG,MAEvCgG,EAAmB,KACnB1E,GAAavH,EAAM,YAAa2I,EAAK4D,UAEjCV,GACJtE,GAAavH,EAAM,UAAW2I,EAAK2D,YAEpCrG,GAAgBvE,GAChBsH,KACItH,EAAE8K,gBACL9K,EAAIiH,EAAK8D,MAAQ/K,EAAE8K,eAAe,GAClC7D,EAAK+D,QAAUhL,EAAEiL,YACPjL,EAAEkL,UACZjE,EAAK+D,QAAUhL,EAAEkL,UAEjBjE,EAAK8D,MAAQ9D,EAAK+D,QAAU,KAE7B/D,EAAKkE,eAAiBlE,EAAKmE,SAAWpL,EAAEqL,MACxCpE,EAAKqE,eAAiBrE,EAAKsE,SAAWvL,EAAEwL,MACxCvE,EAAKwE,eAAiBxE,EAAKwD,QAAQzK,EACnCiH,EAAKyE,eAAiBzE,EAAKwD,QAAQtJ,EAEf,IAAhBkB,KAAKqI,KAAKpX,GAA2B,IAAhB+O,KAAKqI,KAAKhX,GAA2B,IAAhB2O,KAAKqI,KAAK/W,GAA2B,IAAhB0O,KAAKqI,KAAK9W,OACvE8W,KAAO,MAEZtU,EAAO6Q,EAAKqE,eAAiBjJ,KAAKqI,KAAKpX,EAAI2T,EAAKkE,eAAiB9I,KAAKqI,KAAK/W,EAAI0O,KAAKqI,KAAK1K,EACzFiH,EAAKkE,eAAiBlE,EAAKqE,eAAiBjJ,KAAKqI,KAAKhX,EAAIuT,EAAKkE,eAAiB9I,KAAKqI,KAAK9W,EAAIyO,KAAKqI,KAAKvJ,EACxG8F,EAAKqE,eAAiBlV,GAGvB6Q,EAAK0E,UAAYnG,GAAa,EAC9ByB,EAAKsD,iBAAmBA,EACpBtD,EAAKG,KAAKwE,SACb3E,EAAKG,KAAKwE,QAAQ7E,KAAKE,EAAKG,KAAKC,eAAiBJ,EAAMA,EAAK4E,cAGrD,SAAVhB,GAAmB7K,OAGjB0G,EAASnT,EAFN0T,EAAO5E,KACVyJ,EAAgB9L,KAEZiH,EAAK8E,WAAYtF,IAAqBQ,EAAK0E,WAAc3L,MAI9D0G,GADAO,EAAK4E,aAAe7L,GACR8K,oBAEX9K,EAAI0G,EAAQ,MACFO,EAAK8D,OAAS/K,EAAEiL,aAAehE,EAAK+D,QAAS,KACtDzX,EAAImT,EAAQlT,QACE,IAALD,IAAWyM,EAAI0G,EAAQnT,IAAI0X,aAAehE,EAAK+D,aACpDzX,EAAI,eAIH,GAAIyM,EAAEkL,WAAajE,EAAK+D,SAAWhL,EAAEkL,YAAcjE,EAAK+D,eAG/DzG,GAAgBuH,GAChB7E,EAAK+E,mBAAmBhM,EAAEwL,MAAOxL,EAAEqL,OAC/BpE,EAAKG,KAAK6E,QACbhF,EAAKG,KAAK6E,OAAOlF,KAAKE,EAAKG,KAAKC,eAAiBJ,EAAMA,EAAK4E,eAGjD,SAAbjB,GAAsB5K,EAAGkM,OACpBjF,EAAO5E,QACN4E,EAAK8E,UAAa9E,EAAK0E,aAAc3L,GAAqB,MAAhBiH,EAAK+D,SAAoBkB,KAAWlM,EAAEkL,WAAalL,EAAEkL,YAAcjE,EAAK+D,SAAahL,EAAE8K,iBA9MzH,SAAdqB,YAAeC,EAAMC,WAChB9Y,EAAI6Y,EAAK5Y,QACC,IAALD,MACJ6Y,EAAK7Y,GAAG0X,aAAeoB,SACnB,SAGF,EAuMkJF,CAAYnM,EAAE8K,eAAgB7D,EAAK+D,aAnT/K,SAAdsB,cAAoB5G,EAAmBD,IAsTtC6G,GACArF,EAAK0E,UAAYnG,GAAa,MAI7BkB,EAASnT,EAHNuY,EAAgB9L,EACnBuM,EAActF,EAAKuF,WACnBjC,EAAmBtD,EAAKsD,oBAErBA,GACHlE,GAAgBkE,EAAkB,WAAYtD,EAAK2D,YACnDvE,GAAgBkE,EAAkB,YAAatD,EAAK4D,SACpDxE,GAAgBkE,EAAkB,cAAetD,EAAK2D,YACtDvE,GAAgB/H,EAAM,aAAcsI,KAEpCP,GAAgB/H,EAAM,YAAa2I,EAAK4D,SAEpCV,IACJ9D,GAAgB/H,EAAM,UAAW2I,EAAK2D,YAClC5K,GAAKA,EAAEmF,QACVkB,GAAgBrG,EAAEmF,OAAQ,UAAW8B,EAAK2D,aAGxC2B,EACHtF,EAAKuF,YAAa,EACRvF,EAAKG,KAAKqF,SACpBxF,EAAKG,KAAKqF,QAAQ1F,KAAKE,EAAKG,KAAKC,eAAiBJ,EAAM6E,GAErD9L,EAAG,KACN0G,EAAU1G,EAAE8K,kBAEX9K,EAAI0G,EAAQ,MACFO,EAAK8D,OAAS/K,EAAEiL,aAAehE,EAAK+D,QAAS,KACtDzX,EAAImT,EAAQlT,QACE,IAALD,IAAWyM,EAAI0G,EAAQnT,IAAI0X,aAAehE,EAAK+D,aACpDzX,EAAI,SAKV0T,EAAK4E,aAAeC,EACpB7E,EAAKsE,SAAWvL,EAAEwL,MAClBvE,EAAKmE,SAAWpL,EAAEqL,aAEfS,IAAkBS,GAAetF,EAAKG,KAAKsF,cAC9CzF,EAAKG,KAAKsF,cAAc3F,KAAKE,EAAMA,EAAK4E,eAGpCC,GACHvH,GAAgBuH,GAEb7E,EAAKG,KAAKuF,WACb1F,EAAKG,KAAKuF,UAAU5F,KAAKE,EAAKG,KAAKC,eAAiBJ,EAAMA,EAAK4E,eAG7DU,GAAetF,EAAKG,KAAKwF,WAC5B3F,EAAKG,KAAKwF,UAAU7F,KAAKE,EAAKG,KAAKC,eAAiBJ,EAAMA,EAAK4E,eAEzD,GAEgB,SAAxBgB,GAAyBxZ,EAASc,EAAG2Y,EAAQ1F,OAI3C7T,EAHGQ,EAAUV,EAAQc,GACrBH,EAAID,EAAQP,QAAUO,EAAQgF,OAAS,EAAI,GAC3CzF,EAAI,OAEAC,EAAI,EAAGA,EAAIS,EAAGT,GAAG,EACrBD,EAAE2D,KAAK,IAAI8V,GAAOD,EAAQzZ,EAASc,EAAGZ,EAAG6T,WAE1CrT,EAAQgF,SAAWzF,EAAE,GAAG0Z,eAAgB,GACjC1Z,EAEK,SAAb2Z,GAAclZ,EAASR,EAAG2Z,OACrBjZ,EAAIF,EAAQmZ,GAAMnZ,EAAQR,GAC7BW,EAAIH,EAAQmZ,EAAG,GAAKnZ,EAAQR,EAAE,UACxB7B,KAAKwB,KAAKe,EAAIA,EAAIC,EAAIA,GAtY/B,IACCoK,EAAM6L,EAAkBlM,EAAMO,EAAOqJ,EAAME,EAS3CoB,EAAOE,EAAMH,EAAQE,EACrB+D,EA0EAlF,EAAU/B,EAkDVqB,EAMA8C,IA7IG+C,EAAc,0CAEjBC,EAAkB,UAGlB5H,GADW/T,KAAKgB,GACL4a,KAAKC,KAAQ,kBAAM,IAAID,MAAOE,YACzC9H,EAAmB,EACnBF,EAAa,EAKbiI,EAAe,GACflE,GAAW,GACXmE,GAAS,GACTC,GAAQ,GACRC,GAAS,IACTjI,GAAiB,GA0BjBN,GAAwB,CAAC3D,OAAO,IAAItD,GA2CpCqI,GAAmB,EAoTdoH,+BAuBL7B,mBAAA,4BAAmBT,EAAUH,OAE3B0C,EAASC,EAAS9Z,EAAGC,EAAGkC,OACpBmV,SAAWA,OACXH,SAAWA,EACZ/I,KAAKqI,OACRtU,EAAOmV,EAAWlJ,KAAKqI,KAAKpX,EAAI8X,EAAW/I,KAAKqI,KAAK/W,EAAI0O,KAAKqI,KAAK1K,EACnEoL,EAAWG,EAAWlJ,KAAKqI,KAAKhX,EAAI0X,EAAW/I,KAAKqI,KAAK9W,EAAIyO,KAAKqI,KAAKvJ,EACvEoK,EAAWnV,IAEZ2X,EAAW3C,EAAW/I,KAAK8I,gBAxaT,IAAA,EA0agB4C,IACjCA,EAAU,IAFXD,EAAWvC,EAAWlJ,KAAKiJ,gBAzaT,IAAA,EA6agBwC,IACjCA,EAAU,GAEX7Z,GAjBU,KAiBHoO,KAAKoJ,eAAiBqC,GAAkB,GAjBrC,IAkBV5Z,GAlBU,KAkBHmO,KAAKqJ,eAAiBqC,GAAkB,GAlBrC,IAmBN1L,KAAK2L,OAAS9E,IACjBwE,GAAOzZ,EAAIA,EACXyZ,GAAOxZ,EAAIA,OACN8Z,KAAKjH,KAAK1E,KAAMqL,IACrBzZ,EAAIyZ,GAAOzZ,EACXC,EAAIwZ,GAAOxZ,GAERmO,KAAKpO,IAAMA,GAAKoO,KAAKnO,IAAMA,SACzBuW,QAAQtJ,EAAIkB,KAAKnO,EAAIA,OACrBuW,QAAQzK,EAAIqC,KAAKpO,EAAIA,GACrBoO,KAAKmK,YAAcnK,KAAKsJ,iBACvBa,YAAa,EAClBxF,GAAU,cAAe3E,KAAMA,KAAKwJ,mBAKvCoC,QAAA,iBAAQA,UACFC,UAAU1a,cAIVuY,SAAWkC,IAEV9D,GACJtE,GAAaxD,KAAK8C,OAAQ,YAAa9C,KAAKiI,UAE7CzE,GAAaxD,KAAK8C,OAAQ,aAAc9C,KAAKiI,UAC7CzE,GAAaxD,KAAK8C,OAAQ,QAAS9C,KAAK8L,UAAU,KAElDC,EAAW/L,KAAKmK,WAChBnG,GAAgBhE,KAAK8C,OAAQ,YAAa9C,KAAKiI,UAC/CjE,GAAgBhE,KAAK8C,OAAQ,aAAc9C,KAAKiI,UAChDjE,GAAgBpI,EAAM,mBAAoBsG,IAC1C8B,GAAgBhE,KAAK8C,OAAQ,QAAS9C,KAAK8L,UACvC9L,KAAKkI,mBACRlE,GAAgBhE,KAAKkI,iBAAkB,cAAelI,KAAKuI,YAC3DvE,GAAgBhE,KAAKkI,iBAAkB,WAAYlI,KAAKuI,YACxDvE,GAAgBhE,KAAKkI,iBAAkB,YAAalI,KAAKwI,UAE1DxE,GAAgB/H,EAAM,UAAW+D,KAAKuI,YACtCvE,GAAgB/H,EAAM,YAAa+D,KAAKwI,cACnC2B,WAAanK,KAAKsJ,WAAY,EAC/ByC,GACHpH,GAAU,YAAa3E,KAAMA,KAAKwJ,eAG7BxJ,MA5BCA,KAAK0J,aAETqC,KA6BLC,QAAA,iBAAQrO,QACF4K,WAAW5K,wCA5FLmF,EAAQiC,QACdjC,OAA6B,iBAAZA,EAAwB7G,EAAKgQ,iBAAiBnJ,GAAQ,GAAKA,OAC5EiC,KAAOA,GAAQ,QACfkD,SAAWzD,GAAMyD,GAAUjI,WAC3BwI,QAAUhE,GAAMgE,GAASxI,WACzBuI,WAAa/D,GAAM+D,GAAYvI,WAC/B8C,OAAOjG,aAAa,aAAcmD,KAAK8C,OAAOtF,aAAa,cAAgB,IAAM,wBACjF4K,QAAUvF,GAAuB7C,KAAK8C,aACtClR,EAAIoO,KAAKoI,QAAQzK,OACjB9L,EAAImO,KAAKoI,QAAQtJ,OACjB6M,KAAO5G,EAAK4G,KACZxX,MAAM4Q,EAAKmH,OAAU/X,MAAM4Q,EAAKoH,WAK/BC,QAAU,QAJVA,QAAU,OACVF,MAAQnH,EAAKmH,UACbC,MAAQpH,EAAKoH,WAIdP,SAAQ,UAmFTlB,0BAWLnB,QAAA,mBACC5E,GAAU,UAAW3E,UAGtBoK,QAAA,mBACCzF,GAAU,UAAW3E,UAGtB4J,OAAA,sBACKyC,EAAIrM,KAAKtO,aACRqT,KAAK6E,OAAOlF,KAAK1E,KAAK+E,KAAKC,eAAiBhF,KAAMA,KAAMA,KAAKsM,WAAW1a,EAAIya,EAAErM,KAAK9O,GAAI8O,KAAKsM,WAAWza,EAAIwa,EAAErM,KAAK9O,EAAE,QAG1HqZ,UAAA,qBACC5F,GAAU,YAAa3E,UAGxBsK,UAAA,qBACC3F,GAAU,YAAa3E,UAGxB0H,OAAA,gBAAO1W,EAASc,EAAGZ,GACdF,SACEA,QAAUA,GAEZ6a,UAAU1a,QAAU,GACvBW,EAAIkO,KAAKlO,EACTZ,EAAI8O,KAAK9O,SAEJY,EAAIA,OACJZ,EAAIA,OAENqb,EAAavM,KAAKwM,OACrB9a,EAAUsO,KAAKhP,QAAQc,GACvB2a,EAAW,IAANvb,GAAWQ,EAAQgF,OAAShF,EAAQP,OAAS,EAAID,EAAI,OACtDQ,QAAUA,OACV8a,OAAc,EAAJtb,GAASA,EAAIQ,EAAQP,OAAS,GAAK9B,KAAKsB,IAAItB,KAAKqd,MAAMhb,EAAQR,EAAE,GAAKQ,EAAW,EAAH+a,GAAO/a,EAAQR,GAAKQ,EAAQ+a,IAAOpd,KAAKqd,MAAMhb,EAAQR,EAAE,GAAKQ,EAAQR,EAAE,GAAIQ,EAAQR,EAAE,GAAKQ,EAAQR,KAAO,IAAQ,EAAI,EAC9M8O,KAAKwM,SAAWD,QACdlR,QAAQwB,aAAa,IAAKmD,KAAKwM,OAASxM,KAAKyK,OAAOkC,cAAgB3M,KAAKyK,OAAOmC,oBAEjFvR,QAAQwB,aAAa,YAAa,aAAenL,EAAQR,GAAK,IAAMQ,EAAQR,EAAE,GAAK,8BAjD7EuZ,EAAQzZ,EAASc,EAAGZ,EAAG6T,QAC7B0F,OAASA,OACTpP,QAAUkH,GAAW,OAAQkI,EAAOoC,WAAY,CAACC,KAAK9B,EAAiB+B,OAAO/B,EAAiBgC,YAAY,EAAGC,aAAa,4BAC3HvF,OAAO1W,EAASc,EAAGZ,QACnBmK,QAAQ6H,cAAe,OACvB6B,KAAOA,GAAQ,QACfuH,WAAa,IAAId,GAAaxL,KAAK3E,QAAS,CAAC2J,cAAchF,KAAM4J,OAAO5J,KAAK4J,OAAQ+B,KAAK3L,KAAK+E,KAAK4G,KAAMpC,QAAQvJ,KAAKuJ,QAASe,UAAUtK,KAAKsK,UAAWF,QAAQpK,KAAKoK,QAASG,UAAUvK,KAAKuK,mBAkDzL2C,8BAiDZ3E,WAAA,oBAAW5K,OACNwP,EAASnN,KAAKoN,eACdD,IACH/B,EAAaxZ,EAAIub,EAAOzb,QAAQyb,EAAOjc,GACvCka,EAAavZ,EAAIsb,EAAOzb,QAAQyb,EAAOjc,EAAE,IAE1C8S,GAAgBpI,EAAM,mBAAoBsG,IAC1CyC,GAAU,YAAa3E,KAAMrC,OAG9ByJ,KAAA,oBAMEzV,EAAGT,EALAmc,EAAWrN,KAAK/J,KAAKuH,aAAa,KACrCxM,EAAU6D,gBAAgBwY,GAC1B9Q,EAAYyD,KAAK/J,KAAKuH,aAAa,cAAgB,iBACnD8P,GAAkBtN,KAAKuN,UAAYvc,EAAQhB,cAAgBgQ,KAAKuN,SAASvd,aAAegB,EAAQG,SAAW6O,KAAKuN,SAASpc,OACzHqc,EAAa,CAACxI,cAAchF,KAAM2L,KAAK3L,KAAK+E,KAAK0I,WAAY7D,OAAO5J,KAAK0N,cAAenE,QAAQvJ,KAAK2N,eAAgBrD,UAAUtK,KAAKuI,WAAY6B,QAAQpK,KAAK4N,eAAgBrD,UAAUvK,KAAK6N,iBAAkB3B,KAAKlM,KAAK+E,KAAKmH,KAAMC,KAAKnM,KAAK+E,KAAKoH,SAG/OmB,GAAiBtN,KAAKqH,UAAYrH,KAAKqH,SAASlW,OAAQ,KACtDD,EAAI,EAAGA,EAAI8O,KAAKqH,SAASlW,OAAQD,SAChCmW,SAASnW,GAAGmK,QAAQM,WAAWqB,YAAYgD,KAAKqH,SAASnW,GAAGmK,cAC5DgM,SAASnW,GAAGob,WAAWV,SAAQ,QAEhCpE,iBAAiBrW,OAAS,UAE3Boc,SAAWvc,EACZsc,WACEjG,SAAWmD,GAAsBxZ,EAAS,EAAGgP,KAAMwN,GAEhD,GADR7b,EAAIX,EAAQG,YAEND,EAAI,EAAGA,EAAIS,EAAGT,SACbmW,SAAWrH,KAAKqH,SAASyG,OAAOtD,GAAsBxZ,EAASE,EAAG8O,KAAMwN,aAI/Etc,EAAI8O,KAAKqH,SAASlW,QACJ,IAALD,QACHmW,SAASnW,GAAGwW,OAAO1W,eAIrB6b,WAAWjQ,YAAYoD,KAAK+N,eAC5BlB,WAAWjQ,YAAYoD,KAAKgO,eAE5BC,eAAepR,aAAa,IAAKwQ,QACjCa,kBAAkBrR,aAAa,IAAKwQ,QACpCc,GAAGtR,aAAa,YA7lBO,SAA7BuR,2BAA6BtL,WACxBhN,EAAI+M,GAAuBC,GAC9BuL,EAAQvL,EAAO5F,iBACR4F,EAASA,EAAOnH,aAAemH,EAAO5F,kBAAoBmR,GACjEvY,EAAEqJ,SAAS0D,GAAuBC,UAE5B,UAAYhN,EAAE7E,EAAI,IAAM6E,EAAEzE,EAAI,IAAMyE,EAAExE,EAAI,IAAMwE,EAAEvE,EAAI,IAAMuE,EAAE6H,EAAI,IAAM7H,EAAEgJ,EAAI,IAulBnDsP,CAA2BpO,KAAK/J,KAAK0F,aAAe,uBACjFkR,WAAWhQ,aAAa,YAAaN,QACrC2R,kBAAkBrR,aAAa,YAAaN,QAC5CkL,iBACEzH,SAGRsO,WAAA,uBA5lBc,SAAdC,YAAcC,OAGZtd,EAFGoW,EAAkB,GACrBrW,EAAIud,EAAWhH,qBAEXtW,EAAI,EAAGA,EAAID,EAAEE,OAAQD,IACzBoW,EAAgBpW,GAAKD,EAAEC,GAAGA,EAE3BgW,GAASzN,QAAQ,CAACxD,KAAKuY,EAAYjd,EAAEid,EAAWvY,KAAKuH,aAAa,KAAMjB,UAAUiS,EAAWvY,KAAKuH,aAAa,cAAgB,GAAI8J,gBAAgBA,IAC7H,GAAlBJ,GAAS/V,SACZ+V,GAAS/V,OAAS,IAolBnBod,CAAYvO,UAGbyO,sBAAA,+BAAsB9Q,MACoB,WAArCqC,KAAK6M,WAAWpR,MAAMiT,gBACpBtJ,cACC,GAAI4B,GAASrJ,GAAKA,EAAEgR,OAAS,KAGlCC,EAAU1d,EAAGic,EAAQvb,EAAGC,EAAGgd,EAFxBrB,EAAa,CAACxI,cAAchF,KAAM2L,KAAK3L,KAAK+E,KAAK0I,WAAY7D,OAAO5J,KAAK0N,cAAenE,QAAQvJ,KAAK2N,eAAgBrD,UAAUtK,KAAKuI,WAAY6B,QAAQpK,KAAK4N,eAAgBrD,UAAUvK,KAAK6N,iBAAkB3B,KAAKlM,KAAK+E,KAAKmH,KAAMC,KAAKnM,KAAK+E,KAAKoH,MACrPhE,EAAMnI,KAAK6M,WAAWtG,eAAexG,cAElCC,KAAKsM,iBACHA,WAAW/D,WAAW5K,GAExBwK,IACHvW,EAAI+L,EAAEmR,QAAU3G,EAAIlX,EAAI0M,EAAEoR,QAAU5G,EAAI7W,EAAI6W,EAAIxK,EAChD9L,EAAI8L,EAAEmR,QAAU3G,EAAI9W,EAAIsM,EAAEoR,QAAU5G,EAAI5W,EAAI4W,EAAIrJ,GAGjD+P,EFiXI,SAASG,eAAehe,EAASY,EAAGC,EAAG+I,OAG5C1J,EAAGY,EAAGoI,EAAGxI,EAFNud,EAAU,CAACnd,EAAE,EAAGZ,EAAE,EAAGgJ,EAAE,GAC1BgV,EAAepe,MAEXgB,EAAI,EAAGA,EAAId,EAAQG,OAAQW,QAC/BJ,EAAUV,EAAQc,GACbZ,EAAI,EAAGA,EAAIQ,EAAQP,OAAQD,GAAG,EAClCgJ,EAAII,2BAA2B,EAAG1I,EAAGC,EAAG,EAAG,EAAG+I,GAAU,GAAIlJ,EAAQR,GAAIQ,EAAQR,EAAE,GAAIQ,EAAQR,EAAE,GAAIQ,EAAQR,EAAE,GAAIQ,EAAQR,EAAE,GAAIQ,EAAQR,EAAE,GAAIQ,EAAQR,EAAE,GAAIQ,EAAQR,EAAE,IACnJjB,EAAfif,IACHA,EAAejf,EACfgf,EAAQnd,EAAIA,EACZmd,EAAQ/d,EAAIA,EACZ+d,EAAQ/U,EAAIA,UAIR+U,EEjYSD,CAAehP,KAAKuN,SAAU3b,EAAGC,GFxQ3C,SAASsd,iBAAiBzd,EAASR,EAAGgJ,MACxCA,GAAK,GAAU,GAALA,SACN,MAEJkV,EAAK1d,EAAQR,GAChBme,EAAK3d,EAAQR,EAAE,GACfoe,EAAO5d,EAAQR,EAAE,GACjBqe,EAAO7d,EAAQR,EAAE,GACjBse,EAAO9d,EAAQR,EAAE,GACjBue,EAAO/d,EAAQR,EAAE,GAGjBwe,EAAMN,GAAME,EAAOF,GAAMlV,EACzBnD,EAAKuY,GAAQE,EAAOF,GAAQpV,EAC5ByV,EAAMN,GAAME,EAAOF,GAAMnV,EACzBlD,EAAKuY,GAAQE,EAAOF,GAAQrV,EAC5BpH,EAAK4c,GAAO3Y,EAAK2Y,GAAOxV,EACxBnH,EAAK4c,GAAO3Y,EAAK2Y,GAAOzV,EACxB0V,EAAMJ,GARD9d,EAAQR,EAAE,GAQIse,GAAQtV,EAC3B2V,EAAMJ,GARD/d,EAAQR,EAAE,GAQIue,GAAQvV,SAC5BnD,IAAO6Y,EAAM7Y,GAAMmD,EACnBlD,IAAO6Y,EAAM7Y,GAAMkD,EACnBxI,EAAQ6G,OAAOrH,EAAI,EAAG,EACrB/B,EAAOugB,GACPvgB,EAAOwgB,GACPxgB,EAAO2D,GACP3D,EAAO4D,GACP5D,EAAO2D,GAAMiE,EAAKjE,GAAMoH,GACxB/K,EAAO4D,GAAMiE,EAAKjE,GAAMmH,GACxB/K,EAAO4H,GACP5H,EAAO6H,GACP7H,EAAOygB,GACPzgB,EAAO0gB,IAERne,EAAQ/B,SAAW+B,EAAQ/B,QAAQ4I,OAASrH,EAAI,EAAKQ,EAAQ3B,WAAc,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GACzF,EEsOLof,CAAiBnP,KAAKuN,SAASsB,EAAY/c,GAAI+c,EAAY3d,EAAG2d,EAAY3U,GAC1E0U,EAAWC,EAAY3d,EAAI,EACtBA,EAAI,EAAGA,EAAI8O,KAAKqH,SAASlW,OAAQD,IACjC8O,KAAKqH,SAASnW,GAAGA,GAAK0d,GAAY5O,KAAKqH,SAASnW,GAAGY,IAAM+c,EAAY/c,SACnEuV,SAASnW,GAAGA,GAAK,GAGxBic,EAAS,IAAIzC,GAAO1K,KAAMA,KAAKuN,SAAUsB,EAAY/c,EAAG8c,EAAUpB,QAC7DX,WAAWjQ,YAAYoD,KAAK+N,eAC5BlB,WAAWjQ,YAAYoD,KAAKgO,UACjCb,EAAOb,WAAWrE,SAAStK,GAC3BmN,EAAuBqC,OAClB9F,SAASzS,KAAKuY,QACd3F,iBAAiBrW,OAAS,OAC1BqW,iBAAiB5S,KAAKuY,QACtB1F,sBACAC,cACA4G,aAENrJ,KACAzB,GAAa5H,EAAM,mBAAoBsG,IACvCyC,GAAU,UAAW3E,UAGtB8P,gBAAA,+BACK3C,EAASnN,KAAKoN,eACjBlc,EAAIic,EAAOjc,EACXmb,EAAIc,EAAOzb,QACX+a,EAAKU,EAAOxC,cAAgB0B,EAAElb,OAAS,EAAID,EAAI,EAC5C8V,GAAQ3X,KAAKsB,IAAI0b,EAAEnb,GAAKmb,EAAEI,IAAO,GAAKpd,KAAKsB,IAAI0b,EAAEnb,EAAE,GAAKmb,EAAK,EAAHI,IAAS,QACjEmB,eAAeT,OAItB4C,gBAAA,+BACK5C,EAASnN,KAAKoN,eACjBlc,EAAIic,EAAOjc,EACXmb,EAAIc,EAAOzb,QACRsV,GAAQ3X,KAAKsB,IAAI0b,EAAEnb,GAAKmb,EAAEnb,EAAE,IAAM,GAAK7B,KAAKsB,IAAI0b,EAAEnb,EAAE,GAAKmb,EAAEnb,EAAE,IAAM,QACjE0c,eAAeT,OAItBU,iBAAA,4BACC/C,EAAuB,UAClBwD,iBAGN0B,WAAA,6BACwC,EAA/BhQ,KAAKwH,iBAAiBrW,QAAmD,YAArC6O,KAAK6M,WAAWpR,MAAMiT,eAGnEtJ,OAAA,gBAAO6K,WACDpD,WAAWpR,MAAMiT,WAAa,eAC9BtB,eAAiB,WACjBnX,KAAKiN,cAAe,KACrB+M,UACC/e,EAAI8O,KAAKqH,SAASlW,QACR,IAALD,QACHsW,iBAAiBtW,GAAK8O,KAAKqH,SAASnW,UAGL,IAAlCoS,GAAeiE,QAAQvH,OAC1BsD,GAAe1O,KAAKoL,WAEhByH,iBACEzH,SAGRuD,SAAA,gCACMsJ,WAAWpR,MAAMiT,WAAa,cAC9BlH,iBAAiBrW,OAAS,OAC1Bic,eAAiB,UACjBnX,KAAKiN,cAAe,EACzBI,GAAe/K,OAAO+K,GAAeiE,QAAQvH,MAAO,QAC/CyH,iBACEzH,SAGRkQ,YAAA,2BACK3T,EAAYyD,KAAKkO,kBAAkB1Q,aAAa,cAAgB,sBAC/DqP,WAAWhQ,aAAa,YAAaN,QACrCtG,KAAK4G,aAAa,YAAaN,OAGrCoR,eAAA,wBAAeR,IACiC,IAA3CnN,KAAKwH,iBAAiBD,QAAQ4F,IAC5BtG,SACCW,iBAAiBrW,OAAS,QAE3BqW,iBAAiB5S,KAAKuY,IACjBtG,SACLW,iBAAiBjP,OAAOyH,KAAKwH,iBAAiBD,QAAQ4F,GAAS,GACpEA,EAAOb,WAAWN,WAEnBZ,EAAaxZ,EAAIub,EAAOzb,QAAQyb,EAAOjc,GACvCka,EAAavZ,EAAIsb,EAAOzb,QAAQyb,EAAOjc,EAAE,QACpCuW,iBACL9C,GAAU,UAAW3E,UAGtB4H,uBAAA,0CAGEuF,EAAQ7V,EAAOxF,EAAGqe,EAFfC,EAAUpQ,KAAKwH,iBAClBtW,EAAIkf,EAAQjf,QAEC,IAALD,QACRic,EAASiD,EAAQlf,IACVmK,QAAQM,WAAWqB,YAAYmQ,EAAO9R,SAC7C8R,EAAOb,WAAWV,SAAQ,GAC1BtU,EAAQ6V,EAAOjc,EACfif,EAAShD,EAAOrb,EACXwF,EAEMA,EAAQ6V,EAAOzb,QAAQP,OAAS,EAC1Cgc,EAAOzb,QAAQ6G,OAAOjB,EAAM,EAAG,GAE/B6V,EAAOzb,QAAQ6G,OAAOjB,EAAM,EAAG,GAJ/B6V,EAAOzb,QAAQ6G,OAAOjB,EAAO,GAM9B8Y,EAAQ7X,OAAOrH,EAAG,QACbmW,SAAS9O,OAAOyH,KAAKqH,SAASE,QAAQ4F,GAAS,GAC/Crb,EAAI,EAAGA,EAAIkO,KAAKqH,SAASlW,OAAQW,IACjCkO,KAAKqH,SAASvV,GAAGZ,GAAKoG,GAAS0I,KAAKqH,SAASvV,GAAGA,IAAMqe,SACpD9I,SAASvV,GAAGZ,GAAK,QAIpBuW,sBACAC,cACA4G,aACDtO,KAAK+E,KAAKsL,qBACRtL,KAAKsL,eAAe3L,KAAK1E,KAAK+E,KAAKC,eAAiBhF,UAI3D4N,eAAA,wBAAeT,OAMbmD,EAAQC,EAAQC,EAASC,EAASlgB,EAAKE,EALpCS,EAAIic,EAAOjc,EACdQ,EAAUyb,EAAOzb,QACjB+a,EAAKU,EAAOxC,cAAgBjZ,EAAQP,OAAS,EAAID,EAAI,EACrDwf,EAAM,IACNC,GAAUzf,GAAKA,GAAKQ,EAAQP,OAAS,EAElC6V,GAAQ8D,IAAyBqC,GAAUnN,KAAKoN,gBACnDD,EAAOX,QAAUW,EAAOX,OACpBmE,IAAUxD,EAAOxC,gBACpBwC,EAAOX,QAAS,GAEjBW,EAAO9R,QAAQwB,aAAa,IAAKsQ,EAAOX,OAASxM,KAAK2M,cAAgB3M,KAAK4M,gBACvEO,EAAOX,QAAYmE,IAASxD,EAAOxC,cA2B3BwC,EAAOX,QAAYmE,IAASxD,EAAOxC,iBAC1CzZ,GAAKic,EAAOxC,iBACfjZ,EAAQ+a,GAAM/a,EAAQR,GACtBQ,EAAW,EAAH+a,GAAQ/a,EAAQR,EAAE,IAEvBA,EAAIQ,EAAQP,OAAS,IACxBO,EAAQR,EAAE,GAAKQ,EAAQR,GACvBQ,EAAQR,EAAE,GAAKQ,EAAQR,EAAE,SAErBuW,sBACAC,cACA4G,eAnCLgC,IAFAA,EAASjhB,KAAKqd,MAAMhb,EAAQR,EAAE,GAAKQ,EAAW,EAAH+a,GAAO/a,EAAQR,GAAKQ,EAAQ+a,MACvE8D,EAASlhB,KAAKqd,MAAMhb,EAAQR,EAAE,GAAKQ,EAAQR,EAAE,GAAIQ,EAAQR,EAAE,GAAKQ,EAAQR,MAC3C,EAC7Bsf,EAAU5F,GAAWlZ,EAAS+a,EAAIvb,GAClCuf,EAAU7F,GAAWlZ,EAASR,EAAGA,EAAE,GAC/Bsf,EAAU,KACbA,EAAW5F,GAAWlZ,EAASR,EAAGub,EAAG,GAAK,EAC1C6D,EAASC,GAAUlhB,KAAKqd,MAAMhb,EAAQR,EAAE,GAAKQ,EAAQ+a,EAAG,GAAI/a,EAAQR,EAAE,GAAKQ,EAAQ+a,EAAG,KAEnFgE,EAAU,KACbA,EAAW7F,GAAWlZ,EAASR,EAAGA,EAAE,GAAK,EACzCqf,EAASD,GAAUjhB,KAAKqd,MAAMhb,EAAQR,EAAE,GAAKQ,EAAQ+a,EAAG,GAAI/a,EAAQR,EAAE,GAAKQ,EAAQ+a,EAAG,KAEvFlc,EAAMlB,KAAKkB,IAAI+f,GACf7f,EAAMpB,KAAKoB,IAAI6f,GACXjhB,KAAKsB,IAAI4f,EAASD,GAAUjhB,KAAKgB,GAAK,IACzCE,GAAOA,EACPE,GAAOA,GAERiB,EAAQ+a,KAAS/a,EAAQR,GAAKT,EAAM+f,GAAWE,EAAO,GAAKA,EAC3Dhf,EAAW,EAAH+a,KAAW/a,EAAQR,EAAE,GAAKX,EAAMigB,GAAWE,EAAO,GAAKA,EAC/Dhf,EAAQR,EAAE,KAAQQ,EAAQR,GAAKT,EAAMggB,GAAWC,EAAO,GAAKA,EAC5Dhf,EAAQR,EAAE,KAAQQ,EAAQR,EAAE,GAAKX,EAAMkgB,GAAWC,EAAO,GAAKA,OACzDjJ,sBACAC,cACA4G,eAcKzH,SACNW,iBAAiBrW,OAAS,OAC1BqW,iBAAiB5S,KAAKuY,IAE5BrC,EAAuB,UAClBrD,qBAGNA,eAAA,8BAGEvW,EAAGU,EAAGC,EAFHsb,EAA2C,IAAjCnN,KAAKwH,iBAAiBrW,OAAgB6O,KAAKwH,iBAAiB,GAAK,KAC9E9V,EAAUyb,EAASA,EAAOzb,QAAU,cAEhC0b,eAAiBD,EACjBjc,EAAI,EAAGA,EAAI8O,KAAKqH,SAASlW,OAAQD,SAChCmW,SAASnW,GAAGmK,QAAQI,MAAMqR,MAA6D,IAArD9M,KAAKwH,iBAAiBD,QAAQvH,KAAKqH,SAASnW,IAAc8Z,EAAkB,QAGhHmC,SACEY,SAASlR,aAAa,IAAKsQ,EAAOX,OAASxM,KAAK2M,cAAgB3M,KAAK4M,oBACrEoB,SAASnR,aAAa,IAAKsQ,EAAOX,OAASxM,KAAK2M,cAAgB3M,KAAK4M,gBAE3E1b,EAAIic,EAASA,EAAOjc,EAAI,EACpBic,IAAWjc,GAAKic,EAAOxC,gBAC1B/Y,EAAIub,EAAOxC,cAAgBjZ,EAAQA,EAAQP,OAAO,GAAKO,EAAQR,EAAE,GACjEW,EAAIsb,EAAOxC,cAAgBjZ,EAAQA,EAAQP,OAAO,GAAKO,EAAQR,EAAE,QAC5D6c,SAAStS,MAAMiT,WAAa1O,KAAK4Q,OAAOnV,MAAMiT,WAAe1H,GAAQpV,IAAMF,EAAQR,IAAMW,IAAMH,EAAQR,EAAE,GAAiB,UAAX,cAC/G6c,SAASlR,aAAa,YAAa,aAAejL,EAAI2Z,GAAS1Z,EAAI,UACnE+e,OAAO/T,aAAa,SAAWjL,EAAI2Z,GAAS1Z,EAAI0Z,GAAS7Z,EAAQR,GAAKqa,GAAS7Z,EAAQR,EAAE,UAEzF6c,SAAStS,MAAMiT,WAAa1O,KAAK4Q,OAAOnV,MAAMiT,WAAa,SAE7DvB,GAAUjc,EAAIQ,EAAQP,OAAS,GAClCS,EAAIF,EAAQR,EAAE,GACdW,EAAIH,EAAQR,EAAE,QACT8c,SAASvS,MAAMiT,WAAa1O,KAAK6Q,OAAOpV,MAAMiT,WAAe1H,GAAQpV,IAAMF,EAAQR,IAAMW,IAAMH,EAAQR,EAAE,GAAiB,UAAX,cAC/G8c,SAASnR,aAAa,YAAa,aAAejL,EAAI2Z,GAAS1Z,EAAI,UACnEgf,OAAOhU,aAAa,SAAWnL,EAAQR,GAAKqa,GAAS7Z,EAAQR,EAAE,GAAKqa,GAAS3Z,EAAI2Z,GAAS1Z,SAG1Fmc,SAASvS,MAAMiT,WAAa1O,KAAK6Q,OAAOpV,MAAMiT,WAAa,aAIlEzH,YAAA,2BACKkG,EAASnN,KAAKoN,eACdD,KACCA,EAAOjc,GAAKic,EAAOxC,sBACjBoD,SAAStS,MAAMiT,WAAa1O,KAAK4Q,OAAOnV,MAAMiT,WAAa,WAE7DvB,EAAOjc,EAAIic,EAAOzb,QAAQP,OAAS,SACjC6c,SAASvS,MAAMiT,WAAa1O,KAAK6Q,OAAOpV,MAAMiT,WAAa,gBAKnE7G,cAAA,6BAEEwE,EAAGnb,EAAGub,EADHU,EAASnN,KAAKoN,eAEdD,IACHd,EAAIc,EAAOzb,QACXR,EAAIic,EAAOjc,EACXub,EAAKU,EAAOxC,cAAgB0B,EAAElb,OAAS,EAAID,EAAI,EAC3Cmb,EAAEnb,KAAOmb,EAAEI,IAAOJ,EAAEnb,EAAE,KAAOmb,EAAK,EAAHI,UAC7BsB,SAAStS,MAAMiT,WAAa1O,KAAK4Q,OAAOnV,MAAMiT,WAAa,UAE7DrC,EAAEnb,KAAOmb,EAAEnb,EAAE,IAAMmb,EAAEnb,EAAE,KAAOmb,EAAEnb,EAAE,UAChC8c,SAASvS,MAAMiT,WAAa1O,KAAK6Q,OAAOpV,MAAMiT,WAAa,eAKnEoC,gBAAA,2BACK9Q,KAAKoN,eAAeZ,cAClBuE,sBAAwBnG,GAAW5K,KAAKoN,eAAe1b,QAASsO,KAAKoN,eAAelc,EAAG8O,KAAKoN,eAAelc,EAAE,IAEnHyT,GAAU,UAAW3E,UAGtBgR,gBAAA,2BACKhR,KAAKoN,eAAeZ,cAClBuE,sBAAwBnG,GAAW5K,KAAKoN,eAAe1b,QAASsO,KAAKoN,eAAezC,cAAgB3K,KAAKoN,eAAe1b,QAAQP,OAAS,EAAI6O,KAAKoN,eAAelc,EAAE,EAAG8O,KAAKoN,eAAelc,IAEhMyT,GAAU,UAAW3E,UAGtBiR,iBAAA,0BAAiBtT,QACX4K,WAAW5K,QACX2Q,iBAGN4C,eAAA,8BAQE7e,EAPG8a,EAASnN,KAAKoN,eACjBf,EAAIc,EAAOzb,QACXR,EAAIic,EAAOjc,EACXub,EAAKU,EAAOxC,cAAgB0B,EAAElb,OAAO,EAAID,EAAE,EAC3Cwf,EAAM,IACN9e,EAAIoO,KAAK+N,SAASzB,WAAW1a,EAC7BC,EAAImO,KAAK+N,SAASzB,WAAWza,EAE9Bwa,EAAEI,GAAM7a,GAAMA,EAAI8e,EAAO,GAAKA,EAC9BrE,EAAK,EAAHI,GAAQ5a,GAAMA,EAAI6e,EAAO,GAAKA,EAC5BvD,EAAOX,SACNxF,GACHmG,EAAOX,QAAS,EAChBW,EAAO9R,QAAQwB,aAAa,IAAKmD,KAAK4M,oBACjCmB,SAASlR,aAAa,IAAKmD,KAAK4M,oBAChCoB,SAASnR,aAAa,IAAKmD,KAAK4M,iBAErCva,EAAQhD,KAAKqd,MAAML,EAAEnb,EAAE,GAAKW,EAAGwa,EAAEnb,GAAKU,GACtCA,EAAIoO,KAAK+Q,sBAAwB1hB,KAAKoB,IAAI4B,GAC1CR,EAAImO,KAAK+Q,sBAAwB1hB,KAAKkB,IAAI8B,GAC1Cga,EAAEnb,EAAE,KAAQmb,EAAEnb,GAAKU,GAAK8e,EAAO,GAAKA,EACpCrE,EAAEnb,EAAE,KAAQmb,EAAEnb,EAAE,GAAKW,GAAK6e,EAAO,GAAKA,SAInChJ,aAGNyJ,eAAA,8BAQE9e,EAPG8a,EAASnN,KAAKoN,eACjBf,EAAIc,EAAOzb,QACXR,EAAIic,EAAOjc,EACXub,EAAKU,EAAOxC,cAAgB0B,EAAElb,OAAO,EAAID,EAAE,EAC3Cwf,EAAM,IACN9e,EAAIoO,KAAKgO,SAAS1B,WAAW1a,EAC7BC,EAAImO,KAAKgO,SAAS1B,WAAWza,EAE9Bwa,EAAEnb,EAAE,GAAKU,GAAMA,EAAI8e,EAAO,GAAKA,EAC/BrE,EAAEnb,EAAE,GAAKW,GAAMA,EAAI6e,EAAO,GAAKA,EAC3BvD,EAAOX,SACNxF,GACHmG,EAAOX,QAAS,EAChBW,EAAO9R,QAAQwB,aAAa,IAAKmD,KAAK4M,oBACjCmB,SAASlR,aAAa,IAAKmD,KAAK4M,oBAChCoB,SAASnR,aAAa,IAAKmD,KAAK4M,iBAErCva,EAAQhD,KAAKqd,MAAML,EAAEnb,EAAE,GAAKW,EAAGwa,EAAEnb,GAAKU,GACtCA,EAAIoO,KAAK+Q,sBAAwB1hB,KAAKoB,IAAI4B,GAC1CR,EAAImO,KAAK+Q,sBAAwB1hB,KAAKkB,IAAI8B,GAC1Cga,EAAEI,KAASJ,EAAEnb,GAAKU,GAAK8e,EAAO,GAAKA,EACnCrE,EAAK,EAAHI,KAAWJ,EAAEnb,EAAE,GAAKW,GAAK6e,EAAO,GAAKA,SAIpChJ,aAGNgG,cAAA,uBAAcP,EAAQiE,EAASC,OAI7BngB,EAAGY,EAAGua,EAAGpb,EAAGwb,EAHT2D,EAAUpQ,KAAKwH,iBAClB7V,EAAIye,EAAQjf,OACZuf,EAAM,QAEF5e,EAAI,EAAGA,EAAIH,EAAGG,IAElBZ,GADAD,EAAImf,EAAQte,IACNZ,EACNmb,EAAIpb,EAAES,QACFR,GACHmb,EAAEnb,EAAE,KAAQmb,EAAEnb,EAAE,GAAKkgB,GAAWV,EAAO,GAAKA,EAC5CrE,EAAEnb,EAAE,KAAQmb,EAAEnb,EAAE,GAAKmgB,GAAWX,EAAO,GAAKA,GAClCzf,EAAE0Z,gBAEZ0B,EADAI,EAAKJ,EAAElb,OAAO,GACNhC,GAAOkd,EAAEI,GAAM2E,GACvB/E,EAAK,EAAHI,GAAQtd,GAAOkd,EAAK,EAAHI,GAAQ4E,GAC3BhF,EAAEI,EAAG,GAAKtd,GAAOkd,EAAEI,EAAG,GAAK2E,GAC3B/E,EAAEI,EAAG,GAAKtd,GAAOkd,EAAEI,EAAG,GAAK4E,IAE5BhF,EAAEnb,KAAQmb,EAAEnb,GAAKkgB,GAAWV,EAAO,GAAKA,EACxCrE,EAAEnb,EAAE,KAAQmb,EAAEnb,EAAE,GAAKmgB,GAAWX,EAAO,GAAKA,EACxCxf,EAAImb,EAAElb,OAAS,IAClBkb,EAAEnb,EAAE,KAAQmb,EAAEnb,EAAE,GAAKkgB,GAAWV,EAAO,GAAKA,EAC5CrE,EAAEnb,EAAE,KAAQmb,EAAEnb,EAAE,GAAKmgB,GAAWX,EAAO,GAAKA,GAEzCzf,IAAMkc,GACTlc,EAAEoK,QAAQwB,aAAa,YAAa,aAAgBwP,EAAEnb,GAAMqa,GAAUc,EAAEnb,EAAE,GAAM,UAG7EwW,aAGNkE,QAAA,iBAAQA,OACFC,UAAU1a,cACP6O,KAAK0J,iBAETxY,EAAI8O,KAAKqH,SAASlW,QACR,IAALD,QACHmW,SAASnW,GAAGob,WAAWV,QAAQA,eAEhClC,SAAWkC,OACXmC,SAASzB,WAAWV,QAAQA,QAC5BoC,SAAS1B,WAAWV,QAAQA,GAC7B5L,KAAKsM,iBACHA,WAAWV,QAAQA,GAEpBA,EAIO5L,KAAK6M,WAAWlR,kBACtB1F,KAAKiH,gBAAgBN,YAAYoD,KAAKkO,wBACtCjY,KAAKiH,gBAAgBN,YAAYoD,KAAK6M,iBACtCzF,YACAkH,oBAPA/K,gBACA2K,kBAAkBvS,YAAcqE,KAAKkO,kBAAkBvS,WAAWqB,YAAYgD,KAAKkO,wBACnFrB,WAAWlR,YAAcqE,KAAK6M,WAAWlR,WAAWqB,YAAYgD,KAAK6M,kBAOtEpF,iBACEzH,KAAK0H,aAGbA,OAAA,gBAAO4J,OAGLpgB,EAAGmb,EAAGza,EAAGC,EAAG4a,EAFTlb,EAAI,GACP4b,EAASnN,KAAKoN,kBAEXkE,QACElK,OAEF+F,IACHjc,EAAIic,EAAOjc,EACXmb,EAAIc,EAAOzb,SACPR,GAAKic,EAAOxC,iBAEf/Y,EAAIya,EADJI,EAAKU,EAAOxC,cAAgB0B,EAAElb,OAAO,EAAID,EAAE,GAE3CW,EAAIwa,EAAK,EAAHI,QACDsB,SAASlR,aAAa,YAAa,aAAejL,EAAI2Z,GAAS1Z,EAAI,UACnE+e,OAAO/T,aAAa,SAAUjL,EAAI2Z,GAAS1Z,EAAI0Z,GAASc,EAAEnb,GAAKqa,GAASc,EAAEnb,EAAE,KAE9EA,EAAImb,EAAElb,OAAS,IAClBS,EAAIya,EAAEnb,EAAE,GACRW,EAAIwa,EAAEnb,EAAE,QACH8c,SAASnR,aAAa,YAAa,aAAgBjL,EAAK2Z,GAAU1Z,EAAK,UACvEgf,OAAOhU,aAAa,SAAUwP,EAAEnb,GAAKqa,GAASc,EAAEnb,EAAE,GAAKqa,GAAS3Z,EAAI2Z,GAAS1Z,KAIhFyf,EACH/f,EAAIyO,KAAK/J,KAAKuH,aAAa,SACrB,KACDtM,EAAI,EAAGA,EAAI8O,KAAKuN,SAASpc,OAAQD,IAEtB,GADfmb,EAAIrM,KAAKuN,SAASrc,IACZC,SACLI,GAAK,IAAM8a,EAAE,GAAKd,GAASc,EAAE,GAAK,IAAMA,EAAEzc,MAAM,GAAG2hB,KAAKhG,UAGrDtV,KAAK4G,aAAa,IAAKtL,QACvB0c,eAAepR,aAAa,IAAKtL,QACjC2c,kBAAkBrR,aAAa,IAAKtL,UAGtCyO,KAAK+E,KAAKyM,UAAYxR,KAAK0J,UAC9B/E,GAAU,WAAY3E,KAAMzO,GAEtByO,SAGRyR,WAAA,oBAAWC,EAAiBC,EAASC,MAChCF,EAAiB,KAChB5b,EAAI+M,GAAuB7C,KAAK/J,aAC7B7E,iBAAiBL,YAAYiP,KAAKuN,UAAW,EAAG,EAAG,EAAG,EAAGzX,EAAE6H,GAAKgU,GAAW,GAAI7b,EAAEgJ,GAAK8S,GAAW,WAElG5R,KAAKuN,aAGbsE,UAAA,mBAAUH,EAAiBC,EAASC,MAC/BF,EAAiB,KAChB5b,EAAI+M,GAAuB7C,KAAK/J,aFjChC,SAAS6b,gBAAgB9gB,IA5jCnB,SAAZ+gB,UAAY3iB,SAA2B,iBAAXA,EA6jCxB2iB,CAAU/gB,EAAQ,MACrBA,EAAU,CAACA,QAIXghB,EAAI3F,EAAGnb,EAAGQ,EAFPkI,EAAS,GACZjI,EAAIX,EAAQG,WAERkb,EAAI,EAAGA,EAAI1a,EAAG0a,IAAK,KACvB3a,EAAUV,EAAQqb,GAClBzS,GAAU,IAAMzK,EAAOuC,EAAQ,IAAM,IAAMvC,EAAOuC,EAAQ,IAAM,KAChEsgB,EAAKtgB,EAAQP,OACRD,EAAI,EAAGA,EAAI8gB,EAAI9gB,IACnB0I,GAAUzK,EAAOuC,EAAQR,MAAQ,IAAM/B,EAAOuC,EAAQR,MAAQ,IAAM/B,EAAOuC,EAAQR,MAAQ,IAAM/B,EAAOuC,EAAQR,MAAQ,IAAM/B,EAAOuC,EAAQR,MAAQ,IAAM/B,EAAOuC,EAAQR,IAAM,IAE7KQ,EAAQgF,SACXkD,GAAU,YAGLA,EEgBEkY,CAAgB1gB,iBAAiBL,YAAYiP,KAAKuN,UAAW,EAAG,EAAG,EAAG,EAAGzX,EAAE6H,GAAKgU,GAAW,GAAI7b,EAAEgJ,GAAK8S,GAAW,YAElH5R,KAAK/J,KAAKuH,aAAa,SAG/ByU,iBAAA,0BAAiBC,EAAQC,EAASC,EAASC,OAQzChc,EAAQnF,EAAG4B,EAAIC,EAAIgE,EAAIC,EAPpBqV,EAAIrM,KAAKuN,SAAS,GACrB/b,GAAa,EAAR6a,EAAE,GACP5a,EAAkB,IAAZ0gB,EAAiB,IAAMA,GAAW9F,EAAE,IAC1C1a,EAAI0a,EAAElb,OACN4D,EAAK,GAAKsX,EAAE1a,EAAE,GAAKH,GACnBwD,GAAMkd,GAAW7F,EAAE1a,EAAE,GAAKF,MAK1BuD,EADGA,EACE,EAAIA,GAEHD,EAEPA,GARO,IASPC,GATO,IAUF9D,EARLoa,GAAMna,OAAS,EAQHD,EAAIS,EAAGT,GAAK,EACvBoa,GAAMpa,KAAQmb,EAAEnb,GAAKM,GAAMuD,EAAM,GAX3B,IAYNuW,GAAMpa,EAAE,KAAQmb,EAAEnb,EAAE,GAAKO,GAAMuD,EAAM,GAZ/B,OAeHqd,EAAa,KAChBhc,EAAS,GACT1E,EAAI2Z,GAAMna,OACLD,EAAI,EAAGA,EAAIS,EAAGT,GAAG,EACrB4B,EAAKwY,GAAMpa,EAAE,GACb6B,EAAKuY,GAAMpa,EAAE,GACb6F,EAAKuU,GAAMpa,EAAE,GACb8F,EAAKsU,GAAMpa,EAAE,GACbmF,EAAOzB,KAAK9B,EAAIC,EAAIgE,EAAIC,GACxBF,eAAehE,EAAIC,EAAIuY,GAAMpa,GAAIoa,GAAMpa,EAAE,GAAIoa,GAAMpa,EAAE,GAAIoa,GAAMpa,EAAE,GAAI6F,EAAIC,EAAI,KAAOX,EAAQA,EAAOlF,OAAS,OAE7G2B,EAAKuD,EAAO,GACZ1E,EAAI0E,EAAOlF,OACND,EAAI,EAAGA,EAAIS,EAAGT,GAAG,EAAG,KACxB6F,EAAKV,EAAOnF,IACH4B,GAAW,EAALiE,GAAUA,EAAK,EAAG,CAChCsb,UAGDvf,EAAKiE,UAIHqb,GAAiB,IAANzgB,GAAwB,IAAb2Z,GAAM,IAAyB,IAAbA,GAAM,IAA2B,IAAfA,GAAM3Z,EAAE,IAA2B,IAAf2Z,GAAM3Z,EAAE,GAClF2Z,GAAM1b,MAAM,EAAG,GAAG2hB,KAAK,MAE/BjG,GAAM,GAAK,IAAMA,GAAM,GAChB,IAAMA,GAAMiG,KAAK,UAGzBe,KAAA,qBACM1G,SAAQ,QACRuC,GAAGxS,YAAcqE,KAAKmO,GAAGxS,WAAWqB,YAAYgD,KAAKmO,QAG3DoE,OAAA,uBACMD,wCA1nBMxP,EAAQiC,GACnBA,EAAOA,GAAQ,GACfiD,GAAgB3C,UACXN,KAAOA,OACP9O,KAA2B,iBAAZ6M,EAAwB7G,EAAKgQ,iBAAiBnJ,GAAQ,GAAKA,OAC1EqL,GAAK5L,GAAW,IAAKvC,KAAK/J,KAAKiH,gBAAiB,OAAO,mCACvDgR,kBAAoB3L,GAAW,OAAQvC,KAAKmO,GAAI,CAACpB,OAAO,cAAeC,YAAY,GAAIF,KAAK,OAAQG,aAAa,4BACjHJ,WAAa9H,EAAK8H,YAActK,GAAW,IAAKvC,KAAKmO,GAAI,OAAO,2CAChEF,eAAiB1L,GAAW,OAAQvC,KAAK6M,WAAY,CAACE,OAAO/B,EAAiBgC,YAAY,EAAGF,KAAK,OAAQG,aAAa,4BACvHzF,iBAAmB,QACnBoJ,OAASrO,GAAW,WAAYvC,KAAK6M,WAAY,CAACE,OAAO/B,EAAiBgC,YAAY,EAAGC,aAAa,4BACtG4D,OAAStO,GAAW,WAAYvC,KAAK6M,WAAY,CAACE,OAAO/B,EAAiBgC,YAAY,EAAGC,aAAa,4BACtG2D,OAAOnV,MAAM+W,cAAgBxS,KAAK6Q,OAAOpV,MAAM+W,cAAgBxS,KAAKiO,eAAexS,MAAM+W,cAAgB,YACzG9I,UAAW,MACZvB,EAAMnI,KAAK/J,KAAK0F,WAAW4K,eAAexG,UAC7C0S,GAAQtK,EAAIlX,EAAIkX,EAAI5W,GAAK,GAAKwT,EAAK2N,YAAc,QAC7C9F,cA3fe,SAArB+F,mBAAqBF,SAEb,CAAC,MADRA,EAAOtjB,GAAOsjB,KACQA,EAAMA,GAAOA,EAAMA,EAAMA,GAAOA,EAAMA,EAAO,KAAKlB,KAAKhG,IAyfxDoH,CAAmBF,QACnC9F,cAxfe,SAArBiG,mBAAqBH,OAEnBI,EAAQ1jB,GADE,cACKsjB,SAET,KADPA,EAAOtjB,GAAOsjB,IACM,MAAQ,CAACA,EAAMI,EAAOA,EAAOJ,EAAM,EAAGA,GAAQI,EAAOJ,GAAOA,EAAMI,GAAQJ,EAAM,GAAIA,GAAOI,GAAQA,GAAQJ,EAAM,GAAIA,EAAMI,GAAQJ,EAAMA,GAAOI,EAAOJ,EAAM,GAAGlB,KAAKhG,IAAU,IAof9KqH,CAA0B,KAAPH,QACnC1E,SAAWxL,GAAW,OAAQvC,KAAK6M,WAAY,CAACtb,EAAEyO,KAAK4M,cAAeE,KAAK9B,EAAiB+B,OAAO,cAAeC,YAAY,SAC9HgB,SAAWzL,GAAW,OAAQvC,KAAK6M,WAAY,CAACtb,EAAEyO,KAAK4M,cAAeE,KAAK9B,EAAiB+B,OAAO,cAAeC,YAAY,SAC9He,SAASzB,WAAa,IAAId,GAAaxL,KAAK+N,SAAU,CAACnE,OAAO5J,KAAKkR,eAAgBlM,cAAchF,KAAMuJ,QAAQvJ,KAAK8Q,gBAAiBxG,UAAUtK,KAAKiR,iBAAkB7G,QAAQpK,KAAK8P,gBAAiBnE,KAAK5G,EAAK+N,kBAC9M9E,SAAS1B,WAAa,IAAId,GAAaxL,KAAKgO,SAAU,CAACpE,OAAO5J,KAAKmR,eAAgBnM,cAAchF,KAAMuJ,QAAQvJ,KAAKgR,gBAAiB1G,UAAUtK,KAAKiR,iBAAkB7G,QAAQpK,KAAK+P,gBAAiBpE,KAAK5G,EAAK+N,kBAC9M/E,SAAStS,MAAMiT,WAAa1O,KAAKgO,SAASvS,MAAMiT,WAAa,iBAC9DqE,EAAiB,CAAC/S,KAAK+N,SAAU/N,KAAKgO,SAAUhO,KAAK4Q,OAAQ5Q,KAAK6Q,OAAQ7Q,KAAK6M,WAAY7M,KAAKiO,eAAgBjO,KAAKkO,mBACxHhd,EAAI6hB,EAAe5hB,QACN,IAALD,GACR6hB,EAAe7hB,GAAGgS,cAAe,GAEX,IAAnB6B,EAAKiO,iBACH1G,WAAa,IAAId,GAAaxL,KAAKkO,kBAAmB,CAAClJ,cAAchF,KAAMuJ,QAAQvJ,KAAKoF,OAAQkF,UAAUtK,KAAKuI,WAAYqB,OAAO5J,KAAKkQ,YAAa3F,UAAUvK,KAAKsO,WAAYpC,KAAKlM,KAAK+E,KAAKmH,KAAMC,KAAKnM,KAAK+E,KAAKoH,aAEpN/E,YACAyF,WAAWpR,MAAMiT,YAAgC,IAAlB3J,EAAKkO,SAAsB,SAAW,WACpD,IAAlBlO,EAAKkO,gBACHhd,KAAKiN,cAAe,EACzBI,GAAe1O,KAAKoL,YAEhBsO,aACAxG,IACJtE,GAAaxD,KAAKkO,kBAAmB,YAAa1J,GAAMxE,KAAKyO,sBAAuBzO,OACpFwD,GAAaxD,KAAKkO,kBAAmB,UAAW1J,GAAMxE,KAAKuI,WAAYvI,QAExEwD,GAAaxD,KAAKkO,kBAAmB,aAAc1J,GAAMxE,KAAKyO,sBAAuBzO,OACrFwD,GAAaxD,KAAKkO,kBAAmB,WAAY1J,GAAMxE,KAAKuI,WAAYvI,OACxE0F,EAAS1F,MAulBXkN,GAAWxT,eAAiBA,eAC5BwT,GAAW1U,gBAAkBA,gBAC7B0U,GAAWgG,YAAc,SAACC,EAAMpO,OAC3B1J,EAAShF,EAAQnF,EAAG4B,EAAIiE,EAAIhE,EAAIiE,EAAIoc,EAAQC,EAAW1Z,EAAWhI,KAEtEgI,GADAoL,EAAOA,GAAQ,IACEpL,WAAa,EAC9B0Z,EAAYtO,EAAKsO,WAAc,EAAI1Z,OACSgJ,IAAzBoC,EAAKuO,iBAAsCvO,EAAKuO,gBAC9C,iBAAVH,IAEVA,GADA9X,EAAU8X,GACK3V,aAAa,MAEN,MAAnB2V,EAAKvc,OAAO,IAAiC,MAAnBuc,EAAKvc,OAAO,KACzCyE,EAAUY,EAAKsX,cAAcJ,MAE5BA,EAAO9X,EAAQmC,aAAa,MAG9BnH,GAA0B,IAAhB0O,EAAKyO,QAAqB,eAAeC,KAAKN,GAAmCte,gBAAgBse,GAAM,GAAhDA,EAAKnd,MAAM+U,IACxD,IAAhBhG,EAAKyO,OAAkB,KAC1BJ,EAAS/c,EACTA,EAAS,GACT1E,EAAIyhB,EAAOjiB,OACND,EAAI,EAAGA,EAAIS,EAAGT,GAAG,EACrB4B,GAAMsgB,EAAOliB,EAAE,GACf6B,GAAMqgB,EAAOliB,EAAE,GACf6F,GAAMqc,EAAOliB,EAAE,GACf8F,GAAMoc,EAAOliB,EAAE,GACfmF,EAAOzB,KAAKzF,GAAO2D,GAAK3D,GAAO4D,GAAK5D,GAAO4H,GAAK5H,GAAO6H,IACvDF,eAAehE,EAAIC,GAAKqgB,EAAOliB,IAAKkiB,EAAOliB,EAAE,IAAKkiB,EAAOliB,EAAE,IAAKkiB,EAAOliB,EAAE,GAAI6F,EAAIC,EAAI,GAAiB,IAAZqc,GAAqBhd,EAAQA,EAAOlF,OAAS,IAExIkF,EAASmC,gBAAgBkB,eAAerD,EAAQsD,GAAYoL,EAAKtM,YAC1D,GAAK,IAAMpC,EAAO,QAEzBA,EAASqD,eAAerD,EAAQsD,UAEjCwZ,EAAO,IAAM9c,EAAOkb,KAAK,KACrBlW,GACHA,EAAQwB,aAAa,IAAKsW,GAEpBA,GAGRjG,GAAWwG,OAAS,SAAC5Q,EAAQiC,UAAS,IAAImI,GAAWpK,EAAQiC,IAE7DmI,GAAWyG,YAAcvI,EAEzB8B,GAAW0G,gBAAkB,SAAC7O,OACzB8O,EAAI9O,EAAK+O,QAAU,EACtBC,EAAM,KACN5H,EAAQpH,EAAKnT,GAAgB,IAAXmT,EAAKnT,EAAWmT,EAAKnT,EAAImT,EAAKiP,MAAQ,GAAKD,EAC7DE,EAAQlP,EAAKlT,GAAgB,IAAXkT,EAAKlT,EAAWkT,EAAKlT,EAAIkT,EAAKmN,OAAS,GAAK6B,EAC9D7H,EAAOC,GAAQpH,EAAKiP,OAASD,MAC7BG,EAAOD,GAAQlP,EAAKmN,QAAU6B,MAC9BI,GAA8B,IAAlBpP,EAAKoP,SACjBC,GAA8B,IAAlBrP,EAAKqP,SACjBC,EAAOtP,EAAKsP,KACZC,EAAOvP,EAAKwP,gBACbV,GAAKA,EACE,SAAApR,OAGL+R,EAAOC,EAAOtc,EAAIC,EAFfxG,EAAI6Q,EAAE7Q,EACTC,EAAI4Q,EAAE5Q,EAEFsiB,GAAYviB,EAAIua,IAAUhU,EAAKvG,EAAIua,GAAQhU,EAAK0b,EACpDjiB,EAAIua,GACOgI,GAAgBjI,EAAJta,IAAcuG,EAAK+T,EAAOta,GAAKuG,EAAK0b,KAC3DjiB,EAAIsa,GAEAkI,GAAYviB,EAAIoiB,IAAU7b,EAAKvG,EAAIoiB,GAAQ7b,EAAKyb,EACpDhiB,EAAIoiB,GACOG,GAAgBF,EAAJriB,IAAcuG,EAAK8b,EAAOriB,GAAKuG,EAAKyb,KAC3DhiB,EAAIqiB,GAEDG,IACHlc,EAAKvG,EAAIyiB,EAAKziB,EACdwG,EAAKvG,EAAIwiB,EAAKxiB,EACVsG,EAAKA,EAAK0b,IACbjiB,EAAIyiB,EAAKziB,GAENwG,EAAKA,EAAKyb,IACbhiB,EAAIwiB,EAAKxiB,IAGPyiB,IAEHnc,GADAqc,EAAQrI,EAAO9c,KAAKC,OAAOsC,EAAIua,GAAQmI,GAAQA,GAClC1iB,GAGJuG,GADTC,GADAqc,EAAQR,EAAO5kB,KAAKC,OAAOuC,EAAIoiB,GAAQK,GAAQA,GAClCziB,GACMuG,EAAKyb,IACvBjiB,EAAI4iB,EACJ3iB,EAAI4iB,GAGNhS,EAAE7Q,EAAIA,EACN6Q,EAAE5Q,EAAIA,IAIRqb,GAAWwH,QAAU,SAErBxH,GAAWyH,SAAWtP,GClxCT,SAAZuP,GAAYxlB,SAA2B,iBAAXA,EACX,SAAjBkT,GAAkB7E,EAAMF,OACnBI,EAAI1B,GAAKK,gBAAkBL,GAAKK,iBAAiBiB,GAAM,gCAAgC1H,QAAQ,SAAU,QAAS4H,GAAQxB,GAAKQ,cAAcgB,UAC1IE,EAAElC,MAAQkC,EAAI1B,GAAKQ,cAAcgB,GA+B5B,SAAboX,GAAc5e,EAAM6M,EAAQiC,UAAU6P,GAAU3e,IAAS6e,GAAarB,KAAKxd,GAASgG,GAAKsX,cAActd,GAAQ8e,MAAMC,QAAQ/e,GAAQgf,GAAiBC,IAAiB,CAACtjB,EAAE4T,GAAK2P,YAAYrS,EAAQ,KAAMjR,EAAE2T,GAAK2P,YAAYrS,EAAQ,cAAU7M,GAAO8O,IAAU6P,GAAU3e,IAASA,GAA8C,UAArCA,EAAKkH,QAAU,IAAIC,cAA4BnH,EAAO,EAmCrU,SAAZoP,GAAaC,EAAM8P,OACdC,EAAU,+CACdzZ,GAAOC,QACP2J,GAAOA,IAAQF,GAAQ1J,GAAK4J,MAAQjP,QAAQkP,KAAK4P,KACzCnI,GAAWyH,SAASnP,IAC5BvJ,GAAOsJ,SACPpJ,GAAQF,GAAKG,KACbkZ,GAASrZ,GAAKC,gBACVsJ,KACH+P,GAAmB/P,GAAKgQ,QAAQC,WAChCC,GAAiBxI,WAAaA,GAC9BxH,GAAWF,GAAKF,KAAKK,SAAW,cAE5B4P,IArDiB,SAAvBI,wBACCzQ,GAAe5C,GAAe,aACjB7G,MAAM0J,QAAU,OAC7BhJ,GAAMS,YAAYsI,IAqDjByQ,GACAT,GAAkBK,GAAiBK,eACnCX,GAAmBM,GAAiBzD,kBAJtB,IAAbsD,GAAsB7e,QAAQkP,KAAK4P,OAtFnC7P,GAAM5J,GAAMK,GAAMqZ,GAAQnZ,GAAOoZ,GAAmBL,GAAiBD,GAAkBvP,GA+B1FR,GA9BA2Q,GACe,4BAyDf7S,GAAwB,CAAC3D,OAAO,CAACpO,EAAE,EAAGI,EAAE,EAAGC,EAAE,EAAGC,EAAE,EAAGoM,EAAE,EAAGmB,EAAE,IAmChD4W,+BAwIZ7D,UAAA,4BACQ7R,KAAKyK,OAAOoH,WAAU,GAAO7R,KAAK8V,OAAOlkB,GAAIoO,KAAK8V,OAAOjkB,gDAvIrDkkB,EAAehR,uBAAAA,IAAAA,EAAO,IAC5BwQ,IACJlQ,GAAUN,EAAKS,KAAM,OAKrB1C,EAAQ7M,EAAM+f,EAAOzgB,EAAQC,EAAQiK,EAAUnC,EAAK2Y,EAAWC,EAAcniB,EAAMsL,EAAQ8W,EAAaC,EAAkBC,EAHvHC,EAAahU,GAAe,OAC/BsC,EAAO5E,KACP8V,EAAS,CAAClkB,EAAE,EAAGC,EAAE,GAEdkkB,aAAyBvQ,GAAKF,KAAKiR,MAEtCzT,GADAmT,EAAYF,GACOS,UAAU,IAE7B1T,EAAS0C,GAAKiR,MAAMC,QAAQX,GAAe,GAC3CE,EAhDqB,SAAvBU,qBAAuB7T,WAClB8T,EAASpR,GAAKqR,YAAY/T,GAC7B5R,EAAI,EACEA,EAAI0lB,EAAOzlB,OAAQD,IAAK,IAC1B0lB,EAAO1lB,GAAG6T,KAAK0Q,kBACXmB,EAAO1lB,GACJ0lB,EAAO1lB,GAAG4lB,UACpBF,EAAOhiB,WAAPgiB,EAAeA,EAAO1lB,GAAG4lB,SAASC,gBAyCvBJ,CAAqB7T,IAElC7M,EAAO4e,GAAW9P,EAAK9O,KAAM6M,EAAQiC,QAChC+Q,OAASA,EACdrW,EAzGoB,SAArBuX,mBAAqBlU,OAChBmU,EAASnU,EAAOtB,wBACnB0V,EAAgB5B,GAAO6B,WAAavb,GAAKoG,aAAesT,GAAO3V,WAAaxD,GAAMwD,WAAa,GAC/FyX,EAAgB9B,GAAO+B,YAAczb,GAAKkG,aAAewT,GAAO5V,YAAcvD,GAAMuD,YAAc,SAC5F,CAACG,KAAKoX,EAAOpX,KAAOuX,EAAexX,IAAIqX,EAAOrX,IAAMsX,EAAeI,MAAOL,EAAOK,MAAQF,EAAeG,OAAQN,EAAOM,OAASL,GAqG5HF,CAAmBlU,GAC9BvN,EAASuE,WAAW0L,GAAK2P,YAAYrS,EAAQ,IAAK,OAClDtN,EAASsE,WAAW0L,GAAK2P,YAAYrS,EAAQ,IAAK,OAClDkT,EAASlT,EAAOnE,QAA2C,QAAjCmE,EAAO3F,QAAQC,cACrC6Y,IAAchgB,IACjBA,EAAO4e,GAAWoB,EAAUlR,KAAK0Q,WAAWxf,MAAQggB,EAAUlR,KAAK0Q,WAAY3S,EAAQmT,EAAUlR,KAAK0Q,aAEvGa,EAAWzZ,aAAa,QAAS,oBACjCyZ,EAAW7a,MAAMqC,QAAU,gPAC3BwY,EAAWkB,UAAY,mBACvBlB,EAAWmB,UAAY7S,GACtBY,GAAKiR,MAAMC,QAAQ3R,EAAK9G,WAAW,IAAM9B,IAAOS,YAAY0Z,GAtFxC,SAAtBoB,oBAAuB5U,EAAQ6U,EAAQC,GACtC9U,EAAOa,iBAAiB,QAAS,SAAAhG,MAC5BA,EAAEmF,OAAO2U,UAAW,KACnBnmB,EAAIqmB,EAAOha,EAAEmF,YACjBoC,GAAa9V,MAAQkC,IACZ4T,GAAaE,OAAQ,CAC7B7O,QAAQC,IAAIlF,GACZ4T,GAAazJ,MAAM0J,QAAU,QAC7BD,GAAaE,aAEZnJ,GAAK4b,YAAY,QACjB3S,GAAa4S,OACbF,GAAcA,EAAW9U,GACxB,MAAOiV,GACRxhB,QAAQkP,KAAK,uDAEdP,GAAazJ,MAAM0J,QAAU,WAuEhCuS,CAAoBpB,EAAY,kBAAM1R,EAAKiN,aAAa,kBAAMrM,GAAKwS,OAAO1B,EAAY,CAAC2B,gBAAgB,SAAU,CAACC,SAAS,GAAKD,gBAAgB,6BAChJ3a,EAAMrH,GAAQA,EAAKiH,kBA0BlB6H,EAAKoT,WAAa3S,GAAK4S,IAAIniB,EAAM,CAAC8W,OAAQhI,EAAKoT,YAC/CpT,EAAKsT,WAAa7S,GAAK4S,IAAIniB,EAAM,CAAC+W,YAAajI,EAAKsT,YACpDtT,EAAKuT,aAAe9S,GAAK4S,IAAIniB,EAAM,CAACsiB,QAASxT,EAAKuT,gBA1BlDpC,EAAgBF,GAASlT,EAAO5F,iBAAmB4F,EAAO5F,gBAAgBM,aAAa,UAAa,6BAChGwY,GACH1Y,EAAMwF,EAAO5F,gBACbnJ,EAAO+O,EAAO/D,UAEdxJ,GADA8J,EAxEsB,SAAzBwD,uBAAyBC,UAAWA,EAAOvG,UAAUyC,QAAQ+D,eAAiBC,IAAuB3D,OAwEzFwD,CAAuBC,IAChBnF,EAChBnI,EAAS6J,EAAOP,EAChBgX,EAAOlkB,EAAImC,EAAKnC,EAChBkkB,EAAOjkB,EAAIkC,EAAKlC,IAEhByL,EAAMgF,GAAe,MAAO4T,GAC5BG,GAAa,EACbla,GAAMS,YAAYU,GAClBA,EAAIT,aAAa,UAAW,eAC5BS,EAAIT,aAAa,QAAS,sBAC1BS,EAAI7B,MAAMqC,QAAU,qHAAuH2B,EAASG,IAAMpK,GAAU,aAAeiK,EAASI,KAAOtK,GAAU,OAE9MxB,EAAO6gB,GAAU3e,KAAU6e,GAAarB,KAAKxd,GAAQA,EAnIrC,SAAlBuiB,gBAAmB5mB,EAAGC,OAEpBX,EADGunB,EAAc,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,SAE/CvnB,EAAI,EAAGA,EAAIunB,EAAYtnB,OAAQD,GAAG,EACtCunB,EAAYvnB,IAAMU,EAClB6mB,EAAYvnB,EAAE,IAAMW,QAEd,IAAMD,EAAI,IAAMC,EAAI,IAAM4mB,EAAYlH,KAAK,KA4HWiH,CAAgBjjB,EAAQC,IACpFS,EAAOqM,GAAe,OAAQ4T,IACzBrZ,aAAa,IAAK9I,GACvBkC,EAAK4G,aAAa,gBAAiB,sBACnC5G,EAAKwF,MAAMqC,QAAU,mCAAqCiH,EAAKsT,WAAa,GAAK,aAAetT,EAAKoT,WAAa,QAAU,cAAgBpT,EAAKuT,aAAe,IAChKhb,EAAIV,YAAY3G,KAOb6f,EAAOlkB,GAAKkkB,EAAOjkB,IACtB2T,GAAK4S,IAAIniB,EAAM,CAACrE,EAAEkkB,EAAOlkB,EAAGC,EAAEikB,EAAOjkB,IAGhC,aAAckT,IACnBA,EAAKkO,UAAW,GAEX,eAAgBlO,IACrBA,EAAK0I,WAAa,SAAAhL,GACbA,EAAE7Q,EAAI6Q,EAAE7Q,EAAI6Q,EAAE5Q,EAAI4Q,EAAE5Q,EAAI,KAC3B4Q,EAAE7Q,EAAI6Q,EAAE5Q,EAAI,KAKfukB,EAAmBH,GAAaA,EAAU1X,QAAoC,WAA1B0X,EAAU1X,OAAO4U,KAAoB8C,EAAU1X,OAAOA,OAAS0X,EAEnHlR,EAAKwE,QAAU,WACd6M,EAAiBsC,MAAM,IAGxBvC,EAAc,uBAIbF,EAAU0C,aACVvC,EAAiBwC,WAElB7T,EAAKuF,UAAYvF,EAAKsL,eAAiB8F,OAElC1L,OAASyC,GAAWwG,OAAOzd,EAAM8O,GAClCA,EAAK8T,QACRrT,GAAK4S,IAAItV,EAAQ,CAACgW,gBAAgB,UAAWC,UAAU,GAAIC,UAAU,KAElE/C,GACCA,EAAUlR,KAAK0Q,WAAWxf,KAC7BggB,EAAUlR,KAAK0Q,WAAWxf,KAAOA,EAEjCggB,EAAUlR,KAAK0Q,WAAa,CAACxf,KAAKA,GAE/BmgB,EAAiB7X,SAAWiH,GAAKyT,gBACpCzT,GAAKyT,eAAeC,IAAI9C,EA/KV,SAAjB+C,eAAiBlD,WACZmD,EAAOnD,EAAUoD,YACdpD,GACNmD,EAAOnD,EAAUqD,YAAcF,GAAQnD,EAAUsD,aAAe,GAChEtD,EAAYA,EAAU1X,cAEhB6a,EAyKqCD,CAAe/C,GAAoBA,EAAiBoD,SAE/FpD,EAAiBqD,QAAQ,GAAGC,YAAY,IAGxCzD,EAAYG,EAAmB5Q,GAAKmU,GAAG7W,EAAQ,CAC9C2S,WAAY,CACXxf,KAAMA,EACNyE,MAAOqK,EAAKrK,OAAS,EACrBC,IAAM,QAASoK,EAAQA,EAAKpK,IAAM,EAClCif,WAAa,eAAgB7U,GAAQA,EAAK6U,WAC1CC,MAAO5jB,EACP6jB,YAAa/U,EAAK+U,aAEnB5B,SAAUnT,EAAKmT,UAAY,EAC3B6B,KAAMhV,EAAKgV,MAAQ,eACnBN,QAAQ,EACRC,YAAY,EACZM,QAAQjV,EAAK9O,YAGVggB,UAAYA,EAEjBvQ,GAAS1F,WACJsS,KAAOtS,KAAKuS,OAAS,WACzB0H,EAAKxP,OAAO6H,OACZgE,EAAW3a,YAAc2a,EAAW3a,WAAWqB,YAAYsZ,GAC3DD,GAAc/Y,EAAI3B,YAAc2B,EAAI3B,WAAWqB,YAAYM,GAC3D8Y,GAAoBA,EAAiB7D,UAUxCmD,GAAiBf,SAAWtP,GAC5BqQ,GAAiBhC,OAAS,SAAC5Q,EAAQiC,UAAS,IAAI2Q,GAAiB5S,EAAQiC,IACzE2Q,GAAiBwE,SAAW,SAACjkB,EAAM8O,UAASmI,GAAWwG,OAAOzd,EAAM8O,IACpE2Q,GAAiBhB,QAAU"}