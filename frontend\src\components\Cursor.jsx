import { useRef, useEffect } from 'react';
import gsap from 'gsap';
import { useGSAP } from '@gsap/react';
import cursorDefault from '../assets/CURSOR.svg';
import cursorHover from '../assets/CURSOR-HOVER.svg';
import cursorText from '../assets/CURSOR-TXT.svg';

const CustomCursor = () => {
  const cursorRef = useRef(null);
  const cursorDotRef = useRef(null);
  const cursorImageRef = useRef(null);
  const glowRef = useRef(null);
  
  useGSAP(() => {
    const cursor = cursorRef.current;
    const cursorDot = cursorDotRef.current;
    const cursorImage = cursorImageRef.current;
    const glow = glowRef.current;
    
    if (!cursor || !cursorDot || !cursorImage || !glow) return;
    
    // Hide default cursor
    document.body.style.cursor = 'none';
    
    // Set initial position
    gsap.set([cursor, cursorDot, glow], {
      xPercent: -50,
      yPercent: -50,
      scale: 0
    });
    
    // Animate cursor entrance with stagger
    const tl = gsap.timeline();
    tl.to(cursor, {
      scale: 1,
      duration: 0.4,
      ease: "back.out(1.7)"
    })
    .to(cursorDot, {
      scale: 1,
      duration: 0.3,
      ease: "power2.out"
    }, "-=0.2")
    .to(glow, {
      scale: 1,
      duration: 0.5,
      ease: "power2.out"
    }, "-=0.3");
    
    // Mouse move handler with smooth following
    const handleMouseMove = (e) => {
      const { clientX, clientY } = e;
      
      // Main cursor follows immediately
      gsap.to(cursor, {
        x: clientX,
        y: clientY,
        duration: 0.1,
        ease: "power2.out"
      });
      
      // Dot follows with delay for trailing effect
      gsap.to(cursorDot, {
        x: clientX,
        y: clientY,
        duration: 0.4,
        ease: "power2.out"
      });
      
      // Glow follows with more delay
      gsap.to(glow, {
        x: clientX,
        y: clientY,
        duration: 0.8,
        ease: "power2.out"
      });
    };
    
    // Hover effects for different elements
    const handleMouseEnter = (e) => {
      const target = e.target;
      const isClickable = target.tagName === 'A' || 
                         target.tagName === 'BUTTON' || 
                         target.classList.contains('cursor-hover') ||
                         target.closest('button') ||
                         target.closest('a');
      
      const isText = target.tagName === 'H1' || 
                    target.tagName === 'H2' || 
                    target.tagName === 'H3' ||
                    target.tagName === 'P' || 
                    target.classList.contains('cursor-text');
      
      if (isClickable) {
        // Hover state for clickable elements
        cursorImage.src = cursorHover;
        
        gsap.to(cursor, {
          scale: 1.8,
          rotation: 300,
          x:"+=10",
          duration: 0.4,
          ease: "back.out(1.7)"
        });
        
        gsap.to(cursorDot, {
          scale: 0.3,
          duration: 0.3,
          ease: "power2.out"
        });
        
        gsap.to(glow, {
          scale: 1.5,
          opacity: 0.6,
          duration: 0.3,
          ease: "power2.out"
        });
        
      } else if (isText) {
        // Text cursor for text elements
        cursorImage.src = cursorText;
        
        gsap.to(cursor, {
          scale: 1.3,
          rotation: 15,
          duration: 0.3,
          ease: "power2.out"
        });
        
        gsap.to(cursorDot, {
          scale: 0.8,
          duration: 0.2,
          ease: "power2.out"
        });
      }
    };
    
    // Reset cursor on mouse leave
    const handleMouseLeave = (e) => {
      const target = e.target;
      const isSpecialElement = target.tagName === 'A' || 
                              target.tagName === 'BUTTON' || 
                              target.classList.contains('cursor-hover') ||
                              target.tagName === 'H1' || 
                              target.tagName === 'H2' || 
                              target.tagName === 'H3' ||
                              target.tagName === 'P' || 
                              target.classList.contains('cursor-text') ||
                              target.closest('button') ||
                              target.closest('a');
      
      if (isSpecialElement) {
        // Reset to default cursor
        cursorImage.src = cursorDefault;
        
        gsap.to(cursor, {
          scale: 1,
          rotation: 0,
          duration: 0.4,
          ease: "back.out(1.7)"
        });
        
        gsap.to(cursorDot, {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
        
        gsap.to(glow, {
          scale: 1,
          opacity: 0.3,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    };
    
    // Click effects
    const handleMouseDown = () => {
      gsap.to(cursor, {
        scale: 0.7,
        duration: 0.1,
        ease: "power2.out"
      });
      
      gsap.to(cursorDot, {
        scale: 1.8,
        duration: 0.1,
        ease: "power2.out"
      });
    };
    
    const handleMouseUp = () => {
      gsap.to(cursor, {
        scale: 1,
        duration: 0.3,
        ease: "back.out(1.7)"
      });
      
      gsap.to(cursorDot, {
        scale: 1,
        duration: 0.2,
        ease: "power2.out"
      });
    };
    
    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseenter', handleMouseEnter, true);
    document.addEventListener('mouseleave', handleMouseLeave, true);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);
    
    // Cleanup function
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseenter', handleMouseEnter, true);
      document.removeEventListener('mouseleave', handleMouseLeave, true);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'auto';
    };
  }, []);
  
  // Handle window focus/blur
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        gsap.to([cursorRef.current, cursorDotRef.current, glowRef.current], {
          scale: 0,
          duration: 0.2,
          ease: "power2.out"
        });
      } else {
        gsap.to([cursorRef.current, cursorDotRef.current, glowRef.current], {
          scale: 1,
          duration: 0.4,
          ease: "back.out(1.7)",
          stagger: 0.1
        });
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return (
    <>
      {/* Main cursor with image */}
      <div
        ref={cursorRef}
        className="fixed top-0 left-0 pointer-events-none z-[9999]"
        style={{
          width: '32px',
          height: '32px',
          mixBlendMode: 'difference',
        }}
      >
        <img
          ref={cursorImageRef}
          src={cursorDefault}
          alt="cursor"
          className="w-full h-full object-contain filter invert brightness-0 contrast-200"
          style={{
            imageRendering: 'crisp-edges',
          }}
        />
      </div>
      
      {/* Trailing dot */}
      <div
        ref={cursorDotRef}
        className="fixed top-0 left-0 pointer-events-none z-[9998]"
        style={{
          width: '6px',
          height: '6px',
          backgroundColor: '#ffffff',
          borderRadius: '50%',
          mixBlendMode: 'difference',
        }}
      />
      
      {/* Cursor glow effect */}
      <div
        ref={glowRef}
        className="fixed top-0 left-0 pointer-events-none z-[9997]"
        style={{
          width: '80px',
          height: '80px',
          background: 'radial-gradient(circle, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 50%, transparent 70%)',
          borderRadius: '50%',
          opacity: 0.3,
        }}
      />
    </>
  );
};

export default CustomCursor;
