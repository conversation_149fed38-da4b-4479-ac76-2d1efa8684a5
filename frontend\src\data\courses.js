export const courses = [
  {
    id: 1,
    title: "Complete React Development Course",
    subtitle: "Master Modern React Development",
    description: "Master React from basics to advanced concepts including hooks, context, and state management.",
    fullDescription: "This comprehensive React course covers everything you need to know to become a proficient React developer. Starting from the fundamentals of JSX and components, you'll progress through advanced topics like hooks, context API, state management with Redux, and modern React patterns. The course includes hands-on projects, real-world examples, and best practices used in the industry.",
    instructor: {
      name: "<PERSON>",
      title: "Senior Frontend Developer at Google",
      bio: "Senior Frontend Developer with 8+ years of experience at top tech companies. Specialized in React ecosystem and modern JavaScript.",
      image: "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      experience: "8+ years"
    },
    pricing: {
      current: 99,
      original: 149,
      discount: 34,
      emi: 16
    },
    thumbnail: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=250&fit=crop",
    banner: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=400&fit=crop",
    category: "Frontend",
    duration: "12 hours",
    lessons: 45,
    level: "Beginner to Advanced",
    rating: 4.8,
    students: 2340,
    stats: {
      students: "2340+",
      duration: "12 hours",
      projects: "8+",
      placement: "92%"
    },
    features: [
      "Live Interactive Classes",
      "1:1 Doubt Sessions",
      "Real Industry Projects",
      "Placement Assistance",
      "Lifetime Access",
      "Certificate of Completion"
    ],
    curriculum: [
      {
        module: "React Fundamentals",
        duration: "2 weeks",
        topics: [
          "Introduction to React and JSX",
          "Components and Props",
          "State and Event Handling",
          "React Developer Tools"
        ]
      },
      {
        module: "Advanced React Concepts",
        duration: "3 weeks",
        topics: [
          "React Hooks (useState, useEffect, useContext)",
          "Custom Hooks Development",
          "Context API and State Management",
          "Performance Optimization"
        ]
      },
      {
        module: "React Ecosystem",
        duration: "2 weeks",
        topics: [
          "Routing with React Router",
          "State Management with Redux",
          "API Integration and Async Operations",
          "Testing React Applications"
        ]
      },
      {
        module: "Production & Deployment",
        duration: "1 week",
        topics: [
          "Build Optimization",
          "Deployment Strategies",
          "Production Best Practices",
          "Monitoring and Analytics"
        ]
      }
    ],
    faqs: [
      {
        question: "Is this course suitable for beginners?",
        answer: "Yes! This course starts from the basics and gradually builds up to advanced concepts. No prior React experience required."
      },
      {
        question: "What projects will I build?",
        answer: "You'll build 8+ real-world projects including a social media app, e-commerce platform, and portfolio website."
      },
      {
        question: "Do I get lifetime access?",
        answer: "Yes, you get lifetime access to all course materials, including future updates and new content."
      }
    ]
  },
  {
    id: "dsa-domination-cohort",
    title: "DSA Domination Cohort",
    subtitle: "Zero To Job-Ready In 6 Months",
    description: "Master Data Structures & Algorithms with live mentorship, real projects, and guaranteed placement support.",
    fullDescription: "This comprehensive DSA course is designed to take you from zero to job-ready in just 6 months. With live interactive classes, personalized mentorship, and real industry projects, you'll master all essential data structures and algorithms needed for top tech companies.",
    instructor: {
      name: "Harsh Sharma",
      title: "Senior Software Engineer at Google",
      bio: "Senior Software Engineer at Google with 5+ years of experience. Expert in competitive programming and system design.",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      experience: "5+ years"
    },
    pricing: {
      current: 7999,
      original: 15999,
      discount: 50,
      emi: 1333
    },
    thumbnail: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=250&fit=crop",
    banner: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=800&h=400&fit=crop",
    category: "Programming",
    duration: "6 months",
    lessons: 120,
    level: "Beginner to Advanced",
    rating: 4.9,
    students: 2000,
    stats: {
      students: "2000+",
      duration: "6 months",
      projects: "15+",
      placement: "95%"
    },
    features: [
      "Live Interactive Classes",
      "1:1 Doubt Sessions",
      "Real Industry Projects",
      "Placement Assistance",
      "Lifetime Access",
      "Certificate of Completion"
    ],
    curriculum: [
      {
        module: "Foundation & Basics",
        duration: "2 weeks",
        topics: [
          "Programming Fundamentals",
          "Time & Space Complexity",
          "Basic Problem Solving",
          "Coding Environment Setup"
        ]
      },
      {
        module: "Arrays & Strings",
        duration: "3 weeks",
        topics: [
          "Array Manipulation",
          "Two Pointer Technique",
          "Sliding Window",
          "String Algorithms"
        ]
      },
      {
        module: "Linked Lists & Stacks",
        duration: "2 weeks",
        topics: [
          "Singly & Doubly Linked Lists",
          "Stack Implementation",
          "Queue Implementation",
          "Deque Operations"
        ]
      },
      {
        module: "Trees & Graphs",
        duration: "4 weeks",
        topics: [
          "Binary Trees & BST",
          "Tree Traversals",
          "Graph Representations",
          "BFS & DFS Algorithms"
        ]
      },
      {
        module: "Dynamic Programming",
        duration: "3 weeks",
        topics: [
          "DP Fundamentals",
          "Memoization Techniques",
          "Classic DP Problems",
          "Optimization Strategies"
        ]
      },
      {
        module: "System Design & Projects",
        duration: "4 weeks",
        topics: [
          "System Design Basics",
          "Scalability Concepts",
          "Real-world Projects",
          "Interview Preparation"
        ]
      }
    ],
    faqs: [
      {
        question: "Is this course suitable for beginners?",
        answer: "Yes! This course is designed for complete beginners. We start from the very basics and gradually build up to advanced concepts."
      },
      {
        question: "What programming language will be used?",
        answer: "We primarily use Java and Python for implementations, but concepts are language-agnostic and can be applied to any programming language."
      },
      {
        question: "Do you provide placement assistance?",
        answer: "Yes, we provide comprehensive placement assistance including resume building, mock interviews, and direct referrals to partner companies."
      },
      {
        question: "Can I access the course content after completion?",
        answer: "Absolutely! You get lifetime access to all course materials, including future updates and additional content."
      },
      {
        question: "What if I miss a live class?",
        answer: "All live classes are recorded and available within 24 hours. You can catch up at your own pace."
      }
    ]
  },
  {
    id: 2,
    title: "Node.js Backend Mastery",
    description: "Build scalable backend applications with Node.js, Express, and MongoDB.",
    fullDescription: "Learn to build robust, scalable backend applications using Node.js and Express. This course covers server-side JavaScript, RESTful API development, database integration with MongoDB, authentication, security best practices, and deployment strategies.",
    instructor: "Sarah Johnson",
    instructorBio: "Full-stack developer and tech lead with expertise in Node.js, microservices, and cloud architecture.",
    price: 129,
    originalPrice: 199,
    thumbnail: "https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=250&fit=crop",
    banner: "https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=800&h=400&fit=crop",
    category: "Backend",
    duration: "15 hours",
    lessons: 52,
    level: "Intermediate",
    rating: 4.9,
    students: 1890,
    syllabus: [
      "Node.js Fundamentals",
      "Express.js Framework",
      "RESTful API Design",
      "MongoDB and Mongoose",
      "Authentication and Authorization",
      "Error Handling and Validation",
      "File Upload and Processing",
      "Security Best Practices",
      "Testing with Jest",
      "Deployment and DevOps"
    ]
  },
  {
    id: 3,
    title: "Python Data Science Bootcamp",
    description: "Complete data science course covering Python, pandas, NumPy, and machine learning.",
    fullDescription: "Dive into the world of data science with Python. This comprehensive bootcamp covers data manipulation with pandas, numerical computing with NumPy, data visualization with matplotlib and seaborn, and machine learning with scikit-learn.",
    instructor: "Dr. Michael Chen",
    instructorBio: "Data Scientist with PhD in Statistics. 10+ years experience in machine learning and data analytics.",
    price: 149,
    originalPrice: 249,
    thumbnail: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=250&fit=crop",
    banner: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop",
    category: "Data Science",
    duration: "20 hours",
    lessons: 68,
    level: "Beginner to Intermediate",
    rating: 4.7,
    students: 3120,
    syllabus: [
      "Python Fundamentals for Data Science",
      "NumPy for Numerical Computing",
      "Pandas for Data Manipulation",
      "Data Visualization with Matplotlib",
      "Advanced Plotting with Seaborn",
      "Statistical Analysis",
      "Introduction to Machine Learning",
      "Supervised Learning Algorithms",
      "Unsupervised Learning",
      "Model Evaluation and Deployment"
    ]
  },
  {
    id: 4,
    title: "UI/UX Design Fundamentals",
    description: "Learn design principles, user research, prototyping, and modern design tools.",
    fullDescription: "Master the art and science of user experience design. This course covers design thinking, user research methodologies, wireframing, prototyping, and using industry-standard tools like Figma and Adobe XD.",
    instructor: "Emma Wilson",
    instructorBio: "Senior UX Designer at a Fortune 500 company. Expert in user-centered design and design systems.",
    price: 89,
    originalPrice: 139,
    thumbnail: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=250&fit=crop",
    banner: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=800&h=400&fit=crop",
    category: "Design",
    duration: "10 hours",
    lessons: 35,
    level: "Beginner",
    rating: 4.6,
    students: 1560,
    syllabus: [
      "Introduction to UX/UI Design",
      "Design Thinking Process",
      "User Research Methods",
      "Information Architecture",
      "Wireframing and Prototyping",
      "Visual Design Principles",
      "Color Theory and Typography",
      "Figma Mastery",
      "Usability Testing",
      "Design Systems and Style Guides"
    ]
  },
  {
    id: 5,
    title: "DevOps and Cloud Computing",
    description: "Master Docker, Kubernetes, AWS, and CI/CD pipelines for modern deployment.",
    fullDescription: "Learn modern DevOps practices and cloud computing with hands-on experience in containerization, orchestration, and cloud platforms. This course covers Docker, Kubernetes, AWS services, and building robust CI/CD pipelines.",
    instructor: "Alex Rodriguez",
    instructorBio: "DevOps Engineer and Cloud Architect with expertise in AWS, Docker, and Kubernetes. 7+ years in infrastructure automation.",
    price: 159,
    originalPrice: 229,
    thumbnail: "https://images.unsplash.com/photo-1667372393119-3d4c48d07fc9?w=400&h=250&fit=crop",
    banner: "https://images.unsplash.com/photo-1667372393119-3d4c48d07fc9?w=800&h=400&fit=crop",
    category: "DevOps",
    duration: "18 hours",
    lessons: 55,
    level: "Intermediate to Advanced",
    rating: 4.8,
    students: 980,
    syllabus: [
      "Introduction to DevOps Culture",
      "Version Control with Git",
      "Containerization with Docker",
      "Container Orchestration with Kubernetes",
      "AWS Cloud Services",
      "Infrastructure as Code",
      "CI/CD Pipeline Design",
      "Monitoring and Logging",
      "Security in DevOps",
      "Scaling and Performance Optimization"
    ]
  },
  {
    id: 6,
    title: "Mobile App Development with React Native",
    description: "Build cross-platform mobile apps for iOS and Android using React Native.",
    fullDescription: "Create beautiful, performant mobile applications for both iOS and Android using React Native. This course covers navigation, state management, native modules, and publishing to app stores.",
    instructor: "Lisa Park",
    instructorBio: "Mobile App Developer with 6+ years experience. Published 15+ apps on both iOS and Android app stores.",
    price: 119,
    originalPrice: 179,
    thumbnail: "https://images.unsplash.com/photo-*************-90a1b58e7e9c?w=400&h=250&fit=crop",
    banner: "https://images.unsplash.com/photo-*************-90a1b58e7e9c?w=800&h=400&fit=crop",
    category: "Mobile",
    duration: "14 hours",
    lessons: 48,
    level: "Intermediate",
    rating: 4.7,
    students: 1420,
    syllabus: [
      "React Native Fundamentals",
      "Navigation and Routing",
      "Styling and Layouts",
      "State Management",
      "API Integration",
      "Native Device Features",
      "Push Notifications",
      "Performance Optimization",
      "Testing Mobile Apps",
      "Publishing to App Stores"
    ]
  }
];

export const categories = [
  "All",
  "Frontend",
  "Backend",
  "Data Science",
  "Design",
  "DevOps",
  "Mobile",
  "Programming"
];

export const testimonials = [
  {
    id: 1,
    name: "David Thompson",
    role: "Software Engineer at Google",
    content: "The courses here are incredibly well-structured and practical. I landed my dream job after completing the React course!",
    avatar: "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
    rating: 5
  },
  {
    id: 2,
    name: "Maria Garcia",
    role: "Data Scientist at Microsoft",
    content: "The Python Data Science bootcamp gave me all the skills I needed to transition into data science. Highly recommended!",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
    rating: 5
  },
  {
    id: 3,
    name: "James Wilson",
    role: "UX Designer at Airbnb",
    content: "Amazing instructors and hands-on projects. The UI/UX course helped me build a strong portfolio and get hired.",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    rating: 5
  }
];
