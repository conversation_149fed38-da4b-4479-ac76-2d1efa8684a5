{"version": 3, "file": "ScrollTrigger.min.js", "sources": ["../src/Observer.js", "../src/ScrollTrigger.js"], "sourcesContent": ["/*!\n * Observer 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _clamp, _win, _doc, _docEl, _body, _isTouch, _pointerType, ScrollTrigger, _root, _normalizer, _eventTypes, _context,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_startup = 1,\n\t_observers = [],\n\t_scrollers = [],\n\t_proxies = [],\n\t_getTime = Date.now,\n\t_bridge = (name, value) => value,\n\t_integrate = () => {\n\t\tlet core = ScrollTrigger.core,\n\t\t\tdata = core.bridge || {},\n\t\t\tscrollers = core._scrollers,\n\t\t\tproxies = core._proxies;\n\t\tscrollers.push(..._scrollers);\n\t\tproxies.push(..._proxies);\n\t\t_scrollers = scrollers;\n\t\t_proxies = proxies;\n\t\t_bridge = (name, value) => data[name](value);\n\t},\n\t_getProxyProp = (element, property) => ~_proxies.indexOf(element) && _proxies[_proxies.indexOf(element) + 1][property],\n\t_isViewport = el => !!~_root.indexOf(el),\n\t_addListener = (element, type, func, passive, capture) => element.addEventListener(type, func, {passive: passive !== false, capture: !!capture}),\n\t_removeListener = (element, type, func, capture) => element.removeEventListener(type, func, !!capture),\n\t_scrollLeft = \"scrollLeft\",\n\t_scrollTop = \"scrollTop\",\n\t_onScroll = () => (_normalizer && _normalizer.isPressed) || _scrollers.cache++,\n\t_scrollCacheFunc = (f, doNotCache) => {\n\t\tlet cachingFunc = value => { // since reading the scrollTop/scrollLeft/pageOffsetY/pageOffsetX can trigger a layout, this function allows us to cache the value so it only gets read fresh after a \"scroll\" event fires (or while we're refreshing because that can lengthen the page and alter the scroll position). when \"soft\" is true, that means don't actually set the scroll, but cache the new value instead (useful in ScrollSmoother)\n\t\t\tif (value || value === 0) {\n\t\t\t\t_startup && (_win.history.scrollRestoration = \"manual\"); // otherwise the new position will get overwritten by the browser onload.\n\t\t\t\tlet isNormalizing = _normalizer && _normalizer.isPressed;\n\t\t\t\tvalue = cachingFunc.v = Math.round(value) || (_normalizer && _normalizer.iOS ? 1 : 0); //TODO: iOS Bug: if you allow it to go to 0, Safari can start to report super strange (wildly inaccurate) touch positions!\n\t\t\t\tf(value);\n\t\t\t\tcachingFunc.cacheID = _scrollers.cache;\n\t\t\t\tisNormalizing && _bridge(\"ss\", value); // set scroll (notify ScrollTrigger so it can dispatch a \"scrollStart\" event if necessary\n\t\t\t} else if (doNotCache || _scrollers.cache !== cachingFunc.cacheID || _bridge(\"ref\")) {\n\t\t\t\tcachingFunc.cacheID = _scrollers.cache;\n\t\t\t\tcachingFunc.v = f();\n\t\t\t}\n\t\t\treturn cachingFunc.v + cachingFunc.offset;\n\t\t};\n\t\tcachingFunc.offset = 0;\n\t\treturn f && cachingFunc;\n\t},\n\t_horizontal = {s: _scrollLeft, p: \"left\", p2: \"Left\", os: \"right\", os2: \"Right\", d: \"width\", d2: \"Width\", a: \"x\", sc: _scrollCacheFunc(function(value) { return arguments.length ? _win.scrollTo(value, _vertical.sc()) : _win.pageXOffset || _doc[_scrollLeft] || _docEl[_scrollLeft] || _body[_scrollLeft] || 0})},\n\t_vertical = {s: _scrollTop, p: \"top\", p2: \"Top\", os: \"bottom\", os2: \"Bottom\", d: \"height\", d2: \"Height\", a: \"y\", op: _horizontal, sc: _scrollCacheFunc(function(value) { return arguments.length ? _win.scrollTo(_horizontal.sc(), value) : _win.pageYOffset || _doc[_scrollTop] || _docEl[_scrollTop] || _body[_scrollTop] || 0})},\n\t_getTarget = (t, self) => ((self && self._ctx && self._ctx.selector) || gsap.utils.toArray)(t)[0] || (typeof(t) === \"string\" && gsap.config().nullTargetWarn !== false ? console.warn(\"Element not found:\", t) : null),\n\n\t_isWithin = (element, list) => { // check if the element is in the list or is a descendant of an element in the list.\n\t\tlet i = list.length;\n\t\twhile (i--) {\n\t\t\tif (list[i] === element || list[i].contains(element)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t},\n\t_getScrollFunc = (element, {s, sc}) => { // we store the scroller functions in an alternating sequenced Array like [element, verticalScrollFunc, horizontalScrollFunc, ...] so that we can minimize memory, maximize performance, and we also record the last position as a \".rec\" property in order to revert to that after refreshing to ensure things don't shift around.\n\t\t_isViewport(element) && (element = _doc.scrollingElement || _docEl);\n\t\tlet i = _scrollers.indexOf(element),\n\t\t\toffset = sc === _vertical.sc ? 1 : 2;\n\t\t!~i && (i = _scrollers.push(element) - 1);\n\t\t_scrollers[i + offset] || _addListener(element, \"scroll\", _onScroll); // clear the cache when a scroll occurs\n\t\tlet prev = _scrollers[i + offset],\n\t\t\tfunc = prev || (_scrollers[i + offset] = _scrollCacheFunc(_getProxyProp(element, s), true) || (_isViewport(element) ? sc : _scrollCacheFunc(function(value) { return arguments.length ? (element[s] = value) : element[s]; })));\n\t\tfunc.target = element;\n\t\tprev || (func.smooth = gsap.getProperty(element, \"scrollBehavior\") === \"smooth\"); // only set it the first time (don't reset every time a scrollFunc is requested because perhaps it happens during a refresh() when it's disabled in ScrollTrigger.\n\t\treturn func;\n\t},\n\t_getVelocityProp = (value, minTimeRefresh, useDelta) => {\n\t\tlet v1 = value,\n\t\t\tv2 = value,\n\t\t\tt1 = _getTime(),\n\t\t\tt2 = t1,\n\t\t\tmin = minTimeRefresh || 50,\n\t\t\tdropToZeroTime = Math.max(500, min * 3),\n\t\t\tupdate = (value, force) => {\n\t\t\t\tlet t = _getTime();\n\t\t\t\tif (force || t - t1 > min) {\n\t\t\t\t\tv2 = v1;\n\t\t\t\t\tv1 = value;\n\t\t\t\t\tt2 = t1;\n\t\t\t\t\tt1 = t;\n\t\t\t\t} else if (useDelta) {\n\t\t\t\t\tv1 += value;\n\t\t\t\t} else { // not totally necessary, but makes it a bit more accurate by adjusting the v1 value according to the new slope. This way we're not just ignoring the incoming data. Removing for now because it doesn't seem to make much practical difference and it's probably not worth the kb.\n\t\t\t\t\tv1 = v2 + (value - v2) / (t - t2) * (t1 - t2);\n\t\t\t\t}\n\t\t\t},\n\t\t\treset = () => { v2 = v1 = useDelta ? 0 : v1; t2 = t1 = 0; },\n\t\t\tgetVelocity = latestValue => {\n\t\t\t\tlet tOld = t2,\n\t\t\t\t\tvOld = v2,\n\t\t\t\t\tt = _getTime();\n\t\t\t\t(latestValue || latestValue === 0) && latestValue !== v1 && update(latestValue);\n\t\t\t\treturn (t1 === t2 || t - t2 > dropToZeroTime) ? 0 : (v1 + (useDelta ? vOld : -vOld)) / ((useDelta ? t : t1) - tOld) * 1000;\n\t\t\t};\n\t\treturn {update, reset, getVelocity};\n\t},\n\t_getEvent = (e, preventDefault) => {\n\t\tpreventDefault && !e._gsapAllow && e.preventDefault();\n\t\treturn e.changedTouches ? e.changedTouches[0] : e;\n\t},\n\t_getAbsoluteMax = a => {\n\t\tlet max = Math.max(...a),\n\t\t\tmin = Math.min(...a);\n\t\treturn Math.abs(max) >= Math.abs(min) ? max : min;\n\t},\n\t_setScrollTrigger = () => {\n\t\tScrollTrigger = gsap.core.globals().ScrollTrigger;\n\t\tScrollTrigger && ScrollTrigger.core && _integrate();\n\t},\n\t_initCore = core => {\n\t\tgsap = core || _getGSAP();\n\t\tif (!_coreInitted && gsap && typeof(document) !== \"undefined\" && document.body) {\n\t\t\t_win = window;\n\t\t\t_doc = document;\n\t\t\t_docEl = _doc.documentElement;\n\t\t\t_body = _doc.body;\n\t\t\t_root = [_win, _doc, _docEl, _body];\n\t\t\t_clamp = gsap.utils.clamp;\n\t\t\t_context = gsap.core.context || function() {};\n\t\t\t_pointerType = \"onpointerenter\" in _body ? \"pointer\" : \"mouse\";\n\t\t\t// isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\t\t\t_isTouch = Observer.isTouch = _win.matchMedia && _win.matchMedia(\"(hover: none), (pointer: coarse)\").matches ? 1 : (\"ontouchstart\" in _win || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0) ? 2 : 0;\n\t\t\t_eventTypes = Observer.eventTypes = (\"ontouchstart\" in _docEl ? \"touchstart,touchmove,touchcancel,touchend\" : !(\"onpointerdown\" in _docEl) ? \"mousedown,mousemove,mouseup,mouseup\" : \"pointerdown,pointermove,pointercancel,pointerup\").split(\",\");\n\t\t\tsetTimeout(() => _startup = 0, 500);\n\t\t\t_setScrollTrigger();\n\t\t\t_coreInitted = 1;\n\t\t}\n\t\treturn _coreInitted;\n\t};\n\n_horizontal.op = _vertical;\n_scrollers.cache = 0;\n\nexport class Observer {\n\tconstructor(vars) {\n\t\tthis.init(vars);\n\t}\n\n\tinit(vars) {\n\t\t_coreInitted || _initCore(gsap) || console.warn(\"Please gsap.registerPlugin(Observer)\");\n\t\tScrollTrigger || _setScrollTrigger();\n\t\tlet {tolerance, dragMinimum, type, target, lineHeight, debounce, preventDefault, onStop, onStopDelay, ignore, wheelSpeed, event, onDragStart, onDragEnd, onDrag, onPress, onRelease, onRight, onLeft, onUp, onDown, onChangeX, onChangeY, onChange, onToggleX, onToggleY, onHover, onHoverEnd, onMove, ignoreCheck, isNormalizer, onGestureStart, onGestureEnd, onWheel, onEnable, onDisable, onClick, scrollSpeed, capture, allowClicks, lockAxis, onLockAxis} = vars;\n\t\tthis.target = target = _getTarget(target) || _docEl;\n\t\tthis.vars = vars;\n\t\tignore && (ignore = gsap.utils.toArray(ignore));\n\t\ttolerance = tolerance || 1e-9;\n\t\tdragMinimum = dragMinimum || 0;\n\t\twheelSpeed = wheelSpeed || 1;\n\t\tscrollSpeed = scrollSpeed || 1;\n\t\ttype = type || \"wheel,touch,pointer\";\n\t\tdebounce = debounce !== false;\n\t\tlineHeight || (lineHeight = parseFloat(_win.getComputedStyle(_body).lineHeight) || 22); // note: browser may report \"normal\", so default to 22.\n\t\tlet id, onStopDelayedCall, dragged, moved, wheeled, locked, axis,\n\t\t\tself = this,\n\t\t\tprevDeltaX = 0,\n\t\t\tprevDeltaY = 0,\n\t\t\tpassive = vars.passive || (!preventDefault && vars.passive !== false),\n\t\t\tscrollFuncX = _getScrollFunc(target, _horizontal),\n\t\t\tscrollFuncY = _getScrollFunc(target, _vertical),\n\t\t\tscrollX = scrollFuncX(),\n\t\t\tscrollY = scrollFuncY(),\n\t\t\tlimitToTouch = ~type.indexOf(\"touch\") && !~type.indexOf(\"pointer\") && _eventTypes[0] === \"pointerdown\", // for devices that accommodate mouse events and touch events, we need to distinguish.\n\t\t\tisViewport = _isViewport(target),\n\t\t\townerDoc = target.ownerDocument || _doc,\n\t\t\tdeltaX = [0, 0, 0], // wheel, scroll, pointer/touch\n\t\t\tdeltaY = [0, 0, 0],\n\t\t\tonClickTime = 0,\n\t\t\tclickCapture = () => onClickTime = _getTime(),\n\t\t\t_ignoreCheck = (e, isPointerOrTouch) => (self.event = e) && (ignore && _isWithin(e.target, ignore)) || (isPointerOrTouch && limitToTouch && e.pointerType !== \"touch\") || (ignoreCheck && ignoreCheck(e, isPointerOrTouch)),\n\t\t\tonStopFunc = () => {\n\t\t\t\tself._vx.reset();\n\t\t\t\tself._vy.reset();\n\t\t\t\tonStopDelayedCall.pause();\n\t\t\t\tonStop && onStop(self);\n\t\t\t},\n\t\t\tupdate = () => {\n\t\t\t\tlet dx = self.deltaX = _getAbsoluteMax(deltaX),\n\t\t\t\t\tdy = self.deltaY = _getAbsoluteMax(deltaY),\n\t\t\t\t\tchangedX = Math.abs(dx) >= tolerance,\n\t\t\t\t\tchangedY = Math.abs(dy) >= tolerance;\n\t\t\t\tonChange && (changedX || changedY) && onChange(self, dx, dy, deltaX, deltaY); // in ScrollTrigger.normalizeScroll(), we need to know if it was touch/pointer so we need access to the deltaX/deltaY Arrays before we clear them out.\n\t\t\t\tif (changedX) {\n\t\t\t\t\tonRight && self.deltaX > 0 && onRight(self);\n\t\t\t\t\tonLeft && self.deltaX < 0 && onLeft(self);\n\t\t\t\t\tonChangeX && onChangeX(self);\n\t\t\t\t\tonToggleX && ((self.deltaX < 0) !== (prevDeltaX < 0)) && onToggleX(self);\n\t\t\t\t\tprevDeltaX = self.deltaX;\n\t\t\t\t\tdeltaX[0] = deltaX[1] = deltaX[2] = 0\n\t\t\t\t}\n\t\t\t\tif (changedY) {\n\t\t\t\t\tonDown && self.deltaY > 0 && onDown(self);\n\t\t\t\t\tonUp && self.deltaY < 0 && onUp(self);\n\t\t\t\t\tonChangeY && onChangeY(self);\n\t\t\t\t\tonToggleY && ((self.deltaY < 0) !== (prevDeltaY < 0)) && onToggleY(self);\n\t\t\t\t\tprevDeltaY = self.deltaY;\n\t\t\t\t\tdeltaY[0] = deltaY[1] = deltaY[2] = 0\n\t\t\t\t}\n\t\t\t\tif (moved || dragged) {\n\t\t\t\t\tonMove && onMove(self);\n\t\t\t\t\tif (dragged) {\n\t\t\t\t\t\tonDragStart && dragged === 1 && onDragStart(self);\n\t\t\t\t\t\tonDrag && onDrag(self);\n\t\t\t\t\t\tdragged = 0;\n\t\t\t\t\t}\n\t\t\t\t\tmoved = false;\n\t\t\t\t}\n\t\t\t\tlocked && !(locked = false) && onLockAxis && onLockAxis(self);\n\t\t\t\tif (wheeled) {\n\t\t\t\t\tonWheel(self);\n\t\t\t\t\twheeled = false;\n\t\t\t\t}\n\t\t\t\tid = 0;\n\t\t\t},\n\t\t\tonDelta = (x, y, index) => {\n\t\t\t\tdeltaX[index] += x;\n\t\t\t\tdeltaY[index] += y;\n\t\t\t\tself._vx.update(x);\n\t\t\t\tself._vy.update(y);\n\t\t\t\tdebounce ? id || (id = requestAnimationFrame(update)) : update();\n\t\t\t},\n\t\t\tonTouchOrPointerDelta = (x, y) => {\n\t\t\t\tif (lockAxis && !axis) {\n\t\t\t\t\tself.axis = axis = Math.abs(x) > Math.abs(y) ? \"x\" : \"y\";\n\t\t\t\t\tlocked = true;\n\t\t\t\t}\n\t\t\t\tif (axis !== \"y\") {\n\t\t\t\t\tdeltaX[2] += x;\n\t\t\t\t\tself._vx.update(x, true); // update the velocity as frequently as possible instead of in the debounced function so that very quick touch-scrolls (flicks) feel natural. If it's the mouse/touch/pointer, force it so that we get snappy/accurate momentum scroll.\n\t\t\t\t}\n\t\t\t\tif (axis !== \"x\") {\n\t\t\t\t\tdeltaY[2] += y;\n\t\t\t\t\tself._vy.update(y, true);\n\t\t\t\t}\n\t\t\t\tdebounce ? id || (id = requestAnimationFrame(update)) : update();\n\t\t\t},\n\t\t\t_onDrag = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\te = _getEvent(e, preventDefault);\n\t\t\t\tlet x = e.clientX,\n\t\t\t\t\ty = e.clientY,\n\t\t\t\t\tdx = x - self.x,\n\t\t\t\t\tdy = y - self.y,\n\t\t\t\t\tisDragging = self.isDragging;\n\t\t\t\tself.x = x;\n\t\t\t\tself.y = y;\n\t\t\t\tif (isDragging || ((dx || dy) && (Math.abs(self.startX - x) >= dragMinimum || Math.abs(self.startY - y) >= dragMinimum))) {\n\t\t\t\t\tdragged = isDragging ? 2 : 1; // dragged: 0 = not dragging, 1 = first drag, 2 = normal drag\n\t\t\t\t\tisDragging || (self.isDragging = true);\n\t\t\t\t\tonTouchOrPointerDelta(dx, dy);\n\t\t\t\t}\n\t\t\t},\n\t\t\t_onPress = self.onPress = e => {\n\t\t\t\tif (_ignoreCheck(e, 1) || (e && e.button)) {return;}\n\t\t\t\tself.axis = axis = null;\n\t\t\t\tonStopDelayedCall.pause();\n\t\t\t\tself.isPressed = true;\n\t\t\t\te = _getEvent(e); // note: may need to preventDefault(?) Won't side-scroll on iOS Safari if we do, though.\n\t\t\t\tprevDeltaX = prevDeltaY = 0;\n\t\t\t\tself.startX = self.x = e.clientX;\n\t\t\t\tself.startY = self.y = e.clientY;\n\t\t\t\tself._vx.reset(); // otherwise the t2 may be stale if the user touches and flicks super fast and releases in less than 2 requestAnimationFrame ticks, causing velocity to be 0.\n\t\t\t\tself._vy.reset();\n\t\t\t\t_addListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, passive, true);\n\t\t\t\tself.deltaX = self.deltaY = 0;\n\t\t\t\tonPress && onPress(self);\n\t\t\t},\n\t\t\t_onRelease = self.onRelease = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\t_removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\t\t\t\tlet isTrackingDrag = !isNaN(self.y - self.startY),\n\t\t\t\t\twasDragging = self.isDragging,\n\t\t\t\t\tisDragNotClick = wasDragging && (Math.abs(self.x - self.startX) > 3 || Math.abs(self.y - self.startY) > 3), // some touch devices need some wiggle room in terms of sensing clicks - the finger may move a few pixels.\n\t\t\t\t\teventData = _getEvent(e);\n\t\t\t\tif (!isDragNotClick && isTrackingDrag) {\n\t\t\t\t\tself._vx.reset();\n\t\t\t\t\tself._vy.reset();\n\t\t\t\t\t//if (preventDefault && allowClicks && self.isPressed) { // check isPressed because in a rare edge case, the inputObserver in ScrollTrigger may stopPropagation() on the press/drag, so the onRelease may get fired without the onPress/onDrag ever getting called, thus it could trigger a click to occur on a link after scroll-dragging it.\n\t\t\t\t\tif (preventDefault && allowClicks) {\n\t\t\t\t\t\tgsap.delayedCall(0.08, () => { // some browsers (like Firefox) won't trust script-generated clicks, so if the user tries to click on a video to play it, for example, it simply won't work. Since a regular \"click\" event will most likely be generated anyway (one that has its isTrusted flag set to true), we must slightly delay our script-generated click so that the \"real\"/trusted one is prioritized. Remember, when there are duplicate events in quick succession, we suppress all but the first one. Some browsers don't even trigger the \"real\" one at all, so our synthetic one is a safety valve that ensures that no matter what, a click event does get dispatched.\n\t\t\t\t\t\t\tif (_getTime() - onClickTime > 300 && !e.defaultPrevented) {\n\t\t\t\t\t\t\t\tif (e.target.click) { //some browsers (like mobile Safari) don't properly trigger the click event\n\t\t\t\t\t\t\t\t\te.target.click();\n\t\t\t\t\t\t\t\t} else if (ownerDoc.createEvent) {\n\t\t\t\t\t\t\t\t\tlet syntheticEvent = ownerDoc.createEvent(\"MouseEvents\");\n\t\t\t\t\t\t\t\t\tsyntheticEvent.initMouseEvent(\"click\", true, true, _win, 1, eventData.screenX, eventData.screenY, eventData.clientX, eventData.clientY, false, false, false, false, 0, null);\n\t\t\t\t\t\t\t\t\te.target.dispatchEvent(syntheticEvent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tself.isDragging = self.isGesturing = self.isPressed = false;\n\t\t\t\tonStop && wasDragging && !isNormalizer && onStopDelayedCall.restart(true);\n\t\t\t\tdragged && update(); // in case debouncing, we don't want onDrag to fire AFTER onDragEnd().\n\t\t\t\tonDragEnd && wasDragging && onDragEnd(self);\n\t\t\t\tonRelease && onRelease(self, isDragNotClick);\n\t\t\t},\n\t\t\t_onGestureStart = e => e.touches && e.touches.length > 1 && (self.isGesturing = true) && onGestureStart(e, self.isDragging),\n\t\t\t_onGestureEnd = () => (self.isGesturing = false) || onGestureEnd(self),\n\t\t\tonScroll = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\tlet x = scrollFuncX(),\n\t\t\t\t\ty = scrollFuncY();\n\t\t\t\tonDelta((x - scrollX) * scrollSpeed, (y - scrollY) * scrollSpeed, 1);\n\t\t\t\tscrollX = x;\n\t\t\t\tscrollY = y;\n\t\t\t\tonStop && onStopDelayedCall.restart(true);\n\t\t\t},\n\t\t\t_onWheel = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\te = _getEvent(e, preventDefault);\n\t\t\t\tonWheel && (wheeled = true);\n\t\t\t\tlet multiplier = (e.deltaMode === 1 ? lineHeight : e.deltaMode === 2 ? _win.innerHeight : 1) * wheelSpeed;\n\t\t\t\tonDelta(e.deltaX * multiplier, e.deltaY * multiplier, 0);\n\t\t\t\tonStop && !isNormalizer && onStopDelayedCall.restart(true);\n\t\t\t},\n\t\t\t_onMove = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\tlet x = e.clientX,\n\t\t\t\t\ty = e.clientY,\n\t\t\t\t\tdx = x - self.x,\n\t\t\t\t\tdy = y - self.y;\n\t\t\t\tself.x = x;\n\t\t\t\tself.y = y;\n\t\t\t\tmoved = true;\n\t\t\t\tonStop && onStopDelayedCall.restart(true);\n\t\t\t\t(dx || dy) && onTouchOrPointerDelta(dx, dy);\n\t\t\t},\n\t\t\t_onHover = e => {self.event = e; onHover(self);},\n\t\t\t_onHoverEnd = e => {self.event = e; onHoverEnd(self);},\n\t\t\t_onClick = e => _ignoreCheck(e) || (_getEvent(e, preventDefault) && onClick(self));\n\n\t\tonStopDelayedCall = self._dc = gsap.delayedCall(onStopDelay || 0.25, onStopFunc).pause();\n\n\t\tself.deltaX = self.deltaY = 0;\n\t\tself._vx = _getVelocityProp(0, 50, true);\n\t\tself._vy = _getVelocityProp(0, 50, true);\n\t\tself.scrollX = scrollFuncX;\n\t\tself.scrollY = scrollFuncY;\n\t\tself.isDragging = self.isGesturing = self.isPressed = false;\n\t\t_context(this);\n\t\tself.enable = e => {\n\t\t\tif (!self.isEnabled) {\n\t\t\t\t_addListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\t\t\t\ttype.indexOf(\"scroll\") >= 0 && _addListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, passive, capture);\n\t\t\t\ttype.indexOf(\"wheel\") >= 0 && _addListener(target, \"wheel\", _onWheel, passive, capture);\n\t\t\t\tif ((type.indexOf(\"touch\") >= 0 && _isTouch) || type.indexOf(\"pointer\") >= 0) {\n\t\t\t\t\t_addListener(target, _eventTypes[0], _onPress, passive, capture);\n\t\t\t\t\t_addListener(ownerDoc, _eventTypes[2], _onRelease);\n\t\t\t\t\t_addListener(ownerDoc, _eventTypes[3], _onRelease);\n\t\t\t\t\tallowClicks && _addListener(target, \"click\", clickCapture, true, true);\n\t\t\t\t\tonClick && _addListener(target, \"click\", _onClick);\n\t\t\t\t\tonGestureStart && _addListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\t\t\t\t\tonGestureEnd && _addListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\t\t\t\t\tonHover && _addListener(target, _pointerType + \"enter\", _onHover);\n\t\t\t\t\tonHoverEnd && _addListener(target, _pointerType + \"leave\", _onHoverEnd);\n\t\t\t\t\tonMove && _addListener(target, _pointerType + \"move\", _onMove);\n\t\t\t\t}\n\t\t\t\tself.isEnabled = true;\n\t\t\t\tself.isDragging = self.isGesturing = self.isPressed = moved = dragged = false;\n\t\t\t\tself._vx.reset();\n\t\t\t\tself._vy.reset();\n\t\t\t\tscrollX = scrollFuncX();\n\t\t\t\tscrollY = scrollFuncY();\n\t\t\t\te && e.type && _onPress(e);\n\t\t\t\tonEnable && onEnable(self);\n\t\t\t}\n\t\t\treturn self;\n\t\t};\n\t\tself.disable = () => {\n\t\t\tif (self.isEnabled) {\n\t\t\t\t// only remove the _onScroll listener if there aren't any others that rely on the functionality.\n\t\t\t\t_observers.filter(o => o !== self && _isViewport(o.target)).length || _removeListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\t\t\t\tif (self.isPressed) {\n\t\t\t\t\tself._vx.reset();\n\t\t\t\t\tself._vy.reset();\n\t\t\t\t\t_removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\t\t\t\t}\n\t\t\t\t_removeListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, capture);\n\t\t\t\t_removeListener(target, \"wheel\", _onWheel, capture);\n\t\t\t\t_removeListener(target, _eventTypes[0], _onPress, capture);\n\t\t\t\t_removeListener(ownerDoc, _eventTypes[2], _onRelease);\n\t\t\t\t_removeListener(ownerDoc, _eventTypes[3], _onRelease);\n\t\t\t\t_removeListener(target, \"click\", clickCapture, true);\n\t\t\t\t_removeListener(target, \"click\", _onClick);\n\t\t\t\t_removeListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\t\t\t\t_removeListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\t\t\t\t_removeListener(target, _pointerType + \"enter\", _onHover);\n\t\t\t\t_removeListener(target, _pointerType + \"leave\", _onHoverEnd);\n\t\t\t\t_removeListener(target, _pointerType + \"move\", _onMove);\n\t\t\t\tself.isEnabled = self.isPressed = self.isDragging = false;\n\t\t\t\tonDisable && onDisable(self);\n\t\t\t}\n\t\t};\n\n\t\tself.kill = self.revert = () => {\n\t\t\tself.disable();\n\t\t\tlet i = _observers.indexOf(self);\n\t\t\ti >= 0 && _observers.splice(i, 1);\n\t\t\t_normalizer === self && (_normalizer = 0);\n\t\t}\n\n\t\t_observers.push(self);\n\t\tisNormalizer && _isViewport(target) && (_normalizer = self);\n\n\t\tself.enable(event);\n\t}\n\n\tget velocityX() {\n\t\treturn this._vx.getVelocity();\n\t}\n\tget velocityY() {\n\t\treturn this._vy.getVelocity();\n\t}\n\n}\n\nObserver.version = \"3.13.0\";\nObserver.create = vars => new Observer(vars);\nObserver.register = _initCore;\nObserver.getAll = () => _observers.slice();\nObserver.getById = id => _observers.filter(o => o.vars.id === id)[0];\n\n_getGSAP() && gsap.registerPlugin(Observer);\n\nexport { Observer as default, _isViewport, _scrollers, _getScrollFunc, _getProxyProp, _proxies, _getVelocityProp, _vertical, _horizontal, _getTarget };", "/*!\n * ScrollTrigger 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { Observer, _getTarget, _vertical, _horizontal, _scrollers, _proxies, _getScrollFunc, _getProxyProp, _getVelocityProp } from \"./Observer.js\";\n\nlet gsap, _coreInitted, _win, _doc, _docEl, _body, _root, _resizeDelay, _toArray, _clamp, _time2, _syncInterval, _refreshing, _pointerIsDown, _transformProp, _i, _prevWidth, _prevHeight, _autoRefresh, _sort, _suppressOverwrites, _ignoreResize, _normalizer, _ignoreMobileResize, _baseScreenHeight, _baseScreenWidth, _fixIOSBug, _context, _scrollRestoration, _div100vh, _100vh, _isReverted, _clampingMax,\n\t_limitCallbacks, // if true, we'll only trigger callbacks if the active state toggles, so if you scroll immediately past both the start and end positions of a ScrollTrigger (thus inactive to inactive), neither its onEnter nor onLeave will be called. This is useful during startup.\n\t_startup = 1,\n\t_getTime = Date.now,\n\t_time1 = _getTime(),\n\t_lastScrollTime = 0,\n\t_enabled = 0,\n\t_parseClamp = (value, type, self) => {\n\t\tlet clamp = (_isString(value) && (value.substr(0, 6) === \"clamp(\" || value.indexOf(\"max\") > -1));\n\t\tself[\"_\" + type + \"Clamp\"] = clamp;\n\t\treturn clamp ? value.substr(6, value.length - 7) : value;\n\t},\n\t_keepClamp = (value, clamp) => clamp && (!_isString(value) || value.substr(0, 6) !== \"clamp(\") ? \"clamp(\" + value + \")\" : value,\n\t_rafBugFix = () => _enabled && requestAnimationFrame(_rafBugFix), // in some browsers (like Firefox), screen repaints weren't consistent unless we had SOMETHING queued up in requestAnimationFrame()! So this just creates a super simple loop to keep it alive and smooth out repaints.\n\t_pointerDownHandler = () => _pointerIsDown = 1,\n\t_pointerUpHandler = () => _pointerIsDown = 0,\n\t_passThrough = v => v,\n\t_round = value => Math.round(value * 100000) / 100000 || 0,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_isViewport = e => !!~_root.indexOf(e),\n\t_getViewportDimension = dimensionProperty => (dimensionProperty === \"Height\" ? _100vh : _win[\"inner\" + dimensionProperty]) || _docEl[\"client\" + dimensionProperty] || _body[\"client\" + dimensionProperty],\n\t_getBoundsFunc = element => _getProxyProp(element, \"getBoundingClientRect\") || (_isViewport(element) ? () => {_winOffsets.width = _win.innerWidth; _winOffsets.height = _100vh; return _winOffsets;} : () => _getBounds(element)),\n\t_getSizeFunc = (scroller, isViewport, {d, d2, a}) => (a = _getProxyProp(scroller, \"getBoundingClientRect\")) ? () => a()[d] : () => (isViewport ? _getViewportDimension(d2) : scroller[\"client\" + d2]) || 0,\n\t_getOffsetsFunc = (element, isViewport) => !isViewport || ~_proxies.indexOf(element) ? _getBoundsFunc(element) : () => _winOffsets,\n\t_maxScroll = (element, {s, d2, d, a}) => Math.max(0, (s = \"scroll\" + d2) && (a = _getProxyProp(element, s)) ? a() - _getBoundsFunc(element)()[d] : _isViewport(element) ? (_docEl[s] || _body[s]) - _getViewportDimension(d2) : element[s] - element[\"offset\" + d2]),\n\t_iterateAutoRefresh = (func, events) => {\n\t\tfor (let i = 0; i < _autoRefresh.length; i += 3) {\n\t\t\t(!events || ~events.indexOf(_autoRefresh[i+1])) && func(_autoRefresh[i], _autoRefresh[i+1], _autoRefresh[i+2]);\n\t\t}\n\t},\n\t_isString = value => typeof(value) === \"string\",\n\t_isFunction = value => typeof(value) === \"function\",\n\t_isNumber = value => typeof(value) === \"number\",\n\t_isObject = value => typeof(value) === \"object\",\n\t_endAnimation = (animation, reversed, pause) => animation && animation.progress(reversed ? 0 : 1) && pause && animation.pause(),\n\t_callback = (self, func) => {\n\t\tif (self.enabled) {\n\t\t\tlet result = self._ctx ? self._ctx.add(() => func(self)) : func(self);\n\t\t\tresult && result.totalTime && (self.callbackAnimation = result);\n\t\t}\n\t},\n\t_abs = Math.abs,\n\t_left = \"left\",\n\t_top = \"top\",\n\t_right = \"right\",\n\t_bottom = \"bottom\",\n\t_width = \"width\",\n\t_height = \"height\",\n\t_Right = \"Right\",\n\t_Left = \"Left\",\n\t_Top = \"Top\",\n\t_Bottom = \"Bottom\",\n\t_padding = \"padding\",\n\t_margin = \"margin\",\n\t_Width = \"Width\",\n\t_Height = \"Height\",\n\t_px = \"px\",\n\t_getComputedStyle = element => _win.getComputedStyle(element),\n\t_makePositionable = element => { // if the element already has position: absolute or fixed, leave that, otherwise make it position: relative\n\t\tlet position = _getComputedStyle(element).position;\n\t\telement.style.position = (position === \"absolute\" || position === \"fixed\") ? position : \"relative\";\n\t},\n\t_setDefaults = (obj, defaults) => {\n\t\tfor (let p in defaults) {\n\t\t\t(p in obj) || (obj[p] = defaults[p]);\n\t\t}\n\t\treturn obj;\n\t},\n\t_getBounds = (element, withoutTransforms) => {\n\t\tlet tween = withoutTransforms && _getComputedStyle(element)[_transformProp] !== \"matrix(1, 0, 0, 1, 0, 0)\" && gsap.to(element, {x: 0, y: 0, xPercent: 0, yPercent: 0, rotation: 0, rotationX: 0, rotationY: 0, scale: 1, skewX: 0, skewY: 0}).progress(1),\n\t\t\tbounds = element.getBoundingClientRect();\n\t\ttween && tween.progress(0).kill();\n\t\treturn bounds;\n\t},\n\t_getSize = (element, {d2}) => element[\"offset\" + d2] || element[\"client\" + d2] || 0,\n\t_getLabelRatioArray = timeline => {\n\t\tlet a = [],\n\t\t\tlabels = timeline.labels,\n\t\t\tduration = timeline.duration(),\n\t\t\tp;\n\t\tfor (p in labels) {\n\t\t\ta.push(labels[p] / duration);\n\t\t}\n\t\treturn a;\n\t},\n\t_getClosestLabel = animation => value => gsap.utils.snap(_getLabelRatioArray(animation), value),\n\t_snapDirectional = snapIncrementOrArray => {\n\t\tlet snap = gsap.utils.snap(snapIncrementOrArray),\n\t\t\ta = Array.isArray(snapIncrementOrArray) && snapIncrementOrArray.slice(0).sort((a, b) => a - b);\n\t\treturn a ? (value, direction, threshold= 1e-3) => {\n\t\t\tlet i;\n\t\t\tif (!direction) {\n\t\t\t\treturn snap(value);\n\t\t\t}\n\t\t\tif (direction > 0) {\n\t\t\t\tvalue -= threshold; // to avoid rounding errors. If we're too strict, it might snap forward, then immediately again, and again.\n\t\t\t\tfor (i = 0; i < a.length; i++) {\n\t\t\t\t\tif (a[i] >= value) {\n\t\t\t\t\t\treturn a[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn a[i-1];\n\t\t\t} else {\n\t\t\t\ti = a.length;\n\t\t\t\tvalue += threshold;\n\t\t\t\twhile (i--) {\n\t\t\t\t\tif (a[i] <= value) {\n\t\t\t\t\t\treturn a[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn a[0];\n\t\t} : (value, direction, threshold= 1e-3) => {\n\t\t\tlet snapped = snap(value);\n\t\t\treturn !direction || Math.abs(snapped - value) < threshold || ((snapped - value < 0) === direction < 0) ? snapped : snap(direction < 0 ? value - snapIncrementOrArray : value + snapIncrementOrArray);\n\t\t};\n\t},\n\t_getLabelAtDirection = timeline => (value, st) => _snapDirectional(_getLabelRatioArray(timeline))(value, st.direction),\n\t_multiListener = (func, element, types, callback) => types.split(\",\").forEach(type => func(element, type, callback)),\n\t_addListener = (element, type, func, nonPassive, capture) => element.addEventListener(type, func, {passive: !nonPassive, capture: !!capture}),\n\t_removeListener = (element, type, func, capture) => element.removeEventListener(type, func, !!capture),\n\t_wheelListener = (func, el, scrollFunc) => {\n\t\tscrollFunc = scrollFunc && scrollFunc.wheelHandler\n\t\tif (scrollFunc) {\n\t\t\tfunc(el, \"wheel\", scrollFunc);\n\t\t\tfunc(el, \"touchmove\", scrollFunc);\n\t\t}\n\t},\n\t_markerDefaults = {startColor: \"green\", endColor: \"red\", indent: 0, fontSize: \"16px\", fontWeight:\"normal\"},\n\t_defaults = {toggleActions: \"play\", anticipatePin: 0},\n\t_keywords = {top: 0, left: 0, center: 0.5, bottom: 1, right: 1},\n\t_offsetToPx = (value, size) => {\n\t\tif (_isString(value)) {\n\t\t\tlet eqIndex = value.indexOf(\"=\"),\n\t\t\t\trelative = ~eqIndex ? +(value.charAt(eqIndex-1) + 1) * parseFloat(value.substr(eqIndex + 1)) : 0;\n\t\t\tif (~eqIndex) {\n\t\t\t\t(value.indexOf(\"%\") > eqIndex) && (relative *= size / 100);\n\t\t\t\tvalue = value.substr(0, eqIndex-1);\n\t\t\t}\n\t\t\tvalue = relative + ((value in _keywords) ? _keywords[value] * size : ~value.indexOf(\"%\") ? parseFloat(value) * size / 100 : parseFloat(value) || 0);\n\t\t}\n\t\treturn value;\n\t},\n\t_createMarker = (type, name, container, direction, {startColor, endColor, fontSize, indent, fontWeight}, offset, matchWidthEl, containerAnimation) => {\n\t\tlet e = _doc.createElement(\"div\"),\n\t\t\tuseFixedPosition = _isViewport(container) || _getProxyProp(container, \"pinType\") === \"fixed\",\n\t\t\tisScroller = type.indexOf(\"scroller\") !== -1,\n\t\t\tparent = useFixedPosition ? _body : container,\n\t\t\tisStart = type.indexOf(\"start\") !== -1,\n\t\t\tcolor = isStart ? startColor : endColor,\n\t\t\tcss = \"border-color:\" + color + \";font-size:\" + fontSize + \";color:\" + color + \";font-weight:\" + fontWeight + \";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;\";\n\t\tcss += \"position:\" + ((isScroller || containerAnimation) && useFixedPosition ? \"fixed;\" : \"absolute;\");\n\t\t(isScroller || containerAnimation || !useFixedPosition) && (css += (direction === _vertical ? _right : _bottom) + \":\" + (offset + parseFloat(indent)) + \"px;\");\n\t\tmatchWidthEl && (css += \"box-sizing:border-box;text-align:left;width:\" + matchWidthEl.offsetWidth + \"px;\");\n\t\te._isStart = isStart;\n\t\te.setAttribute(\"class\", \"gsap-marker-\" + type + (name ? \" marker-\" + name : \"\"));\n\t\te.style.cssText = css;\n\t\te.innerText = name || name === 0 ? type + \"-\" + name : type;\n\t\tparent.children[0] ? parent.insertBefore(e, parent.children[0]) : parent.appendChild(e);\n\t\te._offset = e[\"offset\" + direction.op.d2];\n\t\t_positionMarker(e, 0, direction, isStart);\n\t\treturn e;\n\t},\n\t_positionMarker = (marker, start, direction, flipped) => {\n\t\tlet vars = {display: \"block\"},\n\t\t\tside = direction[flipped ? \"os2\" : \"p2\"],\n\t\t\toppositeSide = direction[flipped ? \"p2\" : \"os2\"];\n\t\tmarker._isFlipped = flipped;\n\t\tvars[direction.a + \"Percent\"] = flipped ? -100 : 0;\n\t\tvars[direction.a] = flipped ? \"1px\" : 0;\n\t\tvars[\"border\" + side + _Width] = 1;\n\t\tvars[\"border\" + oppositeSide + _Width] = 0;\n\t\tvars[direction.p] = start + \"px\";\n\t\tgsap.set(marker, vars);\n\t},\n\t_triggers = [],\n\t_ids = {},\n\t_rafID,\n\t_sync = () => _getTime() - _lastScrollTime > 34 && (_rafID || (_rafID = requestAnimationFrame(_updateAll))),\n\t_onScroll = () => { // previously, we tried to optimize performance by batching/deferring to the next requestAnimationFrame(), but discovered that Safari has a few bugs that make this unworkable (especially on iOS). See https://codepen.io/GreenSock/pen/16c435b12ef09c38125204818e7b45fc?editors=0010 and https://codepen.io/GreenSock/pen/JjOxYpQ/3dd65ccec5a60f1d862c355d84d14562?editors=0010 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503?editors=0010\n\t\tif (!_normalizer || !_normalizer.isPressed || _normalizer.startX > _body.clientWidth) { // if the user is dragging the scrollbar, allow it.\n\t\t\t_scrollers.cache++;\n\t\t\tif (_normalizer) {\n\t\t\t\t_rafID || (_rafID = requestAnimationFrame(_updateAll));\n\t\t\t} else {\n\t\t\t\t_updateAll(); // Safari in particular (on desktop) NEEDS the immediate update rather than waiting for a requestAnimationFrame() whereas iOS seems to benefit from waiting for the requestAnimationFrame() tick, at least when normalizing. See https://codepen.io/GreenSock/pen/qBYozqO?editors=0110\n\t\t\t}\n\t\t\t_lastScrollTime || _dispatch(\"scrollStart\");\n\t\t\t_lastScrollTime = _getTime();\n\t\t}\n\t},\n\t_setBaseDimensions = () => {\n\t\t_baseScreenWidth = _win.innerWidth;\n\t\t_baseScreenHeight = _win.innerHeight;\n\t},\n\t_onResize = (force) => {\n\t\t_scrollers.cache++;\n\t\t(force === true || (!_refreshing && !_ignoreResize && !_doc.fullscreenElement && !_doc.webkitFullscreenElement && (!_ignoreMobileResize || _baseScreenWidth !== _win.innerWidth || Math.abs(_win.innerHeight - _baseScreenHeight) > _win.innerHeight * 0.25))) && _resizeDelay.restart(true);\n\t}, // ignore resizes triggered by refresh()\n\t_listeners = {},\n\t_emptyArray = [],\n\t_softRefresh = () => _removeListener(ScrollTrigger, \"scrollEnd\", _softRefresh) || _refreshAll(true),\n\t_dispatch = type => (_listeners[type] && _listeners[type].map(f => f())) || _emptyArray,\n\t_savedStyles = [], // when ScrollTrigger.saveStyles() is called, the inline styles are recorded in this Array in a sequential format like [element, cssText, gsCache, media]. This keeps it very memory-efficient and fast to iterate through.\n\t_revertRecorded = media => {\n\t\tfor (let i = 0; i < _savedStyles.length; i+=5) {\n\t\t\tif (!media || _savedStyles[i+4] && _savedStyles[i+4].query === media) {\n\t\t\t\t_savedStyles[i].style.cssText = _savedStyles[i+1];\n\t\t\t\t_savedStyles[i].getBBox && _savedStyles[i].setAttribute(\"transform\", _savedStyles[i+2] || \"\");\n\t\t\t\t_savedStyles[i+3].uncache = 1;\n\t\t\t}\n\t\t}\n\t},\n\t_revertAll = (kill, media) => {\n\t\tlet trigger;\n\t\tfor (_i = 0; _i < _triggers.length; _i++) {\n\t\t\ttrigger = _triggers[_i];\n\t\t\tif (trigger && (!media || trigger._ctx === media)) {\n\t\t\t\tif (kill) {\n\t\t\t\t\ttrigger.kill(1);\n\t\t\t\t} else {\n\t\t\t\t\ttrigger.revert(true, true);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t_isReverted = true;\n\t\tmedia && _revertRecorded(media);\n\t\tmedia || _dispatch(\"revert\");\n\t},\n\t_clearScrollMemory = (scrollRestoration, force) => { // zero-out all the recorded scroll positions. Don't use _triggers because if, for example, .matchMedia() is used to create some ScrollTriggers and then the user resizes and it removes ALL ScrollTriggers, and then go back to a size where there are ScrollTriggers, it would have kept the position(s) saved from the initial state.\n\t\t_scrollers.cache++;\n\t\t(force || !_refreshingAll) && _scrollers.forEach(obj => _isFunction(obj) && obj.cacheID++ && (obj.rec = 0));\n\t\t_isString(scrollRestoration) && (_win.history.scrollRestoration = _scrollRestoration = scrollRestoration);\n\t},\n\t_refreshingAll,\n\t_refreshID = 0,\n\t_queueRefreshID,\n\t_queueRefreshAll = () => { // we don't want to call _refreshAll() every time we create a new ScrollTrigger (for performance reasons) - it's better to batch them. Some frameworks dynamically load content and we can't rely on the window's \"load\" or \"DOMContentLoaded\" events to trigger it.\n\t\tif (_queueRefreshID !== _refreshID) {\n\t\t\tlet id = _queueRefreshID = _refreshID;\n\t\t\trequestAnimationFrame(() => id === _refreshID && _refreshAll(true));\n\t\t}\n\t},\n\t_refresh100vh = () => {\n\t\t_body.appendChild(_div100vh);\n\t\t_100vh = (!_normalizer && _div100vh.offsetHeight) || _win.innerHeight;\n\t\t_body.removeChild(_div100vh);\n\t},\n\t_hideAllMarkers = hide => _toArray(\".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end\").forEach(el => el.style.display = hide ? \"none\" : \"block\"),\n\t_refreshAll = (force, skipRevert) => {\n\t\t_docEl = _doc.documentElement; // some frameworks like Astro may cache the <body> and replace it during routing, so we'll just re-record the _docEl and _body for safety (otherwise, the markers may not get added properly).\n\t\t_body = _doc.body;\n\t\t_root = [_win, _doc, _docEl, _body];\n\t\tif (_lastScrollTime && !force && !_isReverted) {\n\t\t\t_addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\t\t\treturn;\n\t\t}\n\t\t_refresh100vh();\n\t\t_refreshingAll = ScrollTrigger.isRefreshing = true;\n\t\t_scrollers.forEach(obj => _isFunction(obj) && ++obj.cacheID && (obj.rec = obj())); // force the clearing of the cache because some browsers take a little while to dispatch the \"scroll\" event and the user may have changed the scroll position and then called ScrollTrigger.refresh() right away\n\t\tlet refreshInits = _dispatch(\"refreshInit\");\n\t\t_sort && ScrollTrigger.sort();\n\t\tskipRevert || _revertAll();\n\t\t_scrollers.forEach(obj => {\n\t\t\tif (_isFunction(obj)) {\n\t\t\t\tobj.smooth && (obj.target.style.scrollBehavior = \"auto\"); // smooth scrolling interferes\n\t\t\t\tobj(0);\n\t\t\t}\n\t\t});\n\t\t_triggers.slice(0).forEach(t => t.refresh()) // don't loop with _i because during a refresh() someone could call ScrollTrigger.update() which would iterate through _i resulting in a skip.\n\t\t_isReverted = false;\n\t\t_triggers.forEach((t) => { // nested pins (pinnedContainer) with pinSpacing may expand the container, so we must accommodate that here.\n\t\t\tif (t._subPinOffset && t.pin) {\n\t\t\t\tlet prop = t.vars.horizontal ? \"offsetWidth\" : \"offsetHeight\",\n\t\t\t\t\toriginal = t.pin[prop];\n\t\t\t\tt.revert(true, 1);\n\t\t\t\tt.adjustPinSpacing(t.pin[prop] - original);\n\t\t\t\tt.refresh();\n\t\t\t}\n\t\t});\n\t\t_clampingMax = 1; // pinSpacing might be propping a page open, thus when we .setPositions() to clamp a ScrollTrigger's end we should leave the pinSpacing alone. That's what this flag is for.\n\t\t_hideAllMarkers(true);\n\t\t_triggers.forEach(t => { // the scroller's max scroll position may change after all the ScrollTriggers refreshed (like pinning could push it down), so we need to loop back and correct any with end: \"max\". Same for anything with a clamped end\n\t\t\tlet max = _maxScroll(t.scroller, t._dir),\n\t\t\t\tendClamp = t.vars.end === \"max\" || (t._endClamp && t.end > max),\n\t\t\t\tstartClamp = t._startClamp && t.start >= max;\n\t\t\t(endClamp || startClamp) && t.setPositions(startClamp ? max - 1 : t.start, endClamp ? Math.max(startClamp ? max : t.start + 1, max) : t.end, true);\n\t\t});\n\t\t_hideAllMarkers(false);\n\t\t_clampingMax = 0;\n\t\trefreshInits.forEach(result => result && result.render && result.render(-1)); // if the onRefreshInit() returns an animation (typically a gsap.set()), revert it. This makes it easy to put things in a certain spot before refreshing for measurement purposes, and then put things back.\n\t\t_scrollers.forEach(obj => {\n\t\t\tif (_isFunction(obj)) {\n\t\t\t\tobj.smooth && requestAnimationFrame(() => obj.target.style.scrollBehavior = \"smooth\");\n\t\t\t\tobj.rec && obj(obj.rec);\n\t\t\t}\n\t\t});\n\t\t_clearScrollMemory(_scrollRestoration, 1);\n\t\t_resizeDelay.pause();\n\t\t_refreshID++;\n\t\t_refreshingAll = 2;\n\t\t_updateAll(2);\n\t\t_triggers.forEach(t => _isFunction(t.vars.onRefresh) && t.vars.onRefresh(t));\n\t\t_refreshingAll = ScrollTrigger.isRefreshing = false;\n\t\t_dispatch(\"refresh\");\n\t},\n\t_lastScroll = 0,\n\t_direction = 1,\n\t_primary,\n\t_updateAll = (force) => {\n\t\tif (force === 2 || (!_refreshingAll && !_isReverted)) { // _isReverted could be true if, for example, a matchMedia() is in the process of executing. We don't want to update during the time everything is reverted.\n\t\t\tScrollTrigger.isUpdating = true;\n\t\t\t_primary && _primary.update(0); // ScrollSmoother uses refreshPriority -9999 to become the primary that gets updated before all others because it affects the scroll position.\n\t\t\tlet l = _triggers.length,\n\t\t\t\ttime = _getTime(),\n\t\t\t\trecordVelocity = time - _time1 >= 50,\n\t\t\t\tscroll = l && _triggers[0].scroll();\n\t\t\t_direction = _lastScroll > scroll ? -1 : 1;\n\t\t\t_refreshingAll || (_lastScroll = scroll);\n\t\t\tif (recordVelocity) {\n\t\t\t\tif (_lastScrollTime && !_pointerIsDown && time - _lastScrollTime > 200) {\n\t\t\t\t\t_lastScrollTime = 0;\n\t\t\t\t\t_dispatch(\"scrollEnd\");\n\t\t\t\t}\n\t\t\t\t_time2 = _time1;\n\t\t\t\t_time1 = time;\n\t\t\t}\n\t\t\tif (_direction < 0) {\n\t\t\t\t_i = l;\n\t\t\t\twhile (_i-- > 0) {\n\t\t\t\t\t_triggers[_i] && _triggers[_i].update(0, recordVelocity);\n\t\t\t\t}\n\t\t\t\t_direction = 1;\n\t\t\t} else {\n\t\t\t\tfor (_i = 0; _i < l; _i++) {\n\t\t\t\t\t_triggers[_i] && _triggers[_i].update(0, recordVelocity);\n\t\t\t\t}\n\t\t\t}\n\t\t\tScrollTrigger.isUpdating = false;\n\t\t}\n\t\t_rafID = 0;\n\t},\n\t_propNamesToCopy = [_left, _top, _bottom, _right, _margin + _Bottom, _margin + _Right, _margin + _Top, _margin + _Left, \"display\", \"flexShrink\", \"float\", \"zIndex\", \"gridColumnStart\", \"gridColumnEnd\", \"gridRowStart\", \"gridRowEnd\", \"gridArea\", \"justifySelf\", \"alignSelf\", \"placeSelf\", \"order\"],\n\t_stateProps = _propNamesToCopy.concat([_width, _height, \"boxSizing\", \"max\" + _Width, \"max\" + _Height, \"position\", _margin, _padding, _padding + _Top, _padding + _Right, _padding + _Bottom, _padding + _Left]),\n\t_swapPinOut = (pin, spacer, state) => {\n\t\t_setState(state);\n\t\tlet cache = pin._gsap;\n\t\tif (cache.spacerIsNative) {\n\t\t\t_setState(cache.spacerState);\n\t\t} else if (pin._gsap.swappedIn) {\n\t\t\tlet parent = spacer.parentNode;\n\t\t\tif (parent) {\n\t\t\t\tparent.insertBefore(pin, spacer);\n\t\t\t\tparent.removeChild(spacer);\n\t\t\t}\n\t\t}\n\t\tpin._gsap.swappedIn = false;\n\t},\n\t_swapPinIn = (pin, spacer, cs, spacerState) => {\n\t\tif (!pin._gsap.swappedIn) {\n\t\t\tlet i = _propNamesToCopy.length,\n\t\t\t\tspacerStyle = spacer.style,\n\t\t\t\tpinStyle = pin.style,\n\t\t\t\tp;\n\t\t\twhile (i--) {\n\t\t\t\tp = _propNamesToCopy[i];\n\t\t\t\tspacerStyle[p] = cs[p];\n\t\t\t}\n\t\t\tspacerStyle.position = cs.position === \"absolute\" ? \"absolute\" : \"relative\";\n\t\t\t(cs.display === \"inline\") && (spacerStyle.display = \"inline-block\");\n\t\t\tpinStyle[_bottom] = pinStyle[_right] = \"auto\";\n\t\t\tspacerStyle.flexBasis = cs.flexBasis || \"auto\";\n\t\t\tspacerStyle.overflow = \"visible\";\n\t\t\tspacerStyle.boxSizing = \"border-box\";\n\t\t\tspacerStyle[_width] = _getSize(pin, _horizontal) + _px;\n\t\t\tspacerStyle[_height] = _getSize(pin, _vertical) + _px;\n\t\t\tspacerStyle[_padding] = pinStyle[_margin] = pinStyle[_top] = pinStyle[_left] = \"0\";\n\t\t\t_setState(spacerState);\n\t\t\tpinStyle[_width] = pinStyle[\"max\" + _Width] = cs[_width];\n\t\t\tpinStyle[_height] = pinStyle[\"max\" + _Height] = cs[_height];\n\t\t\tpinStyle[_padding] = cs[_padding];\n\t\t\tif (pin.parentNode !== spacer) {\n\t\t\t\tpin.parentNode.insertBefore(spacer, pin);\n\t\t\t\tspacer.appendChild(pin);\n\t\t\t}\n\t\t\tpin._gsap.swappedIn = true;\n\t\t}\n\t},\n\t_capsExp = /([A-Z])/g,\n\t_setState = state => {\n\t\tif (state) {\n\t\t\tlet style = state.t.style,\n\t\t\t\tl = state.length,\n\t\t\t\ti = 0,\n\t\t\t\tp, value;\n\t\t\t(state.t._gsap || gsap.core.getCache(state.t)).uncache = 1; // otherwise transforms may be off\n\t\t\tfor (; i < l; i +=2) {\n\t\t\t\tvalue = state[i+1];\n\t\t\t\tp = state[i];\n\t\t\t\tif (value) {\n\t\t\t\t\tstyle[p] = value;\n\t\t\t\t} else if (style[p]) {\n\t\t\t\t\tstyle.removeProperty(p.replace(_capsExp, \"-$1\").toLowerCase());\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\t_getState = element => { // returns an Array with alternating values like [property, value, property, value] and a \"t\" property pointing to the target (element). Makes it fast and cheap.\n\t\tlet l = _stateProps.length,\n\t\t\tstyle = element.style,\n\t\t\tstate = [],\n\t\t\ti = 0;\n\t\tfor (; i < l; i++) {\n\t\t\tstate.push(_stateProps[i], style[_stateProps[i]]);\n\t\t}\n\t\tstate.t = element;\n\t\treturn state;\n\t},\n\t_copyState = (state, override, omitOffsets) => {\n\t\tlet result = [],\n\t\t\tl = state.length,\n\t\t\ti = omitOffsets ? 8 : 0, // skip top, left, right, bottom if omitOffsets is true\n\t\t\tp;\n\t\tfor (; i < l; i += 2) {\n\t\t\tp = state[i];\n\t\t\tresult.push(p, (p in override) ? override[p] : state[i+1]);\n\t\t}\n\t\tresult.t = state.t;\n\t\treturn result;\n\t},\n\t_winOffsets = {left:0, top:0},\n\t// // potential future feature (?) Allow users to calculate where a trigger hits (scroll position) like getScrollPosition(\"#id\", \"top bottom\")\n\t// _getScrollPosition = (trigger, position, {scroller, containerAnimation, horizontal}) => {\n\t// \tscroller = _getTarget(scroller || _win);\n\t// \tlet direction = horizontal ? _horizontal : _vertical,\n\t// \t\tisViewport = _isViewport(scroller);\n\t// \t_getSizeFunc(scroller, isViewport, direction);\n\t// \treturn _parsePosition(position, _getTarget(trigger), _getSizeFunc(scroller, isViewport, direction)(), direction, _getScrollFunc(scroller, direction)(), 0, 0, 0, _getOffsetsFunc(scroller, isViewport)(), isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0, 0, containerAnimation ? containerAnimation.duration() : _maxScroll(scroller), containerAnimation);\n\t// },\n\t_parsePosition = (value, trigger, scrollerSize, direction, scroll, marker, markerScroller, self, scrollerBounds, borderWidth, useFixedPosition, scrollerMax, containerAnimation, clampZeroProp) => {\n\t\t_isFunction(value) && (value = value(self));\n\t\tif (_isString(value) && value.substr(0,3) === \"max\") {\n\t\t\tvalue = scrollerMax + (value.charAt(4) === \"=\" ? _offsetToPx(\"0\" + value.substr(3), scrollerSize) : 0);\n\t\t}\n\t\tlet time = containerAnimation ? containerAnimation.time() : 0,\n\t\t\tp1, p2, element;\n\t\tcontainerAnimation && containerAnimation.seek(0);\n\t\tisNaN(value) || (value = +value); // convert a string number like \"45\" to an actual number\n\t\tif (!_isNumber(value)) {\n\t\t\t_isFunction(trigger) && (trigger = trigger(self));\n\t\t\tlet offsets = (value || \"0\").split(\" \"),\n\t\t\t\tbounds, localOffset, globalOffset, display;\n\t\t\telement = _getTarget(trigger, self) || _body;\n\t\t\tbounds = _getBounds(element) || {};\n\t\t\tif ((!bounds || (!bounds.left && !bounds.top)) && _getComputedStyle(element).display === \"none\") { // if display is \"none\", it won't report getBoundingClientRect() properly\n\t\t\t\tdisplay = element.style.display;\n\t\t\t\telement.style.display = \"block\";\n\t\t\t\tbounds = _getBounds(element);\n\t\t\t\tdisplay ? (element.style.display = display) : element.style.removeProperty(\"display\");\n\t\t\t}\n\t\t\tlocalOffset = _offsetToPx(offsets[0], bounds[direction.d]);\n\t\t\tglobalOffset = _offsetToPx(offsets[1] || \"0\", scrollerSize);\n\t\t\tvalue = bounds[direction.p] - scrollerBounds[direction.p] - borderWidth + localOffset + scroll - globalOffset;\n\t\t\tmarkerScroller && _positionMarker(markerScroller, globalOffset, direction, (scrollerSize - globalOffset < 20 || (markerScroller._isStart && globalOffset > 20)));\n\t\t\tscrollerSize -= scrollerSize - globalOffset; // adjust for the marker\n\t\t} else {\n\t\t\tcontainerAnimation && (value = gsap.utils.mapRange(containerAnimation.scrollTrigger.start, containerAnimation.scrollTrigger.end, 0, scrollerMax, value));\n\t\t\tmarkerScroller && _positionMarker(markerScroller, scrollerSize, direction, true);\n\t\t}\n\t\tif (clampZeroProp) {\n\t\t\tself[clampZeroProp] = value || -0.001;\n\t\t\tvalue < 0 && (value = 0);\n\t\t}\n\t\tif (marker) {\n\t\t\tlet position = value + scrollerSize,\n\t\t\t\tisStart = marker._isStart;\n\t\t\tp1 = \"scroll\" + direction.d2;\n\t\t\t_positionMarker(marker, position, direction, (isStart && position > 20) || (!isStart && (useFixedPosition ? Math.max(_body[p1], _docEl[p1]) : marker.parentNode[p1]) <= position + 1));\n\t\t\tif (useFixedPosition) {\n\t\t\t\tscrollerBounds = _getBounds(markerScroller);\n\t\t\t\tuseFixedPosition && (marker.style[direction.op.p] = (scrollerBounds[direction.op.p] - direction.op.m - marker._offset) + _px);\n\t\t\t}\n\t\t}\n\t\tif (containerAnimation && element) {\n\t\t\tp1 = _getBounds(element);\n\t\t\tcontainerAnimation.seek(scrollerMax);\n\t\t\tp2 = _getBounds(element);\n\t\t\tcontainerAnimation._caScrollDist = p1[direction.p] - p2[direction.p];\n\t\t\tvalue = value / (containerAnimation._caScrollDist) * scrollerMax;\n\t\t}\n\t\tcontainerAnimation && containerAnimation.seek(time);\n\t\treturn containerAnimation ? value : Math.round(value);\n\t},\n\t_prefixExp = /(webkit|moz|length|cssText|inset)/i,\n\t_reparent = (element, parent, top, left) => {\n\t\tif (element.parentNode !== parent) {\n\t\t\tlet style = element.style,\n\t\t\t\tp, cs;\n\t\t\tif (parent === _body) {\n\t\t\t\telement._stOrig = style.cssText; // record original inline styles so we can revert them later\n\t\t\t\tcs = _getComputedStyle(element);\n\t\t\t\tfor (p in cs) { // must copy all relevant styles to ensure that nothing changes visually when we reparent to the <body>. Skip the vendor prefixed ones.\n\t\t\t\t\tif (!+p && !_prefixExp.test(p) && cs[p] && typeof style[p] === \"string\" && p !== \"0\") {\n\t\t\t\t\t\tstyle[p] = cs[p];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tstyle.top = top;\n\t\t\t\tstyle.left = left;\n\t\t\t} else {\n\t\t\t\tstyle.cssText = element._stOrig;\n\t\t\t}\n\t\t\tgsap.core.getCache(element).uncache = 1;\n\t\t\tparent.appendChild(element);\n\t\t}\n\t},\n\t_interruptionTracker = (getValueFunc, initialValue, onInterrupt) => {\n\t\tlet last1 = initialValue,\n\t\t\tlast2 = last1;\n\t\treturn value => {\n\t\t\tlet current = Math.round(getValueFunc()); // round because in some [very uncommon] Windows environments, scroll can get reported with decimals even though it was set without.\n\t\t\tif (current !== last1 && current !== last2 && Math.abs(current - last1) > 3 && Math.abs(current - last2) > 3) { // if the user scrolls, kill the tween. iOS Safari intermittently misreports the scroll position, it may be the most recently-set one or the one before that! When Safari is zoomed (CMD-+), it often misreports as 1 pixel off too! So if we set the scroll position to 125, for example, it'll actually report it as 124.\n\t\t\t\tvalue = current;\n\t\t\t\tonInterrupt && onInterrupt();\n\t\t\t}\n\t\t\tlast2 = last1;\n\t\t\tlast1 = Math.round(value);\n\t\t\treturn last1;\n\t\t};\n\t},\n\t_shiftMarker = (marker, direction, value) => {\n\t\tlet vars = {};\n\t\tvars[direction.p] = \"+=\" + value;\n\t\tgsap.set(marker, vars);\n\t},\n\t// _mergeAnimations = animations => {\n\t// \tlet tl = gsap.timeline({smoothChildTiming: true}).startTime(Math.min(...animations.map(a => a.globalTime(0))));\n\t// \tanimations.forEach(a => {let time = a.totalTime(); tl.add(a); a.totalTime(time); });\n\t// \ttl.smoothChildTiming = false;\n\t// \treturn tl;\n\t// },\n\n\t// returns a function that can be used to tween the scroll position in the direction provided, and when doing so it'll add a .tween property to the FUNCTION itself, and remove it when the tween completes or gets killed. This gives us a way to have multiple ScrollTriggers use a central function for any given scroller and see if there's a scroll tween running (which would affect if/how things get updated)\n\t_getTweenCreator = (scroller, direction) => {\n\t\tlet getScroll = _getScrollFunc(scroller, direction),\n\t\t\tprop = \"_scroll\" + direction.p2, // add a tweenable property to the scroller that's a getter/setter function, like _scrollTop or _scrollLeft. This way, if someone does gsap.killTweensOf(scroller) it'll kill the scroll tween.\n\t\t\tgetTween = (scrollTo, vars, initialValue, change1, change2) => {\n\t\t\t\tlet tween = getTween.tween,\n\t\t\t\t\tonComplete = vars.onComplete,\n\t\t\t\t\tmodifiers = {};\n\t\t\t\tinitialValue = initialValue || getScroll();\n\t\t\t\tlet checkForInterruption = _interruptionTracker(getScroll, initialValue, () => {\n\t\t\t\t\ttween.kill();\n\t\t\t\t\tgetTween.tween = 0;\n\t\t\t\t});\n\t\t\t\tchange2 = (change1 && change2) || 0; // if change1 is 0, we set that to the difference and ignore change2. Otherwise, there would be a compound effect.\n\t\t\t\tchange1 = change1 || (scrollTo - initialValue);\n\t\t\t\ttween && tween.kill();\n\t\t\t\tvars[prop] = scrollTo;\n\t\t\t\tvars.inherit = false;\n\t\t\t\tvars.modifiers = modifiers;\n\t\t\t\tmodifiers[prop] = () => checkForInterruption(initialValue + change1 * tween.ratio + change2 * tween.ratio * tween.ratio);\n\t\t\t\tvars.onUpdate = () => {\n\t\t\t\t\t_scrollers.cache++;\n\t\t\t\t\tgetTween.tween && _updateAll(); // if it was interrupted/killed, like in a context.revert(), don't force an updateAll()\n\t\t\t\t};\n\t\t\t\tvars.onComplete = () => {\n\t\t\t\t\tgetTween.tween = 0;\n\t\t\t\t\tonComplete && onComplete.call(tween);\n\t\t\t\t};\n\t\t\t\ttween = getTween.tween = gsap.to(scroller, vars);\n\t\t\t\treturn tween;\n\t\t\t};\n\t\tscroller[prop] = getScroll;\n\t\tgetScroll.wheelHandler = () => getTween.tween && getTween.tween.kill() && (getTween.tween = 0);\n\t\t_addListener(scroller, \"wheel\", getScroll.wheelHandler); // Windows machines handle mousewheel scrolling in chunks (like \"3 lines per scroll\") meaning the typical strategy for cancelling the scroll isn't as sensitive. It's much more likely to match one of the previous 2 scroll event positions. So we kill any snapping as soon as there's a wheel event.\n\t\tScrollTrigger.isTouch && _addListener(scroller, \"touchmove\", getScroll.wheelHandler);\n\t\treturn getTween;\n\t};\n\n\n\n\nexport class ScrollTrigger {\n\n\tconstructor(vars, animation) {\n\t\t_coreInitted || ScrollTrigger.register(gsap) || console.warn(\"Please gsap.registerPlugin(ScrollTrigger)\");\n\t\t_context(this);\n\t\tthis.init(vars, animation);\n\t}\n\n\tinit(vars, animation) {\n\t\tthis.progress = this.start = 0;\n\t\tthis.vars && this.kill(true, true); // in case it's being initted again\n\t\tif (!_enabled) {\n\t\t\tthis.update = this.refresh = this.kill = _passThrough;\n\t\t\treturn;\n\t\t}\n\t\tvars = _setDefaults((_isString(vars) || _isNumber(vars) || vars.nodeType) ? {trigger: vars} : vars, _defaults);\n\t\tlet {onUpdate, toggleClass, id, onToggle, onRefresh, scrub, trigger, pin, pinSpacing, invalidateOnRefresh, anticipatePin, onScrubComplete, onSnapComplete, once, snap, pinReparent, pinSpacer, containerAnimation, fastScrollEnd, preventOverlaps} = vars,\n\t\t\tdirection = vars.horizontal || (vars.containerAnimation && vars.horizontal !== false) ? _horizontal : _vertical,\n\t\t\tisToggle = !scrub && scrub !== 0,\n\t\t\tscroller = _getTarget(vars.scroller || _win),\n\t\t\tscrollerCache = gsap.core.getCache(scroller),\n\t\t\tisViewport = _isViewport(scroller),\n\t\t\tuseFixedPosition = (\"pinType\" in vars ? vars.pinType : _getProxyProp(scroller, \"pinType\") || (isViewport && \"fixed\")) === \"fixed\",\n\t\t\tcallbacks = [vars.onEnter, vars.onLeave, vars.onEnterBack, vars.onLeaveBack],\n\t\t\ttoggleActions = isToggle && vars.toggleActions.split(\" \"),\n\t\t\tmarkers = \"markers\" in vars ? vars.markers : _defaults.markers,\n\t\t\tborderWidth = isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0,\n\t\t\tself = this,\n\t\t\tonRefreshInit = vars.onRefreshInit && (() => vars.onRefreshInit(self)),\n\t\t\tgetScrollerSize = _getSizeFunc(scroller, isViewport, direction),\n\t\t\tgetScrollerOffsets = _getOffsetsFunc(scroller, isViewport),\n\t\t\tlastSnap = 0,\n\t\t\tlastRefresh = 0,\n\t\t\tprevProgress = 0,\n\t\t\tscrollFunc = _getScrollFunc(scroller, direction),\n\t\t\ttweenTo, pinCache, snapFunc, scroll1, scroll2, start, end, markerStart, markerEnd, markerStartTrigger, markerEndTrigger, markerVars, executingOnRefresh,\n\t\t\tchange, pinOriginalState, pinActiveState, pinState, spacer, offset, pinGetter, pinSetter, pinStart, pinChange, spacingStart, spacerState, markerStartSetter, pinMoves,\n\t\t\tmarkerEndSetter, cs, snap1, snap2, scrubTween, scrubSmooth, snapDurClamp, snapDelayedCall, prevScroll, prevAnimProgress, caMarkerSetter, customRevertReturn;\n\n\t\t// for the sake of efficiency, _startClamp/_endClamp serve like a truthy value indicating that clamping was enabled on the start/end, and ALSO store the actual pre-clamped numeric value. We tap into that in ScrollSmoother for speed effects. So for example, if start=\"clamp(top bottom)\" results in a start of -100 naturally, it would get clamped to 0 but -100 would be stored in _startClamp.\n\t\tself._startClamp = self._endClamp = false;\n\t\tself._dir = direction;\n\t\tanticipatePin *= 45;\n\t\tself.scroller = scroller;\n\t\tself.scroll = containerAnimation ? containerAnimation.time.bind(containerAnimation) : scrollFunc;\n\t\tscroll1 = scrollFunc();\n\t\tself.vars = vars;\n\t\tanimation = animation || vars.animation;\n\t\tif (\"refreshPriority\" in vars) {\n\t\t\t_sort = 1;\n\t\t\tvars.refreshPriority === -9999 && (_primary = self); // used by ScrollSmoother\n\t\t}\n\t\tscrollerCache.tweenScroll = scrollerCache.tweenScroll || {\n\t\t\ttop: _getTweenCreator(scroller, _vertical),\n\t\t\tleft: _getTweenCreator(scroller, _horizontal)\n\t\t};\n\t\tself.tweenTo = tweenTo = scrollerCache.tweenScroll[direction.p];\n\t\tself.scrubDuration = value => {\n\t\t\tscrubSmooth = _isNumber(value) && value;\n\t\t\tif (!scrubSmooth) {\n\t\t\t\tscrubTween && scrubTween.progress(1).kill();\n\t\t\t\tscrubTween = 0;\n\t\t\t} else {\n\t\t\t\tscrubTween ? scrubTween.duration(value) : (scrubTween = gsap.to(animation, {ease: \"expo\", totalProgress: \"+=0\", inherit: false, duration: scrubSmooth, paused: true, onComplete: () => onScrubComplete && onScrubComplete(self)}));\n\t\t\t}\n\t\t};\n\t\tif (animation) {\n\t\t\tanimation.vars.lazy = false;\n\t\t\t(animation._initted && !self.isReverted) || (animation.vars.immediateRender !== false && vars.immediateRender !== false && animation.duration() && animation.render(0, true, true)); // special case: if this ScrollTrigger gets re-initted, a from() tween with a stagger could get initted initially and then reverted on the re-init which means it'll need to get rendered again here to properly display things. Otherwise, See https://gsap.com/forums/topic/36777-scrollsmoother-splittext-nextjs/ and https://codepen.io/GreenSock/pen/eYPyPpd?editors=0010\n\t\t\tself.animation = animation.pause();\n\t\t\tanimation.scrollTrigger = self;\n\t\t\tself.scrubDuration(scrub);\n\t\t\tsnap1 = 0;\n\t\t\tid || (id = animation.vars.id);\n\t\t}\n\n\t\tif (snap) {\n\t\t\t// TODO: potential idea: use legitimate CSS scroll snapping by pushing invisible elements into the DOM that serve as snap positions, and toggle the document.scrollingElement.style.scrollSnapType onToggle. See https://codepen.io/GreenSock/pen/JjLrgWM for a quick proof of concept.\n\t\t\tif (!_isObject(snap) || snap.push) {\n\t\t\t\tsnap = {snapTo: snap};\n\t\t\t}\n\t\t\t(\"scrollBehavior\" in _body.style) && gsap.set(isViewport ? [_body, _docEl] : scroller, {scrollBehavior: \"auto\"}); // smooth scrolling doesn't work with snap.\n\t\t\t_scrollers.forEach(o => _isFunction(o) && o.target === (isViewport ? _doc.scrollingElement || _docEl : scroller) && (o.smooth = false)); // note: set smooth to false on both the vertical and horizontal scroll getters/setters\n\t\t\tsnapFunc = _isFunction(snap.snapTo) ? snap.snapTo : snap.snapTo === \"labels\" ? _getClosestLabel(animation) : snap.snapTo === \"labelsDirectional\" ? _getLabelAtDirection(animation) : snap.directional !== false ? (value, st) => _snapDirectional(snap.snapTo)(value, _getTime() - lastRefresh < 500 ? 0 : st.direction) : gsap.utils.snap(snap.snapTo);\n\t\t\tsnapDurClamp = snap.duration || {min: 0.1, max: 2};\n\t\t\tsnapDurClamp = _isObject(snapDurClamp) ? _clamp(snapDurClamp.min, snapDurClamp.max) : _clamp(snapDurClamp, snapDurClamp);\n\t\t\tsnapDelayedCall = gsap.delayedCall(snap.delay || (scrubSmooth / 2) || 0.1, () => {\n\t\t\t\tlet scroll = scrollFunc(),\n\t\t\t\t\trefreshedRecently = _getTime() - lastRefresh < 500,\n\t\t\t\t\ttween = tweenTo.tween;\n\t\t\t\tif ((refreshedRecently || Math.abs(self.getVelocity()) < 10) && !tween && !_pointerIsDown && lastSnap !== scroll) {\n\t\t\t\t\tlet progress = (scroll - start) / change, // don't use self.progress because this might run between the refresh() and when the scroll position updates and self.progress is set properly in the update() method.\n\t\t\t\t\t\ttotalProgress = animation && !isToggle ? animation.totalProgress() : progress,\n\t\t\t\t\t\tvelocity = refreshedRecently ? 0 : ((totalProgress - snap2) / (_getTime() - _time2) * 1000) || 0,\n\t\t\t\t\t\tchange1 = gsap.utils.clamp(-progress, 1 - progress, _abs(velocity / 2) * velocity / 0.185),\n\t\t\t\t\t\tnaturalEnd = progress + (snap.inertia === false ? 0 : change1),\n\t\t\t\t\t\tendValue, endScroll,\n\t\t\t\t\t\t{ onStart, onInterrupt, onComplete } = snap;\n\t\t\t\t\tendValue = snapFunc(naturalEnd, self);\n\t\t\t\t\t_isNumber(endValue) || (endValue = naturalEnd); // in case the function didn't return a number, fall back to using the naturalEnd\n\t\t\t\t\tendScroll = Math.max(0, Math.round(start + endValue * change));\n\t\t\t\t\tif (scroll <= end && scroll >= start && endScroll !== scroll) {\n\t\t\t\t\t\tif (tween && !tween._initted && tween.data <= _abs(endScroll - scroll)) { // there's an overlapping snap! So we must figure out which one is closer and let that tween live.\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (snap.inertia === false) {\n\t\t\t\t\t\t\tchange1 = endValue - progress;\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttweenTo(endScroll, {\n\t\t\t\t\t\t\tduration: snapDurClamp(_abs( (Math.max(_abs(naturalEnd - totalProgress), _abs(endValue - totalProgress)) * 0.185 / velocity / 0.05) || 0)),\n\t\t\t\t\t\t\tease: snap.ease || \"power3\",\n\t\t\t\t\t\t\tdata: _abs(endScroll - scroll), // record the distance so that if another snap tween occurs (conflict) we can prioritize the closest snap.\n\t\t\t\t\t\t\tonInterrupt: () => snapDelayedCall.restart(true) && onInterrupt && onInterrupt(self),\n\t\t\t\t\t\t\tonComplete() {\n\t\t\t\t\t\t\t\tself.update();\n\t\t\t\t\t\t\t\tlastSnap = scrollFunc();\n\t\t\t\t\t\t\t\tif (animation && !isToggle) { // the resolution of the scrollbar is limited, so we should correct the scrubbed animation's playhead at the end to match EXACTLY where it was supposed to snap\n\t\t\t\t\t\t\t\t\tscrubTween ? scrubTween.resetTo(\"totalProgress\", endValue, animation._tTime / animation._tDur) : animation.progress(endValue);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tsnap1 = snap2 = animation && !isToggle ? animation.totalProgress() : self.progress;\n\t\t\t\t\t\t\t\tonSnapComplete && onSnapComplete(self);\n\t\t\t\t\t\t\t\tonComplete && onComplete(self);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, scroll, change1 * change, endScroll - scroll - change1 * change);\n\t\t\t\t\t\tonStart && onStart(self, tweenTo.tween);\n\t\t\t\t\t}\n\t\t\t\t} else if (self.isActive && lastSnap !== scroll) {\n\t\t\t\t\tsnapDelayedCall.restart(true);\n\t\t\t\t}\n\t\t\t}).pause();\n\t\t}\n\t\tid && (_ids[id] = self);\n\t\ttrigger = self.trigger = _getTarget(trigger || (pin !== true && pin));\n\n\t\t// if a trigger has some kind of scroll-related effect applied that could contaminate the \"y\" or \"x\" position (like a ScrollSmoother effect), we needed a way to temporarily revert it, so we use the stRevert property of the gsCache. It can return another function that we'll call at the end so it can return to its normal state.\n\t\tcustomRevertReturn = trigger && trigger._gsap && trigger._gsap.stRevert;\n\t\tcustomRevertReturn && (customRevertReturn = customRevertReturn(self));\n\n\t\tpin = pin === true ? trigger : _getTarget(pin);\n\t\t_isString(toggleClass) && (toggleClass = {targets: trigger, className: toggleClass});\n\t\tif (pin) {\n\t\t\t(pinSpacing === false || pinSpacing === _margin) || (pinSpacing = !pinSpacing && pin.parentNode && pin.parentNode.style && _getComputedStyle(pin.parentNode).display === \"flex\" ? false : _padding); // if the parent is display: flex, don't apply pinSpacing by default. We should check that pin.parentNode is an element (not shadow dom window)\n\t\t\tself.pin = pin;\n\t\t\tpinCache = gsap.core.getCache(pin);\n\t\t\tif (!pinCache.spacer) { // record the spacer and pinOriginalState on the cache in case someone tries pinning the same element with MULTIPLE ScrollTriggers - we don't want to have multiple spacers or record the \"original\" pin state after it has already been affected by another ScrollTrigger.\n\t\t\t\tif (pinSpacer) {\n\t\t\t\t\tpinSpacer = _getTarget(pinSpacer);\n\t\t\t\t\tpinSpacer && !pinSpacer.nodeType && (pinSpacer = pinSpacer.current || pinSpacer.nativeElement); // for React & Angular\n\t\t\t\t\tpinCache.spacerIsNative = !!pinSpacer;\n\t\t\t\t\tpinSpacer && (pinCache.spacerState = _getState(pinSpacer));\n\t\t\t\t}\n\t\t\t\tpinCache.spacer = spacer = pinSpacer || _doc.createElement(\"div\");\n\t\t\t\tspacer.classList.add(\"pin-spacer\");\n\t\t\t\tid && spacer.classList.add(\"pin-spacer-\" + id);\n\t\t\t\tpinCache.pinState = pinOriginalState = _getState(pin);\n\t\t\t} else {\n\t\t\t\tpinOriginalState = pinCache.pinState;\n\t\t\t}\n\t\t\tvars.force3D !== false && gsap.set(pin, {force3D: true});\n\t\t\tself.spacer = spacer = pinCache.spacer;\n\t\t\tcs = _getComputedStyle(pin);\n\t\t\tspacingStart = cs[pinSpacing + direction.os2];\n\t\t\tpinGetter = gsap.getProperty(pin);\n\t\t\tpinSetter = gsap.quickSetter(pin, direction.a, _px);\n\t\t\t// pin.firstChild && !_maxScroll(pin, direction) && (pin.style.overflow = \"hidden\"); // protects from collapsing margins, but can have unintended consequences as demonstrated here: https://codepen.io/GreenSock/pen/1e42c7a73bfa409d2cf1e184e7a4248d so it was removed in favor of just telling people to set up their CSS to avoid the collapsing margins (overflow: hidden | auto is just one option. Another is border-top: 1px solid transparent).\n\t\t\t_swapPinIn(pin, spacer, cs);\n\t\t\tpinState = _getState(pin);\n\t\t}\n\t\tif (markers) {\n\t\t\tmarkerVars = _isObject(markers) ? _setDefaults(markers, _markerDefaults) : _markerDefaults;\n\t\t\tmarkerStartTrigger = _createMarker(\"scroller-start\", id, scroller, direction, markerVars, 0);\n\t\t\tmarkerEndTrigger = _createMarker(\"scroller-end\", id, scroller, direction, markerVars, 0, markerStartTrigger);\n\t\t\toffset = markerStartTrigger[\"offset\" + direction.op.d2];\n\t\t\tlet content = _getTarget(_getProxyProp(scroller, \"content\") || scroller);\n\t\t\tmarkerStart = this.markerStart = _createMarker(\"start\", id, content, direction, markerVars, offset, 0, containerAnimation);\n\t\t\tmarkerEnd = this.markerEnd = _createMarker(\"end\", id, content, direction, markerVars, offset, 0, containerAnimation);\n\t\t\tcontainerAnimation && (caMarkerSetter = gsap.quickSetter([markerStart, markerEnd], direction.a, _px));\n\t\t\tif ((!useFixedPosition && !(_proxies.length && _getProxyProp(scroller, \"fixedMarkers\") === true))) {\n\t\t\t\t_makePositionable(isViewport ? _body : scroller);\n\t\t\t\tgsap.set([markerStartTrigger, markerEndTrigger], {force3D: true});\n\t\t\t\tmarkerStartSetter = gsap.quickSetter(markerStartTrigger, direction.a, _px);\n\t\t\t\tmarkerEndSetter = gsap.quickSetter(markerEndTrigger, direction.a, _px);\n\t\t\t}\n\t\t}\n\n\t\tif (containerAnimation) {\n\t\t\tlet oldOnUpdate = containerAnimation.vars.onUpdate,\n\t\t\t\toldParams = containerAnimation.vars.onUpdateParams;\n\t\t\tcontainerAnimation.eventCallback(\"onUpdate\", () => {\n\t\t\t\tself.update(0, 0, 1);\n\t\t\t\toldOnUpdate && oldOnUpdate.apply(containerAnimation, oldParams || []);\n\t\t\t});\n\t\t}\n\n\t\tself.previous = () => _triggers[_triggers.indexOf(self) - 1];\n\t\tself.next = () => _triggers[_triggers.indexOf(self) + 1];\n\n\t\tself.revert = (revert, temp) => {\n\t\t\tif (!temp) { return self.kill(true); } // for compatibility with gsap.context() and gsap.matchMedia() which call revert()\n\t\t\tlet r = revert !== false || !self.enabled,\n\t\t\t\tprevRefreshing = _refreshing;\n\t\t\tif (r !== self.isReverted) {\n\t\t\t\tif (r) {\n\t\t\t\t\tprevScroll = Math.max(scrollFunc(), self.scroll.rec || 0); // record the scroll so we can revert later (repositioning/pinning things can affect scroll position). In the static refresh() method, we first record all the scroll positions as a reference.\n\t\t\t\t\tprevProgress = self.progress;\n\t\t\t\t\tprevAnimProgress = animation && animation.progress();\n\t\t\t\t}\n\t\t\t\tmarkerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(m => m.style.display = r ? \"none\" : \"block\");\n\t\t\t\tif (r) {\n\t\t\t\t\t_refreshing = self;\n\t\t\t\t\tself.update(r); // make sure the pin is back in its original position so that all the measurements are correct. do this BEFORE swapping the pin out\n\t\t\t\t}\n\t\t\t\tif (pin && (!pinReparent || !self.isActive)) {\n\t\t\t\t\tif (r) {\n\t\t\t\t\t\t_swapPinOut(pin, spacer, pinOriginalState);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t_swapPinIn(pin, spacer, _getComputedStyle(pin), spacerState);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tr || self.update(r); // when we're restoring, the update should run AFTER swapping the pin into its pin-spacer.\n\t\t\t\t_refreshing = prevRefreshing; // restore. We set it to true during the update() so that things fire properly in there.\n\t\t\t\tself.isReverted = r;\n\t\t\t}\n\t\t}\n\n\t\tself.refresh = (soft, force, position, pinOffset) => { // position is typically only defined if it's coming from setPositions() - it's a way to skip the normal parsing. pinOffset is also only from setPositions() and is mostly related to fancy stuff we need to do in ScrollSmoother with effects\n\t\t\tif ((_refreshing || !self.enabled) && !force) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (pin && soft && _lastScrollTime) {\n\t\t\t\t_addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t!_refreshingAll && onRefreshInit && onRefreshInit(self);\n\t\t\t_refreshing = self;\n\t\t\tif (tweenTo.tween && !position) { // we skip this if a position is passed in because typically that's from .setPositions() and it's best to allow in-progress snapping to continue.\n\t\t\t\ttweenTo.tween.kill();\n\t\t\t\ttweenTo.tween = 0;\n\t\t\t}\n\t\t\tscrubTween && scrubTween.pause();\n\n\t\t\tif (invalidateOnRefresh && animation) {\n\t\t\t\tanimation.revert({kill: false}).invalidate();\n\t\t\t\tanimation.getChildren && animation.getChildren(true, true, false).forEach(t => t.vars.immediateRender && t.render(0, true, true)); // any from() or fromTo() tweens inside a timeline should render immediately (well, unless they have immediateRender: false)\n\t\t\t}\n\n\t\t\tself.isReverted || self.revert(true, true);\n\t\t\tself._subPinOffset = false; // we'll set this to true in the sub-pins if we find any\n\t\t\tlet size = getScrollerSize(),\n\t\t\t\tscrollerBounds = getScrollerOffsets(),\n\t\t\t\tmax = containerAnimation ? containerAnimation.duration() : _maxScroll(scroller, direction),\n\t\t\t\tisFirstRefresh = change <= 0.01 || !change,\n\t\t\t\toffset = 0,\n\t\t\t\totherPinOffset = pinOffset || 0,\n\t\t\t\tparsedEnd = _isObject(position) ? position.end : vars.end,\n\t\t\t\tparsedEndTrigger = vars.endTrigger || trigger,\n\t\t\t\tparsedStart = _isObject(position) ? position.start : (vars.start || (vars.start === 0 || !trigger ? 0 : (pin ? \"0 0\" : \"0 100%\"))),\n\t\t\t\tpinnedContainer = self.pinnedContainer = vars.pinnedContainer && _getTarget(vars.pinnedContainer, self),\n\t\t\t\ttriggerIndex = (trigger && Math.max(0, _triggers.indexOf(self))) || 0,\n\t\t\t\ti = triggerIndex,\n\t\t\t\tcs, bounds, scroll, isVertical, override, curTrigger, curPin, oppositeScroll, initted, revertedPins, forcedOverflow, markerStartOffset, markerEndOffset;\n\t\t\tif (markers && _isObject(position)) { // if we alter the start/end positions with .setPositions(), it generally feeds in absolute NUMBERS which don't convey information about where to line up the markers, so to keep it intuitive, we record how far the trigger positions shift after applying the new numbers and then offset by that much in the opposite direction. We do the same to the associated trigger markers too of course.\n\t\t\t\tmarkerStartOffset = gsap.getProperty(markerStartTrigger, direction.p);\n\t\t\t\tmarkerEndOffset = gsap.getProperty(markerEndTrigger, direction.p);\n\t\t\t}\n\t\t\twhile (i-- > 0) { // user might try to pin the same element more than once, so we must find any prior triggers with the same pin, revert them, and determine how long they're pinning so that we can offset things appropriately. Make sure we revert from last to first so that things \"rewind\" properly.\n\t\t\t\tcurTrigger = _triggers[i];\n\t\t\t\tcurTrigger.end || curTrigger.refresh(0, 1) || (_refreshing = self); // if it's a timeline-based trigger that hasn't been fully initialized yet because it's waiting for 1 tick, just force the refresh() here, otherwise if it contains a pin that's supposed to affect other ScrollTriggers further down the page, they won't be adjusted properly.\n\t\t\t\tcurPin = curTrigger.pin;\n\t\t\t\tif (curPin && (curPin === trigger || curPin === pin || curPin === pinnedContainer) && !curTrigger.isReverted) {\n\t\t\t\t\trevertedPins || (revertedPins = []);\n\t\t\t\t\trevertedPins.unshift(curTrigger); // we'll revert from first to last to make sure things reach their end state properly\n\t\t\t\t\tcurTrigger.revert(true, true);\n\t\t\t\t}\n\t\t\t\tif (curTrigger !== _triggers[i]) { // in case it got removed.\n\t\t\t\t\ttriggerIndex--;\n\t\t\t\t\ti--;\n\t\t\t\t}\n\t\t\t}\n\t\t\t_isFunction(parsedStart) && (parsedStart = parsedStart(self));\n\t\t\tparsedStart = _parseClamp(parsedStart, \"start\", self);\n\t\t\tstart = _parsePosition(parsedStart, trigger, size, direction, scrollFunc(), markerStart, markerStartTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation, self._startClamp && \"_startClamp\") || (pin ? -0.001 : 0);\n\t\t\t_isFunction(parsedEnd) && (parsedEnd = parsedEnd(self));\n\t\t\tif (_isString(parsedEnd) && !parsedEnd.indexOf(\"+=\")) {\n\t\t\t\tif (~parsedEnd.indexOf(\" \")) {\n\t\t\t\t\tparsedEnd = (_isString(parsedStart) ? parsedStart.split(\" \")[0] : \"\") + parsedEnd;\n\t\t\t\t} else {\n\t\t\t\t\toffset = _offsetToPx(parsedEnd.substr(2), size);\n\t\t\t\t\tparsedEnd = _isString(parsedStart) ? parsedStart : (containerAnimation ? gsap.utils.mapRange(0, containerAnimation.duration(), containerAnimation.scrollTrigger.start, containerAnimation.scrollTrigger.end, start) : start) + offset; // _parsePosition won't factor in the offset if the start is a number, so do it here.\n\t\t\t\t\tparsedEndTrigger = trigger;\n\t\t\t\t}\n\t\t\t}\n\t\t\tparsedEnd = _parseClamp(parsedEnd, \"end\", self);\n\t\t\tend = Math.max(start, _parsePosition(parsedEnd || (parsedEndTrigger ? \"100% 0\" : max), parsedEndTrigger, size, direction, scrollFunc() + offset, markerEnd, markerEndTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation, self._endClamp && \"_endClamp\")) || -0.001;\n\n\t\t\toffset = 0;\n\t\t\ti = triggerIndex;\n\t\t\twhile (i--) {\n\t\t\t\tcurTrigger = _triggers[i];\n\t\t\t\tcurPin = curTrigger.pin;\n\t\t\t\tif (curPin && curTrigger.start - curTrigger._pinPush <= start && !containerAnimation && curTrigger.end > 0) {\n\t\t\t\t\tcs = curTrigger.end - (self._startClamp ? Math.max(0, curTrigger.start) : curTrigger.start);\n\t\t\t\t\tif (((curPin === trigger && curTrigger.start - curTrigger._pinPush < start) || curPin === pinnedContainer) && isNaN(parsedStart)) { // numeric start values shouldn't be offset at all - treat them as absolute\n\t\t\t\t\t\toffset += cs * (1 - curTrigger.progress);\n\t\t\t\t\t}\n\t\t\t\t\tcurPin === pin && (otherPinOffset += cs);\n\t\t\t\t}\n\t\t\t}\n\t\t\tstart += offset;\n\t\t\tend += offset;\n\t\t\tself._startClamp && (self._startClamp += offset);\n\n\t\t\tif (self._endClamp && !_refreshingAll) {\n\t\t\t\tself._endClamp = end || -0.001;\n\t\t\t\tend = Math.min(end, _maxScroll(scroller, direction));\n\t\t\t}\n\t\t\tchange = (end - start) || ((start -= 0.01) && 0.001);\n\n\t\t\tif (isFirstRefresh) { // on the very first refresh(), the prevProgress couldn't have been accurate yet because the start/end were never calculated, so we set it here. Before 3.11.5, it could lead to an inaccurate scroll position restoration with snapping.\n\t\t\t\tprevProgress = gsap.utils.clamp(0, 1, gsap.utils.normalize(start, end, prevScroll));\n\t\t\t}\n\t\t\tself._pinPush = otherPinOffset;\n\t\t\tif (markerStart && offset) { // offset the markers if necessary\n\t\t\t\tcs = {};\n\t\t\t\tcs[direction.a] = \"+=\" + offset;\n\t\t\t\tpinnedContainer && (cs[direction.p] = \"-=\" + scrollFunc());\n\t\t\t\tgsap.set([markerStart, markerEnd], cs);\n\t\t\t}\n\n\t\t\tif (pin && !(_clampingMax && self.end >= _maxScroll(scroller, direction))) {\n\t\t\t\tcs = _getComputedStyle(pin);\n\t\t\t\tisVertical = direction === _vertical;\n\t\t\t\tscroll = scrollFunc(); // recalculate because the triggers can affect the scroll\n\t\t\t\tpinStart = parseFloat(pinGetter(direction.a)) + otherPinOffset;\n\t\t\t\tif (!max && end > 1) { // makes sure the scroller has a scrollbar, otherwise if something has width: 100%, for example, it would be too big (exclude the scrollbar). See https://gsap.com/forums/topic/25182-scrolltrigger-width-of-page-increase-where-markers-are-set-to-false/\n\t\t\t\t\tforcedOverflow = (isViewport ? (_doc.scrollingElement || _docEl) : scroller).style;\n\t\t\t\t\tforcedOverflow = {style: forcedOverflow, value: forcedOverflow[\"overflow\" + direction.a.toUpperCase()]};\n\t\t\t\t\tif (isViewport && _getComputedStyle(_body)[\"overflow\" + direction.a.toUpperCase()] !== \"scroll\") { // avoid an extra scrollbar if BOTH <html> and <body> have overflow set to \"scroll\"\n\t\t\t\t\t\tforcedOverflow.style[\"overflow\" + direction.a.toUpperCase()] = \"scroll\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t_swapPinIn(pin, spacer, cs);\n\t\t\t\tpinState = _getState(pin);\n\t\t\t\t// transforms will interfere with the top/left/right/bottom placement, so remove them temporarily. getBoundingClientRect() factors in transforms.\n\t\t\t\tbounds = _getBounds(pin, true);\n\t\t\t\toppositeScroll = useFixedPosition && _getScrollFunc(scroller, isVertical ? _horizontal : _vertical)();\n\t\t\t\tif (pinSpacing) {\n\t\t\t\t\tspacerState = [pinSpacing + direction.os2, change + otherPinOffset + _px];\n\t\t\t\t\tspacerState.t = spacer;\n\t\t\t\t\ti = (pinSpacing === _padding) ? _getSize(pin, direction) + change + otherPinOffset : 0;\n\t\t\t\t\tif (i) {\n\t\t\t\t\t\tspacerState.push(direction.d, i + _px); // for box-sizing: border-box (must include padding).\n\t\t\t\t\t\tspacer.style.flexBasis !== \"auto\" && (spacer.style.flexBasis = i + _px);\n\t\t\t\t\t}\n\t\t\t\t\t_setState(spacerState);\n\t\t\t\t\tif (pinnedContainer) { // in ScrollTrigger.refresh(), we need to re-evaluate the pinContainer's size because this pinSpacing may stretch it out, but we can't just add the exact distance because depending on layout, it may not push things down or it may only do so partially.\n\t\t\t\t\t\t_triggers.forEach(t => {\n\t\t\t\t\t\t\tif (t.pin === pinnedContainer && t.vars.pinSpacing !== false) {\n\t\t\t\t\t\t\t\tt._subPinOffset = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tuseFixedPosition && scrollFunc(prevScroll);\n\t\t\t\t} else {\n\t\t\t\t\ti = _getSize(pin, direction);\n\t\t\t\t\ti && spacer.style.flexBasis !== \"auto\" && (spacer.style.flexBasis = i + _px);\n\t\t\t\t}\n\t\t\t\tif (useFixedPosition) {\n\t\t\t\t\toverride = {\n\t\t\t\t\t\ttop: (bounds.top + (isVertical ? scroll - start : oppositeScroll)) + _px,\n\t\t\t\t\t\tleft: (bounds.left + (isVertical ? oppositeScroll : scroll - start)) + _px,\n\t\t\t\t\t\tboxSizing: \"border-box\",\n\t\t\t\t\t\tposition: \"fixed\"\n\t\t\t\t\t};\n\t\t\t\t\toverride[_width] = override[\"max\" + _Width] = Math.ceil(bounds.width) + _px;\n\t\t\t\t\toverride[_height] = override[\"max\" + _Height] = Math.ceil(bounds.height) + _px;\n\t\t\t\t\toverride[_margin] = override[_margin + _Top] = override[_margin + _Right] = override[_margin + _Bottom] = override[_margin + _Left] = \"0\";\n\t\t\t\t\toverride[_padding] = cs[_padding];\n\t\t\t\t\toverride[_padding + _Top] = cs[_padding + _Top];\n\t\t\t\t\toverride[_padding + _Right] = cs[_padding + _Right];\n\t\t\t\t\toverride[_padding + _Bottom] = cs[_padding + _Bottom];\n\t\t\t\t\toverride[_padding + _Left] = cs[_padding + _Left];\n\t\t\t\t\tpinActiveState = _copyState(pinOriginalState, override, pinReparent);\n\t\t\t\t\t_refreshingAll && scrollFunc(0);\n\t\t\t\t}\n\t\t\t\tif (animation) { // the animation might be affecting the transform, so we must jump to the end, check the value, and compensate accordingly. Otherwise, when it becomes unpinned, the pinSetter() will get set to a value that doesn't include whatever the animation did.\n\t\t\t\t\tinitted = animation._initted; // if not, we must invalidate() after this step, otherwise it could lock in starting values prematurely.\n\t\t\t\t\t_suppressOverwrites(1);\n\t\t\t\t\tanimation.render(animation.duration(), true, true);\n\t\t\t\t\tpinChange = pinGetter(direction.a) - pinStart + change + otherPinOffset;\n\t\t\t\t\tpinMoves = Math.abs(change - pinChange) > 1;\n\t\t\t\t\tuseFixedPosition && pinMoves && pinActiveState.splice(pinActiveState.length - 2, 2); // transform is the last property/value set in the state Array. Since the animation is controlling that, we should omit it.\n\t\t\t\t\tanimation.render(0, true, true);\n\t\t\t\t\tinitted || animation.invalidate(true);\n\t\t\t\t\tanimation.parent || animation.totalTime(animation.totalTime()); // if, for example, a toggleAction called play() and then refresh() happens and when we render(1) above, it would cause the animation to complete and get removed from its parent, so this makes sure it gets put back in.\n\t\t\t\t\t_suppressOverwrites(0);\n\t\t\t\t} else {\n\t\t\t\t\tpinChange = change\n\t\t\t\t}\n\t\t\t\tforcedOverflow && (forcedOverflow.value ? (forcedOverflow.style[\"overflow\" + direction.a.toUpperCase()] = forcedOverflow.value) : forcedOverflow.style.removeProperty(\"overflow-\" + direction.a));\n\t\t\t} else if (trigger && scrollFunc() && !containerAnimation) { // it may be INSIDE a pinned element, so walk up the tree and look for any elements with _pinOffset to compensate because anything with pinSpacing that's already scrolled would throw off the measurements in getBoundingClientRect()\n\t\t\t\tbounds = trigger.parentNode;\n\t\t\t\twhile (bounds && bounds !== _body) {\n\t\t\t\t\tif (bounds._pinOffset) {\n\t\t\t\t\t\tstart -= bounds._pinOffset;\n\t\t\t\t\t\tend -= bounds._pinOffset;\n\t\t\t\t\t}\n\t\t\t\t\tbounds = bounds.parentNode;\n\t\t\t\t}\n\t\t\t}\n\t\t\trevertedPins && revertedPins.forEach(t => t.revert(false, true));\n\t\t\tself.start = start;\n\t\t\tself.end = end;\n\t\t\tscroll1 = scroll2 = _refreshingAll ? prevScroll : scrollFunc(); // reset velocity\n\t\t\tif (!containerAnimation && !_refreshingAll) {\n\t\t\t\tscroll1 < prevScroll && scrollFunc(prevScroll);\n\t\t\t\tself.scroll.rec = 0;\n\t\t\t}\n\t\t\tself.revert(false, true);\n\t\t\tlastRefresh = _getTime();\n\t\t\tif (snapDelayedCall) {\n\t\t\t\tlastSnap = -1; // just so snapping gets re-enabled, clear out any recorded last value\n\t\t\t\t// self.isActive && scrollFunc(start + change * prevProgress); // previously this line was here to ensure that when snapping kicks in, it's from the previous progress but in some cases that's not desirable, like an all-page ScrollTrigger when new content gets added to the page, that'd totally change the progress.\n\t\t\t\tsnapDelayedCall.restart(true);\n\t\t\t}\n\t\t\t_refreshing = 0;\n\t\t\tanimation && isToggle && (animation._initted || prevAnimProgress) && animation.progress() !== prevAnimProgress && animation.progress(prevAnimProgress || 0, true).render(animation.time(), true, true); // must force a re-render because if saveStyles() was used on the target(s), the styles could have been wiped out during the refresh().\n\t\t\tif (isFirstRefresh || prevProgress !== self.progress || containerAnimation || invalidateOnRefresh || (animation && !animation._initted)) { // ensures that the direction is set properly (when refreshing, progress is set back to 0 initially, then back again to wherever it needs to be) and that callbacks are triggered.\n\t\t\t\tanimation && !isToggle && (animation._initted || prevProgress || animation.vars.immediateRender !== false) && animation.totalProgress(containerAnimation && start < -0.001 && !prevProgress ? gsap.utils.normalize(start, end, 0) : prevProgress, true); // to avoid issues where animation callbacks like onStart aren't triggered.\n\t\t\t\tself.progress = isFirstRefresh || ((scroll1 - start) / change === prevProgress) ? 0 : prevProgress;\n\t\t\t}\n\t\t\tpin && pinSpacing && (spacer._pinOffset = Math.round(self.progress * pinChange));\n\t\t\tscrubTween && scrubTween.invalidate();\n\n\t\t\tif (!isNaN(markerStartOffset)) { // numbers were passed in for the position which are absolute, so instead of just putting the markers at the very bottom of the viewport, we figure out how far they shifted down (it's safe to assume they were originally positioned in closer relation to the trigger element with values like \"top\", \"center\", a percentage or whatever, so we offset that much in the opposite direction to basically revert them to the relative position thy were at previously.\n\t\t\t\tmarkerStartOffset -= gsap.getProperty(markerStartTrigger, direction.p);\n\t\t\t\tmarkerEndOffset -= gsap.getProperty(markerEndTrigger, direction.p);\n\t\t\t\t_shiftMarker(markerStartTrigger, direction, markerStartOffset);\n\t\t\t\t_shiftMarker(markerStart, direction, markerStartOffset - (pinOffset || 0));\n\t\t\t\t_shiftMarker(markerEndTrigger, direction, markerEndOffset);\n\t\t\t\t_shiftMarker(markerEnd, direction, markerEndOffset - (pinOffset || 0));\n\t\t\t}\n\n\t\t\tisFirstRefresh && !_refreshingAll && self.update(); // edge case - when you reload a page when it's already scrolled down, some browsers fire a \"scroll\" event before DOMContentLoaded, triggering an updateAll(). If we don't update the self.progress as part of refresh(), then when it happens next, it may record prevProgress as 0 when it really shouldn't, potentially causing a callback in an animation to fire again.\n\n\t\t\tif (onRefresh && !_refreshingAll && !executingOnRefresh) { // when refreshing all, we do extra work to correct pinnedContainer sizes and ensure things don't exceed the maxScroll, so we should do all the refreshes at the end after all that work so that the start/end values are corrected.\n\t\t\t\texecutingOnRefresh = true;\n\t\t\t\tonRefresh(self);\n\t\t\t\texecutingOnRefresh = false;\n\t\t\t}\n\t\t};\n\n\t\tself.getVelocity = () => ((scrollFunc() - scroll2) / (_getTime() - _time2) * 1000) || 0;\n\n\t\tself.endAnimation = () => {\n\t\t\t_endAnimation(self.callbackAnimation);\n\t\t\tif (animation) {\n\t\t\t\tscrubTween ? scrubTween.progress(1) : (!animation.paused() ? _endAnimation(animation, animation.reversed()) : isToggle || _endAnimation(animation, self.direction < 0, 1));\n\t\t\t}\n\t\t};\n\n\t\tself.labelToScroll = label => animation && animation.labels && ((start || self.refresh() || start) + (animation.labels[label] / animation.duration()) * change) || 0;\n\n\t\tself.getTrailing = name => {\n\t\t\tlet i = _triggers.indexOf(self),\n\t\t\t\ta = self.direction > 0 ? _triggers.slice(0, i).reverse() : _triggers.slice(i+1);\n\t\t\treturn (_isString(name) ? a.filter(t => t.vars.preventOverlaps === name) : a).filter(t => self.direction > 0 ? t.end <= start : t.start >= end);\n\t\t};\n\n\n\t\tself.update = (reset, recordVelocity, forceFake) => {\n\t\t\tif (containerAnimation && !forceFake && !reset) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tlet scroll = _refreshingAll === true ? prevScroll : self.scroll(),\n\t\t\t\tp = reset ? 0 : (scroll - start) / change,\n\t\t\t\tclipped = p < 0 ? 0 : p > 1 ? 1 : p || 0,\n\t\t\t\tprevProgress = self.progress,\n\t\t\t\tisActive, wasActive, toggleState, action, stateChanged, toggled, isAtMax, isTakingAction;\n\t\t\tif (recordVelocity) {\n\t\t\t\tscroll2 = scroll1;\n\t\t\t\tscroll1 = containerAnimation ? scrollFunc() : scroll;\n\t\t\t\tif (snap) {\n\t\t\t\t\tsnap2 = snap1;\n\t\t\t\t\tsnap1 = animation && !isToggle ? animation.totalProgress() : clipped;\n\t\t\t\t}\n\t\t\t}\n\t\t\t// anticipate the pinning a few ticks ahead of time based on velocity to avoid a visual glitch due to the fact that most browsers do scrolling on a separate thread (not synced with requestAnimationFrame).\n\t\t\tif (anticipatePin && pin && !_refreshing && !_startup && _lastScrollTime) {\n\t\t\t\tif (!clipped && start < scroll + ((scroll - scroll2) / (_getTime() - _time2)) * anticipatePin) {\n\t\t\t\t\tclipped = 0.0001;\n\t\t\t\t} else if (clipped === 1 && end > scroll + ((scroll - scroll2) / (_getTime() - _time2)) * anticipatePin) {\n\t\t\t\t\tclipped = 0.9999;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (clipped !== prevProgress && self.enabled) {\n\t\t\t\tisActive = self.isActive = !!clipped && clipped < 1;\n\t\t\t\twasActive = !!prevProgress && prevProgress < 1;\n\t\t\t\ttoggled = isActive !== wasActive;\n\t\t\t\tstateChanged = toggled || !!clipped !== !!prevProgress; // could go from start all the way to end, thus it didn't toggle but it did change state in a sense (may need to fire a callback)\n\t\t\t\tself.direction = clipped > prevProgress ? 1 : -1;\n\t\t\t\tself.progress = clipped;\n\n\t\t\t\tif (stateChanged && !_refreshing) {\n\t\t\t\t\ttoggleState = clipped && !prevProgress ? 0 : clipped === 1 ? 1 : prevProgress === 1 ? 2 : 3; // 0 = enter, 1 = leave, 2 = enterBack, 3 = leaveBack (we prioritize the FIRST encounter, thus if you scroll really fast past the onEnter and onLeave in one tick, it'd prioritize onEnter.\n\t\t\t\t\tif (isToggle) {\n\t\t\t\t\t\taction = (!toggled && toggleActions[toggleState + 1] !== \"none\" && toggleActions[toggleState + 1]) || toggleActions[toggleState]; // if it didn't toggle, that means it shot right past and since we prioritize the \"enter\" action, we should switch to the \"leave\" in this case (but only if one is defined)\n\t\t\t\t\t\tisTakingAction = animation && (action === \"complete\" || action === \"reset\" || action in animation);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tpreventOverlaps && (toggled || isTakingAction) && (isTakingAction || scrub || !animation) && (_isFunction(preventOverlaps) ? preventOverlaps(self) : self.getTrailing(preventOverlaps).forEach(t => t.endAnimation()));\n\n\t\t\t\tif (!isToggle) {\n\t\t\t\t\tif (scrubTween && !_refreshing && !_startup) {\n\t\t\t\t\t\t(scrubTween._dp._time - scrubTween._start !== scrubTween._time) && scrubTween.render(scrubTween._dp._time - scrubTween._start); // if there's a scrub on both the container animation and this one (or a ScrollSmoother), the update order would cause this one not to have rendered yet, so it wouldn't make any progress before we .restart() it heading toward the new progress so it'd appear stuck thus we force a render here.\n\t\t\t\t\t\tif (scrubTween.resetTo) {\n\t\t\t\t\t\t\tscrubTween.resetTo(\"totalProgress\", clipped, animation._tTime / animation._tDur);\n\t\t\t\t\t\t} else { // legacy support (courtesy), before 3.10.0\n\t\t\t\t\t\t\tscrubTween.vars.totalProgress = clipped;\n\t\t\t\t\t\t\tscrubTween.invalidate().restart();\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (animation) {\n\t\t\t\t\t\tanimation.totalProgress(clipped, !!(_refreshing && (lastRefresh || reset)));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (pin) {\n\t\t\t\t\treset && pinSpacing && (spacer.style[pinSpacing + direction.os2] = spacingStart);\n\t\t\t\t\tif (!useFixedPosition) {\n\t\t\t\t\t\tpinSetter(_round(pinStart + pinChange * clipped));\n\t\t\t\t\t} else if (stateChanged) {\n\t\t\t\t\t\tisAtMax = !reset && clipped > prevProgress && end + 1 > scroll && scroll + 1 >= _maxScroll(scroller, direction); // if it's at the VERY end of the page, don't switch away from position: fixed because it's pointless and it could cause a brief flash when the user scrolls back up (when it gets pinned again)\n\t\t\t\t\t\tif (pinReparent) {\n\t\t\t\t\t\t\tif (!reset && (isActive || isAtMax)) {\n\t\t\t\t\t\t\t\tlet bounds = _getBounds(pin, true),\n\t\t\t\t\t\t\t\t\toffset = scroll - start;\n\t\t\t\t\t\t\t\t_reparent(pin, _body, (bounds.top + (direction === _vertical ? offset : 0)) + _px, (bounds.left + (direction === _vertical ? 0 : offset)) + _px);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t_reparent(pin, spacer);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t_setState(isActive || isAtMax ? pinActiveState : pinState);\n\t\t\t\t\t\t(pinMoves && clipped < 1 && isActive) || pinSetter(pinStart + (clipped === 1 && !isAtMax ? pinChange : 0));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tsnap && !tweenTo.tween && !_refreshing && !_startup && snapDelayedCall.restart(true);\n\t\t\t\ttoggleClass && (toggled || (once && clipped && (clipped < 1 || !_limitCallbacks))) && _toArray(toggleClass.targets).forEach(el => el.classList[isActive || once ? \"add\" : \"remove\"](toggleClass.className)); // classes could affect positioning, so do it even if reset or refreshing is true.\n\t\t\t\tonUpdate && !isToggle && !reset && onUpdate(self);\n\t\t\t\tif (stateChanged && !_refreshing) {\n\t\t\t\t\tif (isToggle) {\n\t\t\t\t\t\tif (isTakingAction) {\n\t\t\t\t\t\t\tif (action === \"complete\") {\n\t\t\t\t\t\t\t\tanimation.pause().totalProgress(1);\n\t\t\t\t\t\t\t} else if (action === \"reset\") {\n\t\t\t\t\t\t\t\tanimation.restart(true).pause();\n\t\t\t\t\t\t\t} else if (action === \"restart\") {\n\t\t\t\t\t\t\t\tanimation.restart(true);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tanimation[action]();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tonUpdate && onUpdate(self);\n\t\t\t\t\t}\n\t\t\t\t\tif (toggled || !_limitCallbacks) { // on startup, the page could be scrolled and we don't want to fire callbacks that didn't toggle. For example onEnter shouldn't fire if the ScrollTrigger isn't actually entered.\n\t\t\t\t\t\tonToggle && toggled && _callback(self, onToggle);\n\t\t\t\t\t\tcallbacks[toggleState] && _callback(self, callbacks[toggleState]);\n\t\t\t\t\t\tonce && (clipped === 1 ? self.kill(false, 1) : (callbacks[toggleState] = 0)); // a callback shouldn't be called again if once is true.\n\t\t\t\t\t\tif (!toggled) { // it's possible to go completely past, like from before the start to after the end (or vice-versa) in which case BOTH callbacks should be fired in that order\n\t\t\t\t\t\t\ttoggleState = clipped === 1 ? 1 : 3;\n\t\t\t\t\t\t\tcallbacks[toggleState] && _callback(self, callbacks[toggleState]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (fastScrollEnd && !isActive && Math.abs(self.getVelocity()) > (_isNumber(fastScrollEnd) ? fastScrollEnd : 2500)) {\n\t\t\t\t\t\t_endAnimation(self.callbackAnimation);\n\t\t\t\t\t\tscrubTween ? scrubTween.progress(1) : _endAnimation(animation, action === \"reverse\" ? 1 : !clipped, 1);\n\t\t\t\t\t}\n\t\t\t\t} else if (isToggle && onUpdate && !_refreshing) {\n\t\t\t\t\tonUpdate(self);\n\t\t\t\t}\n\t\t\t}\n\t\t\t// update absolutely-positioned markers (only if the scroller isn't the viewport)\n\t\t\tif (markerEndSetter) {\n\t\t\t\tlet n = containerAnimation ? scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0) : scroll;\n\t\t\t\tmarkerStartSetter(n + (markerStartTrigger._isFlipped ? 1 : 0));\n\t\t\t\tmarkerEndSetter(n);\n\t\t\t}\n\t\t\tcaMarkerSetter && caMarkerSetter(-scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0));\n\t\t};\n\n\t\tself.enable = (reset, refresh) => {\n\t\t\tif (!self.enabled) {\n\t\t\t\tself.enabled = true;\n\t\t\t\t_addListener(scroller, \"resize\", _onResize);\n\t\t\t\tisViewport || _addListener(scroller, \"scroll\", _onScroll);\n\t\t\t\tonRefreshInit && _addListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\t\t\t\tif (reset !== false) {\n\t\t\t\t\tself.progress = prevProgress = 0;\n\t\t\t\t\tscroll1 = scroll2 = lastSnap = scrollFunc();\n\t\t\t\t}\n\t\t\t\trefresh !== false && self.refresh();\n\t\t\t}\n\t\t};\n\n\t\tself.getTween = snap => snap && tweenTo ? tweenTo.tween : scrubTween;\n\n\t\tself.setPositions = (newStart, newEnd, keepClamp, pinOffset) => { // doesn't persist after refresh()! Intended to be a way to override values that were set during refresh(), like you could set it in onRefresh()\n\t\t\tif (containerAnimation) { // convert ratios into scroll positions. Remember, start/end values on ScrollTriggers that have a containerAnimation refer to the time (in seconds), NOT scroll positions.\n\t\t\t\tlet st = containerAnimation.scrollTrigger,\n\t\t\t\t\tduration = containerAnimation.duration(),\n\t\t\t\t\tchange = st.end - st.start;\n\t\t\t\tnewStart = st.start + change * newStart / duration;\n\t\t\t\tnewEnd = st.start + change * newEnd / duration;\n\t\t\t}\n\t\t\tself.refresh(false, false, {start: _keepClamp(newStart, keepClamp && !!self._startClamp), end: _keepClamp(newEnd, keepClamp && !!self._endClamp)}, pinOffset);\n\t\t\tself.update();\n\t\t};\n\n\t\tself.adjustPinSpacing = amount => {\n\t\t\tif (spacerState && amount) {\n\t\t\t\tlet i = spacerState.indexOf(direction.d) + 1;\n\t\t\t\tspacerState[i] = (parseFloat(spacerState[i]) + amount) + _px;\n\t\t\t\tspacerState[1] = (parseFloat(spacerState[1]) + amount) + _px;\n\t\t\t\t_setState(spacerState);\n\t\t\t}\n\t\t};\n\n\t\tself.disable = (reset, allowAnimation) => {\n\t\t\tif (self.enabled) {\n\t\t\t\treset !== false && self.revert(true, true);\n\t\t\t\tself.enabled = self.isActive = false;\n\t\t\t\tallowAnimation || (scrubTween && scrubTween.pause());\n\t\t\t\tprevScroll = 0;\n\t\t\t\tpinCache && (pinCache.uncache = 1);\n\t\t\t\tonRefreshInit && _removeListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\t\t\t\tif (snapDelayedCall) {\n\t\t\t\t\tsnapDelayedCall.pause();\n\t\t\t\t\ttweenTo.tween && tweenTo.tween.kill() && (tweenTo.tween = 0);\n\t\t\t\t}\n\t\t\t\tif (!isViewport) {\n\t\t\t\t\tlet i = _triggers.length;\n\t\t\t\t\twhile (i--) {\n\t\t\t\t\t\tif (_triggers[i].scroller === scroller && _triggers[i] !== self) {\n\t\t\t\t\t\t\treturn; //don't remove the listeners if there are still other triggers referencing it.\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t_removeListener(scroller, \"resize\", _onResize);\n\t\t\t\t\tisViewport || _removeListener(scroller, \"scroll\", _onScroll);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\tself.kill = (revert, allowAnimation) => {\n\t\t\tself.disable(revert, allowAnimation);\n\t\t\tscrubTween && !allowAnimation && scrubTween.kill();\n\t\t\tid && (delete _ids[id]);\n\t\t\tlet i = _triggers.indexOf(self);\n\t\t\ti >= 0 && _triggers.splice(i, 1);\n\t\t\ti === _i && _direction > 0 && _i--; // if we're in the middle of a refresh() or update(), splicing would cause skips in the index, so adjust...\n\n\t\t\t// if no other ScrollTrigger instances of the same scroller are found, wipe out any recorded scroll position. Otherwise, in a single page application, for example, it could maintain scroll position when it really shouldn't.\n\t\t\ti = 0;\n\t\t\t_triggers.forEach(t => t.scroller === self.scroller && (i = 1));\n\t\t\ti || _refreshingAll || (self.scroll.rec = 0);\n\n\t\t\tif (animation) {\n\t\t\t\tanimation.scrollTrigger = null;\n\t\t\t\trevert && animation.revert({kill: false});\n\t\t\t\tallowAnimation || animation.kill();\n\t\t\t}\n\t\t\tmarkerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(m => m.parentNode && m.parentNode.removeChild(m));\n\t\t\t_primary === self && (_primary = 0);\n\t\t\tif (pin) {\n\t\t\t\tpinCache && (pinCache.uncache = 1);\n\t\t\t\ti = 0;\n\t\t\t\t_triggers.forEach(t => t.pin === pin && i++);\n\t\t\t\ti || (pinCache.spacer = 0); // if there aren't any more ScrollTriggers with the same pin, remove the spacer, otherwise it could be contaminated with old/stale values if the user re-creates a ScrollTrigger for the same element.\n\t\t\t}\n\t\t\tvars.onKill && vars.onKill(self);\n\t\t};\n\n\t\t_triggers.push(self);\n\t\tself.enable(false, false);\n\t\tcustomRevertReturn && customRevertReturn(self);\n\n\t\tif (animation && animation.add && !change) { // if the animation is a timeline, it may not have been populated yet, so it wouldn't render at the proper place on the first refresh(), thus we should schedule one for the next tick. If \"change\" is defined, we know it must be re-enabling, thus we can refresh() right away.\n\t\t\tlet updateFunc = self.update; // some browsers may fire a scroll event BEFORE a tick elapses and/or the DOMContentLoaded fires. So there's a chance update() will be called BEFORE a refresh() has happened on a Timeline-attached ScrollTrigger which means the start/end won't be calculated yet. We don't want to add conditional logic inside the update() method (like check to see if end is defined and if not, force a refresh()) because that's a function that gets hit a LOT (performance). So we swap out the real update() method for this one that'll re-attach it the first time it gets called and of course forces a refresh().\n\t\t\tself.update = () => {\n\t\t\t\tself.update = updateFunc;\n\t\t\t\t_scrollers.cache++; // otherwise a cached scroll position may get used in the refresh() in a very rare scenario, like if ScrollTriggers are created inside a DOMContentLoaded event and the queued requestAnimationFrame() fires beforehand. See https://gsap.com/community/forums/topic/41267-scrolltrigger-breaks-on-refresh-when-using-domcontentloaded/\n\t\t\t\tstart || end || self.refresh();\n\t\t\t};\n\t\t\tgsap.delayedCall(0.01, self.update);\n\t\t\tchange = 0.01;\n\t\t\tstart = end = 0;\n\t\t} else {\n\t\t\tself.refresh();\n\t\t}\n\t\tpin && _queueRefreshAll(); // pinning could affect the positions of other things, so make sure we queue a full refresh()\n\t}\n\n\n\tstatic register(core) {\n\t\tif (!_coreInitted) {\n\t\t\tgsap = core || _getGSAP();\n\t\t\t_windowExists() && window.document && ScrollTrigger.enable();\n\t\t\t_coreInitted = _enabled;\n\t\t}\n\t\treturn _coreInitted;\n\t}\n\n\tstatic defaults(config) {\n\t\tif (config) {\n\t\t\tfor (let p in config) {\n\t\t\t\t_defaults[p] = config[p];\n\t\t\t}\n\t\t}\n\t\treturn _defaults;\n\t}\n\n\tstatic disable(reset, kill) {\n\t\t_enabled = 0;\n\t\t_triggers.forEach(trigger => trigger[kill ? \"kill\" : \"disable\"](reset));\n\t\t_removeListener(_win, \"wheel\", _onScroll);\n\t\t_removeListener(_doc, \"scroll\", _onScroll);\n\t\tclearInterval(_syncInterval);\n\t\t_removeListener(_doc, \"touchcancel\", _passThrough);\n\t\t_removeListener(_body, \"touchstart\", _passThrough);\n\t\t_multiListener(_removeListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\t\t_multiListener(_removeListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\t\t_resizeDelay.kill();\n\t\t_iterateAutoRefresh(_removeListener);\n\t\tfor (let i = 0; i < _scrollers.length; i+=3) {\n\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+1]);\n\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+2]);\n\t\t}\n\t}\n\n\tstatic enable() {\n\t\t_win = window;\n\t\t_doc = document;\n\t\t_docEl = _doc.documentElement;\n\t\t_body = _doc.body;\n\t\tif (gsap) {\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\t_clamp = gsap.utils.clamp;\n\t\t\t_context = gsap.core.context || _passThrough;\n\t\t\t_suppressOverwrites = gsap.core.suppressOverwrites || _passThrough;\n\t\t\t_scrollRestoration = _win.history.scrollRestoration || \"auto\";\n\t\t\t_lastScroll = _win.pageYOffset || 0;\n\t\t\tgsap.core.globals(\"ScrollTrigger\", ScrollTrigger); // must register the global manually because in Internet Explorer, functions (classes) don't have a \"name\" property.\n\t\t\tif (_body) {\n\t\t\t\t_enabled = 1;\n\t\t\t\t_div100vh = document.createElement(\"div\"); // to solve mobile browser address bar show/hide resizing, we shouldn't rely on window.innerHeight. Instead, use a <div> with its height set to 100vh and measure that since that's what the scrolling is based on anyway and it's not affected by address bar showing/hiding.\n\t\t\t\t_div100vh.style.height = \"100vh\";\n\t\t\t\t_div100vh.style.position = \"absolute\";\n\t\t\t\t_refresh100vh();\n\t\t\t\t_rafBugFix();\n\t\t\t\tObserver.register(gsap);\n\t\t\t\t// isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\t\t\t\tScrollTrigger.isTouch = Observer.isTouch;\n\t\t\t\t_fixIOSBug = Observer.isTouch && /(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent); // since 2017, iOS has had a bug that causes event.clientX/Y to be inaccurate when a scroll occurs, thus we must alternate ignoring every other touchmove event to work around it. See https://bugs.webkit.org/show_bug.cgi?id=181954 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503\n\t\t\t\t_ignoreMobileResize = Observer.isTouch === 1;\n\t\t\t\t_addListener(_win, \"wheel\", _onScroll); // mostly for 3rd party smooth scrolling libraries.\n\t\t\t\t_root = [_win, _doc, _docEl, _body];\n\t\t\t\tif (gsap.matchMedia) {\n\t\t\t\t\tScrollTrigger.matchMedia = vars => {\n\t\t\t\t\t\tlet mm = gsap.matchMedia(),\n\t\t\t\t\t\t\tp;\n\t\t\t\t\t\tfor (p in vars) {\n\t\t\t\t\t\t\tmm.add(p, vars[p]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn mm;\n\t\t\t\t\t};\n\t\t\t\t\tgsap.addEventListener(\"matchMediaInit\", () => _revertAll());\n\t\t\t\t\tgsap.addEventListener(\"matchMediaRevert\", () => _revertRecorded());\n\t\t\t\t\tgsap.addEventListener(\"matchMedia\", () => {\n\t\t\t\t\t\t_refreshAll(0, 1);\n\t\t\t\t\t\t_dispatch(\"matchMedia\");\n\t\t\t\t\t});\n\t\t\t\t\tgsap.matchMedia().add(\"(orientation: portrait)\", () => { // when orientation changes, we should take new base measurements for the ignoreMobileResize feature.\n\t\t\t\t\t\t_setBaseDimensions();\n\t\t\t\t\t\treturn _setBaseDimensions;\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tconsole.warn(\"Requires GSAP 3.11.0 or later\");\n\t\t\t\t}\n\t\t\t\t_setBaseDimensions();\n\t\t\t\t_addListener(_doc, \"scroll\", _onScroll); // some browsers (like Chrome), the window stops dispatching scroll events on the window if you scroll really fast, but it's consistent on the document!\n\t\t\t\tlet bodyHasStyle = _body.hasAttribute(\"style\"),\n\t\t\t\t\tbodyStyle = _body.style,\n\t\t\t\t\tborder = bodyStyle.borderTopStyle,\n\t\t\t\t\tAnimationProto = gsap.core.Animation.prototype,\n\t\t\t\t\tbounds, i;\n\t\t\t\tAnimationProto.revert || Object.defineProperty(AnimationProto, \"revert\", { value: function() { return this.time(-0.01, true); }}); // only for backwards compatibility (Animation.revert() was added after 3.10.4)\n\t\t\t\tbodyStyle.borderTopStyle = \"solid\"; // works around an issue where a margin of a child element could throw off the bounds of the _body, making it seem like there's a margin when there actually isn't. The border ensures that the bounds are accurate.\n\t\t\t\tbounds = _getBounds(_body);\n\t\t\t\t_vertical.m = Math.round(bounds.top + _vertical.sc()) || 0; // accommodate the offset of the <body> caused by margins and/or padding\n\t\t\t\t_horizontal.m = Math.round(bounds.left + _horizontal.sc()) || 0;\n\t\t\t\tborder ? (bodyStyle.borderTopStyle = border) : bodyStyle.removeProperty(\"border-top-style\");\n\t\t\t\tif (!bodyHasStyle) { // SSR frameworks like Next.js complain if this attribute gets added.\n\t\t\t\t\t_body.setAttribute(\"style\", \"\"); // it's not enough to just removeAttribute() - we must first set it to empty, otherwise Next.js complains.\n\t\t\t\t\t_body.removeAttribute(\"style\");\n\t\t\t\t}\n\t\t\t\t// TODO: (?) maybe move to leveraging the velocity mechanism in Observer and skip intervals.\n\t\t\t\t_syncInterval = setInterval(_sync, 250);\n\t\t\t\tgsap.delayedCall(0.5, () => _startup = 0);\n\t\t\t\t_addListener(_doc, \"touchcancel\", _passThrough); // some older Android devices intermittently stop dispatching \"touchmove\" events if we don't listen for \"touchcancel\" on the document.\n\t\t\t\t_addListener(_body, \"touchstart\", _passThrough); //works around Safari bug: https://gsap.com/forums/topic/21450-draggable-in-iframe-on-mobile-is-buggy/\n\t\t\t\t_multiListener(_addListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\t\t\t\t_multiListener(_addListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\t\t\t\t_transformProp = gsap.utils.checkPrefix(\"transform\");\n\t\t\t\t_stateProps.push(_transformProp);\n\t\t\t\t_coreInitted = _getTime();\n\t\t\t\t_resizeDelay = gsap.delayedCall(0.2, _refreshAll).pause();\n\t\t\t\t_autoRefresh = [_doc, \"visibilitychange\", () => {\n\t\t\t\t\tlet w = _win.innerWidth,\n\t\t\t\t\t\th = _win.innerHeight;\n\t\t\t\t\tif (_doc.hidden) {\n\t\t\t\t\t\t_prevWidth = w;\n\t\t\t\t\t\t_prevHeight = h;\n\t\t\t\t\t} else if (_prevWidth !== w || _prevHeight !== h) {\n\t\t\t\t\t\t_onResize();\n\t\t\t\t\t}\n\t\t\t\t}, _doc, \"DOMContentLoaded\", _refreshAll, _win, \"load\", _refreshAll, _win, \"resize\", _onResize];\n\t\t\t\t_iterateAutoRefresh(_addListener);\n\t\t\t\t_triggers.forEach(trigger => trigger.enable(0, 1));\n\t\t\t\tfor (i = 0; i < _scrollers.length; i+=3) {\n\t\t\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+1]);\n\t\t\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+2]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tstatic config(vars) {\n\t\t(\"limitCallbacks\" in vars) && (_limitCallbacks = !!vars.limitCallbacks);\n\t\tlet ms = vars.syncInterval;\n\t\tms && clearInterval(_syncInterval) || ((_syncInterval = ms) && setInterval(_sync, ms));\n\t\t(\"ignoreMobileResize\" in vars) && (_ignoreMobileResize = ScrollTrigger.isTouch === 1 && vars.ignoreMobileResize);\n\t\tif (\"autoRefreshEvents\" in vars) {\n\t\t\t_iterateAutoRefresh(_removeListener) || _iterateAutoRefresh(_addListener, vars.autoRefreshEvents || \"none\");\n\t\t\t_ignoreResize = (vars.autoRefreshEvents + \"\").indexOf(\"resize\") === -1;\n\t\t}\n\t}\n\n\tstatic scrollerProxy(target, vars) {\n\t\tlet t = _getTarget(target),\n\t\t\ti = _scrollers.indexOf(t),\n\t\t\tisViewport = _isViewport(t);\n\t\tif (~i) {\n\t\t\t_scrollers.splice(i, isViewport ? 6 : 2);\n\t\t}\n\t\tif (vars) {\n\t\t\tisViewport ? _proxies.unshift(_win, vars, _body, vars, _docEl, vars) : _proxies.unshift(t, vars);\n\t\t}\n\t}\n\n\tstatic clearMatchMedia(query) {\n\t\t_triggers.forEach(t => t._ctx && t._ctx.query === query && t._ctx.kill(true, true));\n\t}\n\n\tstatic isInViewport(element, ratio, horizontal) {\n\t\tlet bounds = (_isString(element) ? _getTarget(element) : element).getBoundingClientRect(),\n\t\t\toffset = bounds[horizontal ? _width : _height] * ratio || 0;\n\t\treturn horizontal ? bounds.right - offset > 0 && bounds.left + offset < _win.innerWidth : bounds.bottom - offset > 0 && bounds.top + offset < _win.innerHeight;\n\t}\n\n\tstatic positionInViewport(element, referencePoint, horizontal) {\n\t\t_isString(element) && (element = _getTarget(element));\n\t\tlet bounds = element.getBoundingClientRect(),\n\t\t\tsize = bounds[horizontal ? _width : _height],\n\t\t\toffset = referencePoint == null ? size / 2 : ((referencePoint in _keywords) ? _keywords[referencePoint] * size : ~referencePoint.indexOf(\"%\") ? parseFloat(referencePoint) * size / 100 : parseFloat(referencePoint) || 0);\n\t\treturn horizontal ? (bounds.left + offset) / _win.innerWidth : (bounds.top + offset) / _win.innerHeight;\n\t}\n\n\tstatic killAll(allowListeners) {\n\t\t_triggers.slice(0).forEach(t => t.vars.id !== \"ScrollSmoother\" && t.kill());\n\t\tif (allowListeners !== true) {\n\t\t\tlet listeners = _listeners.killAll || [];\n\t\t\t_listeners = {};\n\t\t\tlisteners.forEach(f => f());\n\t\t}\n\t}\n\n}\n\nScrollTrigger.version = \"3.13.0\";\nScrollTrigger.saveStyles = targets => targets ? _toArray(targets).forEach(target => { // saved styles are recorded in a consecutive alternating Array, like [element, cssText, transform attribute, cache, matchMedia, ...]\n\tif (target && target.style) {\n\t\tlet i = _savedStyles.indexOf(target);\n\t\ti >= 0 && _savedStyles.splice(i, 5);\n\t\t_savedStyles.push(target, target.style.cssText, target.getBBox && target.getAttribute(\"transform\"), gsap.core.getCache(target), _context());\n\t}\n}) : _savedStyles;\nScrollTrigger.revert = (soft, media) => _revertAll(!soft, media);\nScrollTrigger.create = (vars, animation) => new ScrollTrigger(vars, animation);\nScrollTrigger.refresh = safe => safe ? _onResize(true) : (_coreInitted || ScrollTrigger.register()) && _refreshAll(true);\nScrollTrigger.update = force => ++_scrollers.cache && _updateAll(force === true ? 2 : 0);\nScrollTrigger.clearScrollMemory = _clearScrollMemory;\nScrollTrigger.maxScroll = (element, horizontal) => _maxScroll(element, horizontal ? _horizontal : _vertical);\nScrollTrigger.getScrollFunc = (element, horizontal) => _getScrollFunc(_getTarget(element), horizontal ? _horizontal : _vertical);\nScrollTrigger.getById = id => _ids[id];\nScrollTrigger.getAll = () => _triggers.filter(t => t.vars.id !== \"ScrollSmoother\"); // it's common for people to ScrollTrigger.getAll(t => t.kill()) on page routes, for example, and we don't want it to ruin smooth scrolling by killing the main ScrollSmoother one.\nScrollTrigger.isScrolling = () => !!_lastScrollTime;\nScrollTrigger.snapDirectional = _snapDirectional;\nScrollTrigger.addEventListener = (type, callback) => {\n\tlet a = _listeners[type] || (_listeners[type] = []);\n\t~a.indexOf(callback) || a.push(callback);\n};\nScrollTrigger.removeEventListener = (type, callback) => {\n\tlet a = _listeners[type],\n\t\ti = a && a.indexOf(callback);\n\ti >= 0 && a.splice(i, 1);\n};\nScrollTrigger.batch = (targets, vars) => {\n\tlet result = [],\n\t\tvarsCopy = {},\n\t\tinterval = vars.interval || 0.016,\n\t\tbatchMax = vars.batchMax || 1e9,\n\t\tproxyCallback = (type, callback) => {\n\t\t\tlet elements = [],\n\t\t\t\ttriggers = [],\n\t\t\t\tdelay = gsap.delayedCall(interval, () => {callback(elements, triggers); elements = []; triggers = [];}).pause();\n\t\t\treturn self => {\n\t\t\t\telements.length || delay.restart(true);\n\t\t\t\telements.push(self.trigger);\n\t\t\t\ttriggers.push(self);\n\t\t\t\tbatchMax <= elements.length && delay.progress(1);\n\t\t\t};\n\t\t},\n\t\tp;\n\tfor (p in vars) {\n\t\tvarsCopy[p] = (p.substr(0, 2) === \"on\" && _isFunction(vars[p]) && p !== \"onRefreshInit\") ? proxyCallback(p, vars[p]) : vars[p];\n\t}\n\tif (_isFunction(batchMax)) {\n\t\tbatchMax = batchMax();\n\t\t_addListener(ScrollTrigger, \"refresh\", () => batchMax = vars.batchMax());\n\t}\n\t_toArray(targets).forEach(target => {\n\t\tlet config = {};\n\t\tfor (p in varsCopy) {\n\t\t\tconfig[p] = varsCopy[p];\n\t\t}\n\t\tconfig.trigger = target;\n\t\tresult.push(ScrollTrigger.create(config));\n\t});\n\treturn result;\n}\n\n\n// to reduce file size. clamps the scroll and also returns a duration multiplier so that if the scroll gets chopped shorter, the duration gets curtailed as well (otherwise if you're very close to the top of the page, for example, and swipe up really fast, it'll suddenly slow down and take a long time to reach the top).\nlet _clampScrollAndGetDurationMultiplier = (scrollFunc, current, end, max) => {\n\t\tcurrent > max ? scrollFunc(max) : current < 0 && scrollFunc(0);\n\t\treturn end > max ? (max - current) / (end - current) : end < 0 ? current / (current - end) : 1;\n\t},\n\t_allowNativePanning = (target, direction) => {\n\t\tif (direction === true) {\n\t\t\ttarget.style.removeProperty(\"touch-action\");\n\t\t} else {\n\t\t\ttarget.style.touchAction = direction === true ? \"auto\" : direction ? \"pan-\" + direction + (Observer.isTouch ? \" pinch-zoom\" : \"\") : \"none\"; // note: Firefox doesn't support it pinch-zoom properly, at least in addition to a pan-x or pan-y.\n\t\t}\n\t\ttarget === _docEl && _allowNativePanning(_body, direction);\n\t},\n\t_overflow = {auto: 1, scroll: 1},\n\t_nestedScroll = ({event, target, axis}) => {\n\t\tlet node = (event.changedTouches ? event.changedTouches[0] : event).target,\n\t\t\tcache = node._gsap || gsap.core.getCache(node),\n\t\t\ttime = _getTime(), cs;\n\t\tif (!cache._isScrollT || time - cache._isScrollT > 2000) { // cache for 2 seconds to improve performance.\n\t\t\twhile (node && node !== _body && ((node.scrollHeight <= node.clientHeight && node.scrollWidth <= node.clientWidth) || !(_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]))) node = node.parentNode;\n\t\t\tcache._isScroll = node && node !== target && !_isViewport(node) && (_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]);\n\t\t\tcache._isScrollT = time;\n\t\t}\n\t\tif (cache._isScroll || axis === \"x\") {\n\t\t\tevent.stopPropagation();\n\t\t\tevent._gsapAllow = true;\n\t\t}\n\t},\n\t// capture events on scrollable elements INSIDE the <body> and allow those by calling stopPropagation() when we find a scrollable ancestor\n\t_inputObserver = (target, type, inputs, nested) => Observer.create({\n\t\ttarget: target,\n\t\tcapture: true,\n\t\tdebounce: false,\n\t\tlockAxis: true,\n\t\ttype: type,\n\t\tonWheel: (nested = nested && _nestedScroll),\n\t\tonPress: nested,\n\t\tonDrag: nested,\n\t\tonScroll: nested,\n\t\tonEnable: () => inputs && _addListener(_doc, Observer.eventTypes[0], _captureInputs, false, true),\n\t\tonDisable: () => _removeListener(_doc, Observer.eventTypes[0], _captureInputs, true)\n\t}),\n\t_inputExp = /(input|label|select|textarea)/i,\n\t_inputIsFocused,\n\t_captureInputs = e => {\n\t\tlet isInput = _inputExp.test(e.target.tagName);\n\t\tif (isInput || _inputIsFocused) {\n\t\t\te._gsapAllow = true;\n\t\t\t_inputIsFocused = isInput;\n\t\t}\n\t},\n\t_getScrollNormalizer = vars => {\n\t\t_isObject(vars) || (vars = {});\n\t\tvars.preventDefault = vars.isNormalizer = vars.allowClicks = true;\n\t\tvars.type || (vars.type = \"wheel,touch\");\n\t\tvars.debounce = !!vars.debounce;\n\t\tvars.id = vars.id || \"normalizer\";\n\t\tlet {normalizeScrollX, momentum, allowNestedScroll, onRelease} = vars,\n\t\t\tself, maxY,\n\t\t\ttarget = _getTarget(vars.target) || _docEl,\n\t\t\tsmoother = gsap.core.globals().ScrollSmoother,\n\t\t\tsmootherInstance = smoother && smoother.get(),\n\t\t\tcontent = _fixIOSBug && ((vars.content && _getTarget(vars.content)) || (smootherInstance && vars.content !== false && !smootherInstance.smooth() && smootherInstance.content())),\n\t\t\tscrollFuncY = _getScrollFunc(target, _vertical),\n\t\t\tscrollFuncX = _getScrollFunc(target, _horizontal),\n\t\t\tscale = 1,\n\t\t\tinitialScale = (Observer.isTouch && _win.visualViewport ? _win.visualViewport.scale * _win.visualViewport.width : _win.outerWidth) / _win.innerWidth,\n\t\t\twheelRefresh = 0,\n\t\t\tresolveMomentumDuration = _isFunction(momentum) ? () => momentum(self) : () => momentum || 2.8,\n\t\t\tlastRefreshID, skipTouchMove,\n\t\t\tinputObserver = _inputObserver(target, vars.type, true, allowNestedScroll),\n\t\t\tresumeTouchMove = () => skipTouchMove = false,\n\t\t\tscrollClampX = _passThrough,\n\t\t\tscrollClampY = _passThrough,\n\t\t\tupdateClamps = () => {\n\t\t\t\tmaxY = _maxScroll(target, _vertical);\n\t\t\t\tscrollClampY = _clamp(_fixIOSBug ? 1 : 0, maxY);\n\t\t\t\tnormalizeScrollX && (scrollClampX = _clamp(0, _maxScroll(target, _horizontal)));\n\t\t\t\tlastRefreshID = _refreshID;\n\t\t\t},\n\t\t\tremoveContentOffset = () => {\n\t\t\t\tcontent._gsap.y = _round(parseFloat(content._gsap.y) + scrollFuncY.offset) + \"px\";\n\t\t\t\tcontent.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + parseFloat(content._gsap.y) + \", 0, 1)\";\n\t\t\t\tscrollFuncY.offset = scrollFuncY.cacheID = 0;\n\t\t\t},\n\t\t\tignoreDrag = () => {\n\t\t\t\tif (skipTouchMove) {\n\t\t\t\t\trequestAnimationFrame(resumeTouchMove);\n\t\t\t\t\tlet offset = _round(self.deltaY / 2),\n\t\t\t\t\t\tscroll = scrollClampY(scrollFuncY.v - offset);\n\t\t\t\t\tif (content && scroll !== scrollFuncY.v + scrollFuncY.offset) {\n\t\t\t\t\t\tscrollFuncY.offset = scroll - scrollFuncY.v;\n\t\t\t\t\t\tlet y = _round((parseFloat(content && content._gsap.y) || 0) - scrollFuncY.offset);\n\t\t\t\t\t\tcontent.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + y + \", 0, 1)\";\n\t\t\t\t\t\tcontent._gsap.y = y + \"px\";\n\t\t\t\t\t\tscrollFuncY.cacheID = _scrollers.cache;\n\t\t\t\t\t\t_updateAll();\n\t\t\t\t\t}\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\tscrollFuncY.offset && removeContentOffset();\n\t\t\t\tskipTouchMove = true;\n\t\t\t},\n\t\t\ttween, startScrollX, startScrollY, onStopDelayedCall,\n\t\t\tonResize = () => { // if the window resizes, like on an iPhone which Apple FORCES the address bar to show/hide even if we event.preventDefault(), it may be scrolling too far now that the address bar is showing, so we must dynamically adjust the momentum tween.\n\t\t\t\tupdateClamps();\n\t\t\t\tif (tween.isActive() && tween.vars.scrollY > maxY) {\n\t\t\t\t\tscrollFuncY() > maxY ? tween.progress(1) && scrollFuncY(maxY) : tween.resetTo(\"scrollY\", maxY);\n\t\t\t\t}\n\t\t\t};\n\t\tcontent && gsap.set(content, {y: \"+=0\"}); // to ensure there's a cache (element._gsap)\n\t\tvars.ignoreCheck = e => (_fixIOSBug && e.type === \"touchmove\" && ignoreDrag(e)) || (scale > 1.05 && e.type !== \"touchstart\") || self.isGesturing || (e.touches && e.touches.length > 1);\n\t\tvars.onPress = () => {\n\t\t\tskipTouchMove = false;\n\t\t\tlet prevScale = scale;\n\t\t\tscale = _round(((_win.visualViewport && _win.visualViewport.scale) || 1) / initialScale);\n\t\t\ttween.pause();\n\t\t\tprevScale !== scale && _allowNativePanning(target, scale > 1.01 ? true : normalizeScrollX ? false : \"x\");\n\t\t\tstartScrollX = scrollFuncX();\n\t\t\tstartScrollY = scrollFuncY();\n\t\t\tupdateClamps();\n\t\t\tlastRefreshID = _refreshID;\n\t\t}\n\t\tvars.onRelease = vars.onGestureStart = (self, wasDragging) => {\n\t\t\tscrollFuncY.offset && removeContentOffset();\n\t\t\tif (!wasDragging) {\n\t\t\t\tonStopDelayedCall.restart(true);\n\t\t\t} else {\n\t\t\t\t_scrollers.cache++; // make sure we're pulling the non-cached value\n\t\t\t\t// alternate algorithm: durX = Math.min(6, Math.abs(self.velocityX / 800)),\tdur = Math.max(durX, Math.min(6, Math.abs(self.velocityY / 800))); dur = dur * (0.4 + (1 - _power4In(dur / 6)) * 0.6)) * (momentumSpeed || 1)\n\t\t\t\tlet dur = resolveMomentumDuration(),\n\t\t\t\t\tcurrentScroll, endScroll;\n\t\t\t\tif (normalizeScrollX) {\n\t\t\t\t\tcurrentScroll = scrollFuncX();\n\t\t\t\t\tendScroll = currentScroll + (dur * 0.05 * -self.velocityX) / 0.227; // the constant .227 is from power4(0.05). velocity is inverted because scrolling goes in the opposite direction.\n\t\t\t\t\tdur *= _clampScrollAndGetDurationMultiplier(scrollFuncX, currentScroll, endScroll, _maxScroll(target, _horizontal));\n\t\t\t\t\ttween.vars.scrollX = scrollClampX(endScroll);\n\t\t\t\t}\n\t\t\t\tcurrentScroll = scrollFuncY();\n\t\t\t\tendScroll = currentScroll + (dur * 0.05 * -self.velocityY) / 0.227; // the constant .227 is from power4(0.05)\n\t\t\t\tdur *= _clampScrollAndGetDurationMultiplier(scrollFuncY, currentScroll, endScroll, _maxScroll(target, _vertical));\n\t\t\t\ttween.vars.scrollY = scrollClampY(endScroll);\n\t\t\t\ttween.invalidate().duration(dur).play(0.01);\n\t\t\t\tif (_fixIOSBug && tween.vars.scrollY >= maxY || currentScroll >= maxY-1) { // iOS bug: it'll show the address bar but NOT fire the window \"resize\" event until the animation is done but we must protect against overshoot so we leverage an onUpdate to do so.\n\t\t\t\t\tgsap.to({}, {onUpdate: onResize, duration: dur});\n\t\t\t\t}\n\t\t\t}\n\t\t\tonRelease && onRelease(self);\n\t\t};\n\t\tvars.onWheel = () => {\n\t\t\ttween._ts && tween.pause();\n\t\t\tif (_getTime() - wheelRefresh > 1000) { // after 1 second, refresh the clamps otherwise that'll only happen when ScrollTrigger.refresh() is called or for touch-scrolling.\n\t\t\t\tlastRefreshID = 0;\n\t\t\t\twheelRefresh = _getTime();\n\t\t\t}\n\t\t};\n\t\tvars.onChange = (self, dx, dy, xArray, yArray) => {\n\t\t\t_refreshID !== lastRefreshID && updateClamps();\n\t\t\tdx && normalizeScrollX && scrollFuncX(scrollClampX(xArray[2] === dx ? startScrollX + (self.startX - self.x) : scrollFuncX() + dx - xArray[1])); // for more precision, we track pointer/touch movement from the start, otherwise it'll drift.\n\t\t\tif (dy) {\n\t\t\t\tscrollFuncY.offset && removeContentOffset();\n\t\t\t\tlet isTouch = yArray[2] === dy,\n\t\t\t\t\ty = isTouch ? startScrollY + self.startY - self.y : scrollFuncY() + dy - yArray[1],\n\t\t\t\t\tyClamped = scrollClampY(y);\n\t\t\t\tisTouch && y !== yClamped && (startScrollY += yClamped - y);\n\t\t\t\tscrollFuncY(yClamped);\n\t\t\t}\n\t\t\t(dy || dx) && _updateAll();\n\t\t};\n\t\tvars.onEnable = () => {\n\t\t\t_allowNativePanning(target, normalizeScrollX ? false : \"x\");\n\t\t\tScrollTrigger.addEventListener(\"refresh\", onResize);\n\t\t\t_addListener(_win, \"resize\", onResize);\n\t\t\tif (scrollFuncY.smooth) {\n\t\t\t\tscrollFuncY.target.style.scrollBehavior = \"auto\";\n\t\t\t\tscrollFuncY.smooth = scrollFuncX.smooth = false;\n\t\t\t}\n\t\t\tinputObserver.enable();\n\t\t};\n\t\tvars.onDisable = () => {\n\t\t\t_allowNativePanning(target, true);\n\t\t\t_removeListener(_win, \"resize\", onResize);\n\t\t\tScrollTrigger.removeEventListener(\"refresh\", onResize);\n\t\t\tinputObserver.kill();\n\t\t};\n\t\tvars.lockAxis = vars.lockAxis !== false;\n\t\tself = new Observer(vars);\n\t\tself.iOS = _fixIOSBug; // used in the Observer getCachedScroll() function to work around an iOS bug that wreaks havoc with TouchEvent.clientY if we allow scroll to go all the way back to 0.\n\t\t_fixIOSBug && !scrollFuncY() && scrollFuncY(1); // iOS bug causes event.clientY values to freak out (wildly inaccurate) if the scroll position is exactly 0.\n\t\t_fixIOSBug && gsap.ticker.add(_passThrough); // prevent the ticker from sleeping\n\t\tonStopDelayedCall = self._dc;\n\t\ttween = gsap.to(self, {ease: \"power4\", paused: true, inherit: false, scrollX: normalizeScrollX ? \"+=0.1\" : \"+=0\", scrollY: \"+=0.1\", modifiers: {scrollY: _interruptionTracker(scrollFuncY, scrollFuncY(), () => tween.pause())\t}, onUpdate: _updateAll, onComplete: onStopDelayedCall.vars.onComplete}); // we need the modifier to sense if the scroll position is altered outside of the momentum tween (like with a scrollTo tween) so we can pause() it to prevent conflicts.\n\t\treturn self;\n\t};\n\nScrollTrigger.sort = func => {\n\tif (_isFunction(func)) {\n\t\treturn _triggers.sort(func);\n\t}\n\tlet scroll = _win.pageYOffset || 0;\n\tScrollTrigger.getAll().forEach(t => t._sortY = t.trigger ? scroll + t.trigger.getBoundingClientRect().top : t.start + _win.innerHeight);\n\treturn _triggers.sort(func || ((a, b) => (a.vars.refreshPriority || 0) * -1e6 + (a.vars.containerAnimation ? 1e6 : a._sortY) - ((b.vars.containerAnimation ? 1e6 : b._sortY) + (b.vars.refreshPriority || 0) * -1e6))); // anything with a containerAnimation should refresh last.\n}\nScrollTrigger.observe = vars => new Observer(vars);\nScrollTrigger.normalizeScroll = vars => {\n\tif (typeof(vars) === \"undefined\") {\n\t\treturn _normalizer;\n\t}\n\tif (vars === true && _normalizer) {\n\t\treturn _normalizer.enable();\n\t}\n\tif (vars === false) {\n\t\t_normalizer && _normalizer.kill();\n\t\t_normalizer = vars;\n\t\treturn;\n\t}\n\tlet normalizer = vars instanceof Observer ? vars : _getScrollNormalizer(vars);\n\t_normalizer && _normalizer.target === normalizer.target && _normalizer.kill();\n\t_isViewport(normalizer.target) && (_normalizer = normalizer);\n\treturn normalizer;\n};\n\n\nScrollTrigger.core = { // smaller file size way to leverage in ScrollSmoother and Observer\n\t_getVelocityProp,\n\t_inputObserver,\n\t_scrollers,\n\t_proxies,\n\tbridge: {\n\t\t// when normalizeScroll sets the scroll position (ss = setScroll)\n\t\tss: () => {\n\t\t\t_lastScrollTime || _dispatch(\"scrollStart\");\n\t\t\t_lastScrollTime = _getTime();\n\t\t},\n\t\t// a way to get the _refreshing value in Observer\n\t\tref: () => _refreshing\n\t}\n};\n\n_getGSAP() && gsap.registerPlugin(ScrollTrigger);\n\nexport { ScrollTrigger as default };"], "names": ["_getGSAP", "gsap", "window", "registerPlugin", "_getProxyProp", "element", "property", "_proxies", "indexOf", "_isViewport", "el", "_root", "_addListener", "type", "func", "passive", "capture", "addEventListener", "_removeListener", "removeEventListener", "_onScroll", "_normalizer", "isPressed", "_scrollers", "cache", "_scrollCacheFunc", "f", "doNotCache", "cachingFunc", "value", "_startup", "_win", "history", "scrollRestoration", "isNormalizing", "v", "Math", "round", "iOS", "cacheID", "_bridge", "offset", "_getTarget", "t", "self", "_ctx", "selector", "utils", "toArray", "config", "null<PERSON><PERSON><PERSON><PERSON><PERSON>n", "console", "warn", "_getScrollFunc", "s", "sc", "_doc", "scrollingElement", "_docEl", "i", "_vertical", "push", "prev", "arguments", "length", "target", "smooth", "getProperty", "_getVelocityProp", "minTimeRefresh", "useDel<PERSON>", "update", "force", "_getTime", "min", "t1", "v2", "v1", "t2", "dropToZeroTime", "max", "reset", "getVelocity", "latestValue", "tOld", "vOld", "_getEvent", "e", "preventDefault", "_gsapAllow", "changedTouches", "_getAbsoluteMax", "a", "abs", "_setScrollTrigger", "ScrollTrigger", "core", "globals", "_integrate", "data", "bridge", "scrollers", "proxies", "name", "_initCore", "_coreInitted", "document", "body", "documentElement", "_body", "clamp", "_context", "context", "_pointerType", "_isTouch", "Observer", "is<PERSON><PERSON>ch", "matchMedia", "matches", "navigator", "maxTouchPoints", "msMaxTouchPoints", "_eventTypes", "eventTypes", "split", "setTimeout", "_observers", "Date", "now", "_scrollLeft", "_scrollTop", "_horizontal", "p", "p2", "os", "os2", "d", "d2", "scrollTo", "pageXOffset", "op", "pageYOffset", "init", "vars", "tolerance", "dragMinimum", "lineHeight", "debounce", "onStop", "onStopDelay", "ignore", "wheelSpeed", "event", "onDragStart", "onDragEnd", "onDrag", "onPress", "onRelease", "onRight", "onLeft", "onUp", "onDown", "onChangeX", "onChangeY", "onChange", "onToggleX", "onToggleY", "onHover", "onHoverEnd", "onMove", "<PERSON><PERSON><PERSON><PERSON>", "isNormalizer", "onGestureStart", "onGestureEnd", "onWheel", "onEnable", "onDisable", "onClick", "scrollSpeed", "allowClicks", "lockAxis", "onLockAxis", "clickCapture", "onClickTime", "_ignore<PERSON>heck", "isPointerOr<PERSON>ouch", "_isWithin", "list", "contains", "limitToTouch", "pointerType", "dx", "deltaX", "dy", "deltaY", "changedX", "changedY", "prevDeltaX", "prevDeltaY", "moved", "dragged", "locked", "wheeled", "id", "onDelta", "x", "y", "index", "_vx", "_vy", "requestAnimationFrame", "onTouchOrPointerDelta", "axis", "_onDrag", "clientX", "clientY", "isDragging", "startX", "startY", "_onGestureStart", "touches", "isGesturing", "_onGestureEnd", "onScroll", "scrollFuncX", "scrollFuncY", "scrollX", "scrollY", "onStopDelayedCall", "restart", "_onWheel", "multiplier", "deltaMode", "innerHeight", "_onMove", "_onHover", "_onHoverEnd", "_onClick", "parseFloat", "getComputedStyle", "this", "isViewport", "ownerDoc", "ownerDocument", "_onPress", "button", "pause", "_onRelease", "isTrackingDrag", "isNaN", "wasDragging", "isDragNotClick", "eventData", "delayedCall", "defaultPrevented", "click", "createEvent", "syntheticEvent", "initMouseEvent", "screenX", "screenY", "dispatchEvent", "_dc", "onStopFunc", "enable", "isEnabled", "disable", "filter", "o", "kill", "revert", "splice", "version", "create", "register", "getAll", "slice", "getById", "_parseClamp", "_isString", "substr", "_keepClamp", "_pointerDownHandler", "_pointerIsDown", "_pointerU<PERSON><PERSON><PERSON><PERSON>", "_passThrough", "_round", "_windowExists", "_getViewportDimension", "dimensionProperty", "_100vh", "_getBoundsFunc", "_winOffsets", "width", "innerWidth", "height", "_getBounds", "_maxScroll", "_iterateAutoRefresh", "events", "_autoRefresh", "_isFunction", "_isNumber", "_isObject", "_endAnimation", "animation", "reversed", "progress", "_callback", "enabled", "result", "add", "totalTime", "callbackAnimation", "_getComputedStyle", "_setDefaults", "obj", "defaults", "_getSize", "_getLabelRatioArray", "timeline", "labels", "duration", "_snapDirectional", "snapIncrementOrArray", "snap", "Array", "isArray", "sort", "b", "direction", "threshold", "snapped", "_multiListener", "types", "callback", "for<PERSON>ach", "nonPassive", "_wheelListener", "scrollFunc", "wheelHandler", "_offsetToPx", "size", "eqIndex", "relative", "char<PERSON>t", "_keywords", "_createMarker", "container", "matchWidthEl", "containerAnimation", "startColor", "endColor", "fontSize", "indent", "fontWeight", "createElement", "useFixedPosition", "isScroller", "parent", "isStart", "color", "css", "_right", "_bottom", "offsetWidth", "_isStart", "setAttribute", "style", "cssText", "innerText", "children", "insertBefore", "append<PERSON><PERSON><PERSON>", "_offset", "_position<PERSON><PERSON>er", "_sync", "_lastScrollTime", "_rafID", "_updateAll", "clientWidth", "_dispatch", "_setBaseDimensions", "_baseScreenWidth", "_baseScreenHeight", "_onResize", "_refreshing", "_ignoreResize", "fullscreenElement", "webkitFullscreenElement", "_ignoreMobileResize", "_resizeDelay", "_softRefresh", "_refreshAll", "_revertRecorded", "media", "_savedStyles", "query", "getBBox", "uncache", "_revertAll", "trigger", "_i", "_triggers", "_isReverted", "_clearScrollMemory", "_refreshingAll", "rec", "_scrollRestoration", "_refresh100vh", "_div100vh", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON>", "_hideAllMarkers", "hide", "_toArray", "display", "_swapPinIn", "pin", "spacer", "cs", "spacerState", "_gsap", "swappedIn", "_propNamesToCopy", "spacerStyle", "pinStyle", "position", "flexBasis", "overflow", "boxSizing", "_width", "_px", "_height", "_padding", "_margin", "_setState", "parentNode", "_getState", "l", "_stateProps", "state", "_parsePosition", "scrollerSize", "scroll", "marker", "markerScroller", "scrollerBounds", "borderWidth", "scrollerMax", "clampZeroProp", "p1", "time", "seek", "mapRange", "scrollTrigger", "start", "end", "bounds", "localOffset", "globalOffset", "offsets", "left", "top", "removeProperty", "m", "_caScrollDist", "_reparent", "_st<PERSON><PERSON>", "_prefixExp", "test", "getCache", "_interruptionTracker", "getValueFunc", "initialValue", "onInterrupt", "last1", "last2", "current", "_shiftMarker", "set", "_getTweenCreator", "scroller", "getTween", "change1", "change2", "tween", "onComplete", "modifiers", "getScroll", "checkForInterruption", "prop", "inherit", "ratio", "onUpdate", "call", "to", "_clamp", "_time2", "_syncInterval", "_transformProp", "_prevWidth", "_prevHeight", "_sort", "_suppressOverwrites", "_fixIOSBug", "_clampingMax", "_limitCallbacks", "_queueRefreshID", "_primary", "_time1", "_enabled", "_abs", "_Right", "_Left", "_Top", "_Bottom", "_Width", "_Height", "withoutTransforms", "xPercent", "yPercent", "rotation", "rotationX", "rotationY", "scale", "skewX", "skewY", "getBoundingClientRect", "_markerDefaults", "_defaults", "toggleActions", "anticipatePin", "center", "bottom", "right", "flipped", "side", "oppositeSide", "_isFlipped", "_ids", "_listeners", "_emptyArray", "map", "_refreshID", "<PERSON><PERSON><PERSON><PERSON>", "isRefreshing", "refreshInits", "scroll<PERSON>eh<PERSON>or", "refresh", "_subPinOffset", "horizontal", "original", "adjustPinSpacing", "_dir", "endClamp", "_endClamp", "startClamp", "_startClamp", "setPositions", "render", "onRefresh", "_lastScroll", "_direction", "isUpdating", "recordVelocity", "concat", "_capsExp", "replace", "toLowerCase", "tweenTo", "pinCache", "snapFunc", "scroll1", "scroll2", "markerStart", "markerEnd", "markerStartTrigger", "markerEndTrigger", "markerVars", "executingOnRefresh", "change", "pinOriginalState", "pinActiveState", "pinState", "pinGetter", "pinSetter", "pinStart", "pinChange", "spacingStart", "markerStartSetter", "pinMoves", "markerEndSetter", "snap1", "snap2", "scrubTween", "scrubSmooth", "snapDurClamp", "snapDelayedCall", "prevScroll", "prevAnimProgress", "caMarkerSetter", "customRevertReturn", "nodeType", "toggleClass", "onToggle", "scrub", "pinSpacing", "invalidateOnRefresh", "onScrubComplete", "onSnapComplete", "once", "pinReparent", "pinSpacer", "fastScrollEnd", "preventOverlaps", "isToggle", "scrollerCache", "pinType", "callbacks", "onEnter", "onLeave", "onEnterBack", "onLeaveBack", "markers", "onRefreshInit", "getScrollerSize", "_getSizeFunc", "getScrollerOffsets", "_getOffsetsFunc", "lastSnap", "lastRefresh", "prevProgress", "bind", "refreshPriority", "tweenScroll", "scrubDuration", "ease", "totalProgress", "paused", "lazy", "_initted", "isReverted", "immediateRender", "snapTo", "_getClosestLabel", "_getLabelAtDirection", "st", "directional", "delay", "refreshedRecently", "isActive", "endValue", "endScroll", "velocity", "naturalEnd", "inertia", "onStart", "resetTo", "_tTime", "_tDur", "stRevert", "targets", "className", "nativeElement", "spacerIsNative", "classList", "force3D", "quickSetter", "content", "_makePositionable", "oldOnUpdate", "oldParams", "onUpdateParams", "eventCallback", "apply", "previous", "next", "temp", "r", "prevRefreshing", "_swapPinOut", "soft", "pinOffset", "invalidate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isVertical", "override", "curTrigger", "curPin", "oppositeScroll", "initted", "revertedPins", "forcedOverflow", "markerStartOffset", "markerEndOffset", "isFirstRefresh", "otherPinOffset", "parsedEnd", "parsedEndTrigger", "endTrigger", "parsedStart", "<PERSON><PERSON><PERSON><PERSON>", "triggerIndex", "unshift", "_pinPush", "normalize", "_pinOffset", "toUpperCase", "ceil", "_copyState", "omitOffsets", "endAnimation", "labelToScroll", "label", "getTrailing", "reverse", "forceFake", "toggleState", "action", "stateChanged", "toggled", "isAtMax", "isTakingAction", "clipped", "_dp", "_time", "_start", "n", "newStart", "newEnd", "keepClamp", "amount", "allowAnimation", "onKill", "updateFunc", "_queueRefreshAll", "clearInterval", "suppressOverwrites", "_rafBugFix", "userAgent", "mm", "bodyHasStyle", "hasAttribute", "bodyStyle", "border", "borderTopStyle", "AnimationProto", "Animation", "prototype", "Object", "defineProperty", "removeAttribute", "setInterval", "checkPrefix", "w", "h", "hidden", "limitCallbacks", "ms", "syncInterval", "ignoreMobileResize", "autoRefreshEvents", "scrollerProxy", "clearMatchMedia", "isInViewport", "positionInViewport", "referencePoint", "killAll", "allowListeners", "listeners", "saveStyles", "getAttribute", "safe", "clearScrollMemory", "maxScroll", "getScrollFunc", "isScrolling", "snapDirectional", "batch", "proxyCallback", "elements", "triggers", "interval", "batchMax", "varsCopy", "_clampScrollAndGetDurationMultiplier", "_allowNativePanning", "touchAction", "_nestedScroll", "node", "_isScrollT", "scrollHeight", "clientHeight", "scrollWidth", "_overflow", "overflowY", "overflowX", "_isScroll", "stopPropagation", "_inputObserver", "inputs", "nested", "_captureInputs", "_getScrollNormalizer", "resumeTouchMove", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updateClamps", "maxY", "scrollClampY", "normalizeScrollX", "scrollClampX", "lastRefreshID", "removeContentOffset", "transform", "onResize", "startScrollX", "startScrollY", "momentum", "allowNestedScroll", "smoother", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smootherInstance", "get", "initialScale", "visualViewport", "outerWidth", "wheelRefresh", "resolveMomentumDuration", "inputObserver", "ignoreDrag", "prevScale", "currentScroll", "dur", "velocityX", "velocityY", "play", "_ts", "xArray", "yArray", "yClamped", "ticker", "_inputIsFocused", "auto", "_inputExp", "isInput", "tagName", "_sortY", "observe", "normalizeScroll", "normalizer", "ss", "ref"], "mappings": ";;;;;;;;;mYAWY,SAAXA,WAAiBC,IAA4B,oBAAZC,SAA4BD,GAAOC,OAAOD,OAASA,GAAKE,gBAAkBF,GAkB3F,SAAhBG,EAAiBC,EAASC,UAAcC,GAASC,QAAQH,IAAYE,GAASA,GAASC,QAAQH,GAAW,GAAGC,GAC/F,SAAdG,EAAcC,YAASC,EAAMH,QAAQE,GACtB,SAAfE,EAAgBP,EAASQ,EAAMC,EAAMC,EAASC,UAAYX,EAAQY,iBAAiBJ,EAAMC,EAAM,CAACC,SAAqB,IAAZA,EAAmBC,UAAWA,IACrH,SAAlBE,EAAmBb,EAASQ,EAAMC,EAAME,UAAYX,EAAQc,oBAAoBN,EAAMC,IAAQE,GAGlF,SAAZI,WAAmBC,IAAeA,GAAYC,WAAcC,GAAWC,QACpD,SAAnBC,EAAoBC,EAAGC,GACJ,SAAdC,GAAcC,MACbA,GAAmB,IAAVA,EAAa,CACzBC,IAAaC,GAAKC,QAAQC,kBAAoB,cAC1CC,EAAgBb,IAAeA,GAAYC,UAC/CO,EAAQD,GAAYO,EAAIC,KAAKC,MAAMR,KAAWR,IAAeA,GAAYiB,IAAM,EAAI,GACnFZ,EAAEG,GACFD,GAAYW,QAAUhB,GAAWC,MACjCU,GAAiBM,EAAQ,KAAMX,QACrBF,GAAcJ,GAAWC,QAAUI,GAAYW,SAAWC,EAAQ,UAC5EZ,GAAYW,QAAUhB,GAAWC,MACjCI,GAAYO,EAAIT,YAEVE,GAAYO,EAAIP,GAAYa,cAEpCb,GAAYa,OAAS,EACdf,GAAKE,GAIA,SAAbc,EAAcC,EAAGC,UAAWA,GAAQA,EAAKC,MAAQD,EAAKC,KAAKC,UAAa7C,GAAK8C,MAAMC,SAASL,GAAG,KAAqB,iBAAPA,IAAoD,IAAjC1C,GAAKgD,SAASC,eAA2BC,QAAQC,KAAK,qBAAsBT,GAAK,MAWhM,SAAjBU,EAAkBhD,SAAUiD,IAAAA,EAAGC,IAAAA,GAC9B9C,EAAYJ,KAAaA,EAAUmD,GAAKC,kBAAoBC,QACxDC,EAAIpC,GAAWf,QAAQH,GAC1BoC,EAASc,IAAOK,GAAUL,GAAK,EAAI,GAClCI,IAAMA,EAAIpC,GAAWsC,KAAKxD,GAAW,GACvCkB,GAAWoC,EAAIlB,IAAW7B,EAAaP,EAAS,SAAUe,OACtD0C,EAAOvC,GAAWoC,EAAIlB,GACzB3B,EAAOgD,IAASvC,GAAWoC,EAAIlB,GAAUhB,EAAiBrB,EAAcC,EAASiD,IAAI,KAAU7C,EAAYJ,GAAWkD,EAAK9B,EAAiB,SAASI,UAAgBkC,UAAUC,OAAU3D,EAAQiD,GAAKzB,EAASxB,EAAQiD,cACxNxC,EAAKmD,OAAS5D,EACdyD,IAAShD,EAAKoD,OAAyD,WAAhDjE,GAAKkE,YAAY9D,EAAS,mBAC1CS,EAEW,SAAnBsD,EAAoBvC,EAAOwC,EAAgBC,GAOhC,SAATC,GAAU1C,EAAO2C,OACZ7B,EAAI8B,KACJD,GAAkBE,EAAT/B,EAAIgC,GAChBC,EAAKC,EACLA,EAAKhD,EACLiD,EAAKH,EACLA,EAAKhC,GACK2B,EACVO,GAAMhD,EAENgD,EAAKD,GAAM/C,EAAQ+C,IAAOjC,EAAImC,IAAOH,EAAKG,OAhBzCD,EAAKhD,EACR+C,EAAK/C,EACL8C,EAAKF,KACLK,EAAKH,EACLD,EAAML,GAAkB,GACxBU,EAAiB3C,KAAK4C,IAAI,IAAW,EAANN,SAsBzB,CAACH,OAAAA,GAAQU,MARP,SAARA,QAAgBL,EAAKC,EAAKP,EAAW,EAAIO,EAAIC,EAAKH,EAAK,GAQjCO,YAPR,SAAdA,YAAcC,OACTC,EAAON,EACVO,EAAOT,EACPjC,EAAI8B,YACJU,GAA+B,IAAhBA,GAAsBA,IAAgBN,GAAMN,GAAOY,GAC3DR,IAAOG,GAAeC,EAATpC,EAAImC,EAAuB,GAAKD,GAAMP,EAAWe,GAAQA,MAAWf,EAAW3B,EAAIgC,GAAMS,GAAQ,MAI7G,SAAZE,EAAaC,EAAGC,UACfA,IAAmBD,EAAEE,YAAcF,EAAEC,iBAC9BD,EAAEG,eAAiBH,EAAEG,eAAe,GAAKH,EAE/B,SAAlBI,EAAkBC,OACbZ,EAAM5C,KAAK4C,UAAL5C,KAAYwD,GACrBlB,EAAMtC,KAAKsC,UAALtC,KAAYwD,UACZxD,KAAKyD,IAAIb,IAAQ5C,KAAKyD,IAAInB,GAAOM,EAAMN,EAE3B,SAApBoB,KACCC,GAAgB9F,GAAK+F,KAAKC,UAAUF,gBACnBA,GAAcC,MAtGnB,SAAbE,iBACKF,EAAOD,GAAcC,KACxBG,EAAOH,EAAKI,QAAU,GACtBC,EAAYL,EAAKzE,WACjB+E,EAAUN,EAAKzF,SAChB8F,EAAUxC,WAAVwC,EAAkB9E,IAClB+E,EAAQzC,WAARyC,EAAgB/F,IAChBgB,GAAa8E,EACb9F,GAAW+F,EACX9D,EAAU,iBAAC+D,EAAM1E,UAAUsE,EAAKI,GAAM1E,IA6FCqE,GAE5B,SAAZM,EAAYR,UACX/F,GAAO+F,GAAQhG,KACVyG,IAAgBxG,IAA6B,oBAAdyG,UAA6BA,SAASC,OACzE5E,GAAO7B,OAEPwD,IADAF,GAAOkD,UACOE,gBACdC,GAAQrD,GAAKmD,KACbhG,EAAQ,CAACoB,GAAMyB,GAAME,GAAQmD,IACpB5G,GAAK8C,MAAM+D,MACpBC,GAAW9G,GAAK+F,KAAKgB,SAAW,aAChCC,GAAe,mBAAoBJ,GAAQ,UAAY,QAEvDK,GAAWC,EAASC,QAAUrF,GAAKsF,YAActF,GAAKsF,WAAW,oCAAoCC,QAAU,EAAK,iBAAkBvF,IAAmC,EAA3BwF,UAAUC,gBAAmD,EAA7BD,UAAUE,iBAAwB,EAAI,EACpNC,GAAcP,EAASQ,YAAc,iBAAkBjE,GAAS,4CAAgD,kBAAmBA,GAAkD,kDAAxC,uCAA2FkE,MAAM,KAC9OC,WAAW,kBAAM/F,EAAW,GAAG,KAC/BgE,IACAW,GAAe,GAETA,GAlIT,IAAIxG,GAAMwG,GAAsB1E,GAAMyB,GAAME,GAAQmD,GAAOK,GAAUD,GAAclB,GAAepF,EAAOU,GAAaqG,GAAaX,GAElIjF,EAAW,EACXgG,GAAa,GACbvG,GAAa,GACbhB,GAAW,GACXkE,GAAWsD,KAAKC,IAChBxF,EAAU,iBAAC+D,EAAM1E,UAAUA,GAgB3BoG,EAAc,aACdC,EAAa,YAoBbC,GAAc,CAAC7E,EAAG2E,EAAaG,EAAG,OAAQC,GAAI,OAAQC,GAAI,QAASC,IAAK,QAASC,EAAG,QAASC,GAAI,QAAS7C,EAAG,IAAKrC,GAAI9B,EAAiB,SAASI,UAAgBkC,UAAUC,OAASjC,GAAK2G,SAAS7G,EAAO+B,GAAUL,MAAQxB,GAAK4G,aAAenF,GAAKyE,IAAgBvE,GAAOuE,IAAgBpB,GAAMoB,IAAgB,KAChTrE,GAAY,CAACN,EAAG4E,EAAYE,EAAG,MAAOC,GAAI,MAAOC,GAAI,SAAUC,IAAK,SAAUC,EAAG,SAAUC,GAAI,SAAU7C,EAAG,IAAKgD,GAAIT,GAAa5E,GAAI9B,EAAiB,SAASI,UAAgBkC,UAAUC,OAASjC,GAAK2G,SAASP,GAAY5E,KAAM1B,GAASE,GAAK8G,aAAerF,GAAK0E,IAAexE,GAAOwE,IAAerB,GAAMqB,IAAe,KAwFhUC,GAAYS,GAAKhF,GACjBrC,GAAWC,MAAQ,MAEN2F,sBAKZ2B,KAAA,cAAKC,GACJtC,IAAgBD,EAAUvG,KAASkD,QAAQC,KAAK,wCAChD2C,IAAiBD,QACZkD,EAA6bD,EAA7bC,UAAWC,EAAkbF,EAAlbE,YAAapI,EAAqakI,EAAralI,KAAMoD,EAA+Z8E,EAA/Z9E,OAAQiF,EAAuZH,EAAvZG,WAAYC,EAA2YJ,EAA3YI,SAAU3D,EAAiYuD,EAAjYvD,eAAgB4D,EAAiXL,EAAjXK,OAAQC,EAAyWN,EAAzWM,YAAaC,EAA4VP,EAA5VO,OAAQC,EAAoVR,EAApVQ,WAAYC,EAAwUT,EAAxUS,MAAOC,EAAiUV,EAAjUU,YAAaC,EAAoTX,EAApTW,UAAWC,EAAySZ,EAAzSY,OAAQC,EAAiSb,EAAjSa,QAASC,EAAwRd,EAAxRc,UAAWC,EAA6Qf,EAA7Qe,QAASC,EAAoQhB,EAApQgB,OAAQC,EAA4PjB,EAA5PiB,KAAMC,EAAsPlB,EAAtPkB,OAAQC,EAA8OnB,EAA9OmB,UAAWC,EAAmOpB,EAAnOoB,UAAWC,EAAwNrB,EAAxNqB,SAAUC,EAA8MtB,EAA9MsB,UAAWC,EAAmMvB,EAAnMuB,UAAWC,EAAwLxB,EAAxLwB,QAASC,EAA+KzB,EAA/KyB,WAAYC,EAAmK1B,EAAnK0B,OAAQC,EAA2J3B,EAA3J2B,YAAaC,EAA8I5B,EAA9I4B,aAAcC,EAAgI7B,EAAhI6B,eAAgBC,EAAgH9B,EAAhH8B,aAAcC,EAAkG/B,EAAlG+B,QAASC,EAAyFhC,EAAzFgC,SAAUC,EAA+EjC,EAA/EiC,UAAWC,EAAoElC,EAApEkC,QAASC,EAA2DnC,EAA3DmC,YAAalK,EAA8C+H,EAA9C/H,QAASmK,EAAqCpC,EAArCoC,YAAaC,EAAwBrC,EAAxBqC,SAAUC,EAActC,EAAdsC,WA0Bpa,SAAfC,YAAqBC,GAAc9G,KACpB,SAAf+G,GAAgBjG,EAAGkG,UAAsB7I,GAAK4G,MAAQjE,IAAO+D,GA3HnD,SAAZoC,UAAarL,EAASsL,WACjBhI,EAAIgI,EAAK3H,OACNL,QACFgI,EAAKhI,KAAOtD,GAAWsL,EAAKhI,GAAGiI,SAASvL,UACpC,SAGF,EAoHiEqL,CAAUnG,EAAEtB,OAAQqF,IAAamC,GAAoBI,IAAkC,UAAlBtG,EAAEuG,aAA6BpB,GAAeA,EAAYnF,EAAGkG,GAOhM,SAATlH,SACKwH,EAAKnJ,GAAKoJ,OAASrG,EAAgBqG,IACtCC,EAAKrJ,GAAKsJ,OAASvG,EAAgBuG,IACnCC,EAAW/J,KAAKyD,IAAIkG,IAAO/C,EAC3BoD,EAAWhK,KAAKyD,IAAIoG,IAAOjD,EAC5BoB,IAAa+B,GAAYC,IAAahC,EAASxH,GAAMmJ,EAAIE,EAAID,GAAQE,IACjEC,IACHrC,GAAyB,EAAdlH,GAAKoJ,QAAclC,EAAQlH,IACtCmH,GAAUnH,GAAKoJ,OAAS,GAAKjC,EAAOnH,IACpCsH,GAAaA,EAAUtH,IACvByH,GAAezH,GAAKoJ,OAAS,GAAQK,GAAa,GAAOhC,EAAUzH,IACnEyJ,GAAazJ,GAAKoJ,OAClBA,GAAO,GAAKA,GAAO,GAAKA,GAAO,GAAK,GAEjCI,IACHnC,GAAwB,EAAdrH,GAAKsJ,QAAcjC,EAAOrH,IACpCoH,GAAQpH,GAAKsJ,OAAS,GAAKlC,EAAKpH,IAChCuH,GAAaA,EAAUvH,IACvB0H,GAAe1H,GAAKsJ,OAAS,GAAQI,GAAa,GAAOhC,EAAU1H,IACnE0J,GAAa1J,GAAKsJ,OAClBA,GAAO,GAAKA,GAAO,GAAKA,GAAO,GAAK,IAEjCK,IAASC,MACZ/B,GAAUA,EAAO7H,IACb4J,KACH/C,GAA2B,IAAZ+C,IAAiB/C,EAAY7G,IAC5C+G,GAAUA,EAAO/G,IACjB4J,GAAU,GAEXD,IAAQ,GAETE,MAAYA,IAAS,IAAUpB,GAAcA,EAAWzI,IACpD8J,KACH5B,EAAQlI,IACR8J,IAAU,GAEXC,GAAK,EAEI,SAAVC,GAAWC,EAAGC,EAAGC,GAChBf,GAAOe,IAAUF,EACjBX,GAAOa,IAAUD,EACjBlK,GAAKoK,IAAIzI,OAAOsI,GAChBjK,GAAKqK,IAAI1I,OAAOuI,GAChB3D,EAAkBwD,GAAPA,IAAYO,sBAAsB3I,IAAWA,KAEjC,SAAxB4I,GAAyBN,EAAGC,GACvB1B,IAAagC,KAChBxK,GAAKwK,KAAOA,GAAOhL,KAAKyD,IAAIgH,GAAKzK,KAAKyD,IAAIiH,GAAK,IAAM,IACrDL,IAAS,GAEG,MAATW,KACHpB,GAAO,IAAMa,EACbjK,GAAKoK,IAAIzI,OAAOsI,GAAG,IAEP,MAATO,KACHlB,GAAO,IAAMY,EACblK,GAAKqK,IAAI1I,OAAOuI,GAAG,IAEpB3D,EAAkBwD,GAAPA,IAAYO,sBAAsB3I,IAAWA,KAE/C,SAAV8I,GAAU9H,OACLiG,GAAajG,EAAG,QAEhBsH,GADJtH,EAAID,EAAUC,EAAGC,IACP8H,QACTR,EAAIvH,EAAEgI,QACNxB,EAAKc,EAAIjK,GAAKiK,EACdZ,EAAKa,EAAIlK,GAAKkK,EACdU,EAAa5K,GAAK4K,WACnB5K,GAAKiK,EAAIA,EACTjK,GAAKkK,EAAIA,GACLU,IAAgBzB,GAAME,KAAQ7J,KAAKyD,IAAIjD,GAAK6K,OAASZ,IAAM5D,GAAe7G,KAAKyD,IAAIjD,GAAK8K,OAASZ,IAAM7D,MAC1GuD,GAAUgB,EAAa,EAAI,EAC3BA,IAAe5K,GAAK4K,YAAa,GACjCL,GAAsBpB,EAAIE,KAiDV,SAAlB0B,GAAkBpI,UAAKA,EAAEqI,SAA8B,EAAnBrI,EAAEqI,QAAQ5J,SAAepB,GAAKiL,aAAc,IAASjD,EAAerF,EAAG3C,GAAK4K,YAChG,SAAhBM,YAAuBlL,GAAKiL,aAAc,IAAUhD,EAAajI,IACtD,SAAXmL,GAAWxI,OACNiG,GAAajG,QACbsH,EAAImB,KACPlB,EAAImB,KACLrB,IAASC,EAAIqB,IAAWhD,GAAc4B,EAAIqB,IAAWjD,EAAa,GAClEgD,GAAUrB,EACVsB,GAAUrB,EACV1D,GAAUgF,GAAkBC,SAAQ,IAE1B,SAAXC,GAAW/I,OACNiG,GAAajG,IACjBA,EAAID,EAAUC,EAAGC,GACjBsF,IAAY4B,IAAU,OAClB6B,GAA8B,IAAhBhJ,EAAEiJ,UAAkBtF,EAA6B,IAAhB3D,EAAEiJ,UAAkBzM,GAAK0M,YAAc,GAAKlF,EAC/FqD,GAAQrH,EAAEyG,OAASuC,EAAYhJ,EAAE2G,OAASqC,EAAY,GACtDnF,IAAWuB,GAAgByD,GAAkBC,SAAQ,IAE5C,SAAVK,GAAUnJ,OACLiG,GAAajG,QACbsH,EAAItH,EAAE+H,QACTR,EAAIvH,EAAEgI,QACNxB,EAAKc,EAAIjK,GAAKiK,EACdZ,EAAKa,EAAIlK,GAAKkK,EACflK,GAAKiK,EAAIA,EACTjK,GAAKkK,EAAIA,EACTP,IAAQ,EACRnD,GAAUgF,GAAkBC,SAAQ,IACnCtC,GAAME,IAAOkB,GAAsBpB,EAAIE,IAE9B,SAAX0C,GAAWpJ,GAAM3C,GAAK4G,MAAQjE,EAAGgF,EAAQ3H,IAC3B,SAAdgM,GAAcrJ,GAAM3C,GAAK4G,MAAQjE,EAAGiF,EAAW5H,IACpC,SAAXiM,GAAWtJ,UAAKiG,GAAajG,IAAOD,EAAUC,EAAGC,IAAmByF,EAAQrI,SA5LxEqB,OAASA,EAASvB,EAAWuB,IAAWP,QACxCqF,KAAOA,EACDO,EAAXA,GAAoBrJ,GAAK8C,MAAMC,QAAQsG,GACvCN,EAAYA,GAAa,KACzBC,EAAcA,GAAe,EAC7BM,EAAaA,GAAc,EAC3B2B,EAAcA,GAAe,EAC7BrK,EAAOA,GAAQ,sBACfsI,GAAwB,IAAbA,EACID,EAAfA,GAA4B4F,WAAW/M,GAAKgN,iBAAiBlI,IAAOqC,aAAe,OAC/EyD,GAAIyB,GAAmB5B,GAASD,GAAOG,GAASD,GAAQW,GAC3DxK,GAAOoM,KACP3C,GAAa,EACbC,GAAa,EACbvL,GAAUgI,EAAKhI,UAAayE,IAAmC,IAAjBuD,EAAKhI,QACnDiN,GAAc3K,EAAeY,EAAQkE,IACrC8F,GAAc5K,EAAeY,EAAQL,IACrCsK,GAAUF,KACVG,GAAUF,KACVpC,IAAgBhL,EAAKL,QAAQ,YAAcK,EAAKL,QAAQ,YAAiC,gBAAnBkH,GAAY,GAClFuH,GAAaxO,EAAYwD,GACzBiL,GAAWjL,EAAOkL,eAAiB3L,GACnCwI,GAAS,CAAC,EAAG,EAAG,GAChBE,GAAS,CAAC,EAAG,EAAG,GAChBX,GAAc,EAqFd6D,GAAWxM,GAAKgH,QAAU,SAAArE,GACrBiG,GAAajG,EAAG,IAAOA,GAAKA,EAAE8J,SAClCzM,GAAKwK,KAAOA,GAAO,KACnBgB,GAAkBkB,QAClB1M,GAAKtB,WAAY,EACjBiE,EAAID,EAAUC,GACd8G,GAAaC,GAAa,EAC1B1J,GAAK6K,OAAS7K,GAAKiK,EAAItH,EAAE+H,QACzB1K,GAAK8K,OAAS9K,GAAKkK,EAAIvH,EAAEgI,QACzB3K,GAAKoK,IAAI/H,QACTrC,GAAKqK,IAAIhI,QACTrE,EAAa+J,EAAe1G,EAASiL,GAAUxH,GAAY,GAAI2F,GAAStM,IAAS,GACjF6B,GAAKoJ,OAASpJ,GAAKsJ,OAAS,EAC5BtC,GAAWA,EAAQhH,MAEpB2M,GAAa3M,GAAKiH,UAAY,SAAAtE,OACzBiG,GAAajG,EAAG,IACpBrE,EAAgByJ,EAAe1G,EAASiL,GAAUxH,GAAY,GAAI2F,IAAS,OACvEmC,GAAkBC,MAAM7M,GAAKkK,EAAIlK,GAAK8K,QACzCgC,EAAc9M,GAAK4K,WACnBmC,EAAiBD,IAAiD,EAAjCtN,KAAKyD,IAAIjD,GAAKiK,EAAIjK,GAAK6K,SAAgD,EAAjCrL,KAAKyD,IAAIjD,GAAKkK,EAAIlK,GAAK8K,SAC9FkC,EAAYtK,EAAUC,IAClBoK,GAAkBH,IACtB5M,GAAKoK,IAAI/H,QACTrC,GAAKqK,IAAIhI,QAELO,GAAkB2F,GACrBlL,GAAK4P,YAAY,IAAM,cACS,IAA3BpL,KAAa8G,KAAsBhG,EAAEuK,oBACpCvK,EAAEtB,OAAO8L,MACZxK,EAAEtB,OAAO8L,aACH,GAAIb,GAASc,YAAa,KAC5BC,EAAiBf,GAASc,YAAY,eAC1CC,EAAeC,eAAe,SAAS,GAAM,EAAMnO,GAAM,EAAG6N,EAAUO,QAASP,EAAUQ,QAASR,EAAUtC,QAASsC,EAAUrC,SAAS,GAAO,GAAO,GAAO,EAAO,EAAG,MACvKhI,EAAEtB,OAAOoM,cAAcJ,OAM5BrN,GAAK4K,WAAa5K,GAAKiL,YAAcjL,GAAKtB,WAAY,EACtD8H,GAAUsG,IAAgB/E,GAAgByD,GAAkBC,SAAQ,GACpE7B,IAAWjI,KACXmF,GAAagG,GAAehG,EAAU9G,IACtCiH,GAAaA,EAAUjH,GAAM+M,KAqC/BvB,GAAoBxL,GAAK0N,IAAMrQ,GAAK4P,YAAYxG,GAAe,IAnKjD,SAAbkH,aACC3N,GAAKoK,IAAI/H,QACTrC,GAAKqK,IAAIhI,QACTmJ,GAAkBkB,QAClBlG,GAAUA,EAAOxG,MA+J8D0M,QAEjF1M,GAAKoJ,OAASpJ,GAAKsJ,OAAS,EAC5BtJ,GAAKoK,IAAM5I,EAAiB,EAAG,IAAI,GACnCxB,GAAKqK,IAAM7I,EAAiB,EAAG,IAAI,GACnCxB,GAAKsL,QAAUF,GACfpL,GAAKuL,QAAUF,GACfrL,GAAK4K,WAAa5K,GAAKiL,YAAcjL,GAAKtB,WAAY,EACtDyF,GAASiI,MACTpM,GAAK4N,OAAS,SAAAjL,UACR3C,GAAK6N,YACT7P,EAAaqO,GAAaC,GAAWjL,EAAQ,SAAU7C,GAC7B,GAA1BP,EAAKL,QAAQ,WAAkBI,EAAaqO,GAAaC,GAAWjL,EAAQ,SAAU8J,GAAUhN,GAASC,GAChF,GAAzBH,EAAKL,QAAQ,UAAiBI,EAAaqD,EAAQ,QAASqK,GAAUvN,GAASC,IACjD,GAAzBH,EAAKL,QAAQ,UAAiB0G,IAAwC,GAA3BrG,EAAKL,QAAQ,cAC5DI,EAAaqD,EAAQyD,GAAY,GAAI0H,GAAUrO,GAASC,GACxDJ,EAAasO,GAAUxH,GAAY,GAAI6H,IACvC3O,EAAasO,GAAUxH,GAAY,GAAI6H,IACvCpE,GAAevK,EAAaqD,EAAQ,QAASqH,IAAc,GAAM,GACjEL,GAAWrK,EAAaqD,EAAQ,QAAS4K,IACzCjE,GAAkBhK,EAAasO,GAAU,eAAgBvB,IACzD9C,GAAgBjK,EAAasO,GAAU,aAAcpB,IACrDvD,GAAW3J,EAAaqD,EAAQgD,GAAe,QAAS0H,IACxDnE,GAAc5J,EAAaqD,EAAQgD,GAAe,QAAS2H,IAC3DnE,GAAU7J,EAAaqD,EAAQgD,GAAe,OAAQyH,KAEvD9L,GAAK6N,WAAY,EACjB7N,GAAK4K,WAAa5K,GAAKiL,YAAcjL,GAAKtB,UAAYiL,GAAQC,IAAU,EACxE5J,GAAKoK,IAAI/H,QACTrC,GAAKqK,IAAIhI,QACTiJ,GAAUF,KACVG,GAAUF,KACV1I,GAAKA,EAAE1E,MAAQuO,GAAS7J,GACxBwF,GAAYA,EAASnI,KAEfA,IAERA,GAAK8N,QAAU,WACV9N,GAAK6N,YAER3I,GAAW6I,OAAO,SAAAC,UAAKA,IAAMhO,IAAQnC,EAAYmQ,EAAE3M,UAASD,QAAU9C,EAAgB+N,GAAaC,GAAWjL,EAAQ,SAAU7C,GAC5HwB,GAAKtB,YACRsB,GAAKoK,IAAI/H,QACTrC,GAAKqK,IAAIhI,QACT/D,EAAgByJ,EAAe1G,EAASiL,GAAUxH,GAAY,GAAI2F,IAAS,IAE5EnM,EAAgB+N,GAAaC,GAAWjL,EAAQ,SAAU8J,GAAU/M,GACpEE,EAAgB+C,EAAQ,QAASqK,GAAUtN,GAC3CE,EAAgB+C,EAAQyD,GAAY,GAAI0H,GAAUpO,GAClDE,EAAgBgO,GAAUxH,GAAY,GAAI6H,IAC1CrO,EAAgBgO,GAAUxH,GAAY,GAAI6H,IAC1CrO,EAAgB+C,EAAQ,QAASqH,IAAc,GAC/CpK,EAAgB+C,EAAQ,QAAS4K,IACjC3N,EAAgBgO,GAAU,eAAgBvB,IAC1CzM,EAAgBgO,GAAU,aAAcpB,IACxC5M,EAAgB+C,EAAQgD,GAAe,QAAS0H,IAChDzN,EAAgB+C,EAAQgD,GAAe,QAAS2H,IAChD1N,EAAgB+C,EAAQgD,GAAe,OAAQyH,IAC/C9L,GAAK6N,UAAY7N,GAAKtB,UAAYsB,GAAK4K,YAAa,EACpDxC,GAAaA,EAAUpI,MAIzBA,GAAKiO,KAAOjO,GAAKkO,OAAS,WACzBlO,GAAK8N,cACD/M,EAAImE,GAAWtH,QAAQoC,IACtB,GAALe,GAAUmE,GAAWiJ,OAAOpN,EAAG,GAC/BtC,KAAgBuB,KAASvB,GAAc,IAGxCyG,GAAWjE,KAAKjB,IAChB+H,GAAgBlK,EAAYwD,KAAY5C,GAAcuB,IAEtDA,GAAK4N,OAAOhH,8JAILwF,KAAKhC,IAAI9H,2DAGT8J,KAAK/B,IAAI/H,8CAtRL6D,QACND,KAAKC,GA0RZ5B,EAAS6J,QAAU,SACnB7J,EAAS8J,OAAS,SAAAlI,UAAQ,IAAI5B,EAAS4B,IACvC5B,EAAS+J,SAAW1K,EACpBW,EAASgK,OAAS,kBAAMrJ,GAAWsJ,SACnCjK,EAASkK,QAAU,SAAA1E,UAAM7E,GAAW6I,OAAO,SAAAC,UAAKA,EAAE7H,KAAK4D,KAAOA,IAAI,IAElE3M,KAAcC,GAAKE,eAAegH,GCjanB,SAAdmK,GAAezP,EAAOhB,EAAM+B,OACvBkE,EAASyK,GAAU1P,KAAkC,WAAvBA,EAAM2P,OAAO,EAAG,KAA2C,EAAxB3P,EAAMrB,QAAQ,eACnFoC,EAAK,IAAM/B,EAAO,SAAWiG,GACdjF,EAAM2P,OAAO,EAAG3P,EAAMmC,OAAS,GAAKnC,EAEvC,SAAb4P,GAAc5P,EAAOiF,UAAUA,GAAWyK,GAAU1P,IAAiC,WAAvBA,EAAM2P,OAAO,EAAG,GAA4C3P,EAAzB,SAAWA,EAAQ,IAE9F,SAAtB6P,YAA4BC,GAAiB,EACzB,SAApBC,YAA0BD,GAAiB,EAC5B,SAAfE,GAAe1P,UAAKA,EACX,SAAT2P,GAASjQ,UAASO,KAAKC,MAAc,IAARR,GAAkB,KAAU,EACzC,SAAhBkQ,WAAyC,oBAAZ7R,OAClB,SAAXF,YAAiBC,IAAS8R,OAAoB9R,GAAOC,OAAOD,OAASA,GAAKE,gBAAkBF,GAC9E,SAAdQ,GAAc8E,YAAQ5E,EAAMH,QAAQ+E,GACZ,SAAxByM,GAAwBC,UAA4C,WAAtBA,EAAiCC,EAASnQ,GAAK,QAAUkQ,KAAuBvO,GAAO,SAAWuO,IAAsBpL,GAAM,SAAWoL,GACtK,SAAjBE,GAAiB9R,UAAWD,EAAcC,EAAS,2BAA6BI,GAAYJ,GAAW,kBAAO+R,GAAYC,MAAQtQ,GAAKuQ,WAAYF,GAAYG,OAASL,EAAeE,IAAgB,kBAAMI,GAAWnS,KAG3M,SAAboS,GAAcpS,SAAUiD,IAAAA,EAAGmF,IAAAA,GAAID,IAAAA,EAAG5C,IAAAA,SAAOxD,KAAK4C,IAAI,GAAI1B,EAAI,SAAWmF,KAAQ7C,EAAIxF,EAAcC,EAASiD,IAAMsC,IAAMuM,GAAe9R,EAAf8R,GAA0B3J,GAAK/H,GAAYJ,IAAYqD,GAAOJ,IAAMuD,GAAMvD,IAAM0O,GAAsBvJ,GAAMpI,EAAQiD,GAAKjD,EAAQ,SAAWoI,IAC1O,SAAtBiK,GAAuB5R,EAAM6R,OACvB,IAAIhP,EAAI,EAAGA,EAAIiP,EAAa5O,OAAQL,GAAK,EAC3CgP,KAAWA,EAAOnS,QAAQoS,EAAajP,EAAE,KAAQ7C,EAAK8R,EAAajP,GAAIiP,EAAajP,EAAE,GAAIiP,EAAajP,EAAE,IAI/F,SAAdkP,GAAchR,SAA2B,mBAAXA,EAClB,SAAZiR,GAAYjR,SAA2B,iBAAXA,EAChB,SAAZkR,GAAYlR,SAA2B,iBAAXA,EACZ,SAAhBmR,GAAiBC,EAAWC,EAAU5D,UAAU2D,GAAaA,EAAUE,SAASD,EAAW,EAAI,IAAM5D,GAAS2D,EAAU3D,QAC5G,SAAZ8D,GAAaxQ,EAAM9B,MACd8B,EAAKyQ,QAAS,KACbC,EAAS1Q,EAAKC,KAAOD,EAAKC,KAAK0Q,IAAI,kBAAMzS,EAAK8B,KAAS9B,EAAK8B,GAChE0Q,GAAUA,EAAOE,YAAc5Q,EAAK6Q,kBAAoBH,IAmBtC,SAApBI,GAAoBrT,UAAW0B,GAAKgN,iBAAiB1O,GAKtC,SAAfsT,GAAgBC,EAAKC,OACf,IAAIzL,KAAKyL,EACZzL,KAAKwL,IAASA,EAAIxL,GAAKyL,EAASzL,WAE3BwL,EAQG,SAAXE,GAAYzT,SAAUoI,IAAAA,UAAQpI,EAAQ,SAAWoI,IAAOpI,EAAQ,SAAWoI,IAAO,EAC5D,SAAtBsL,GAAsBC,OAIpB5L,EAHGxC,EAAI,GACPqO,EAASD,EAASC,OAClBC,EAAWF,EAASE,eAEhB9L,KAAK6L,EACTrO,EAAE/B,KAAKoQ,EAAO7L,GAAK8L,UAEbtO,EAGW,SAAnBuO,GAAmBC,OACdC,EAAOpU,GAAK8C,MAAMsR,KAAKD,GAC1BxO,EAAI0O,MAAMC,QAAQH,IAAyBA,EAAqBhD,MAAM,GAAGoD,KAAK,SAAC5O,EAAG6O,UAAM7O,EAAI6O,WACtF7O,EAAI,SAAC/D,EAAO6S,EAAWC,OACzBhR,cADyBgR,IAAAA,EAAW,OAEnCD,SACGL,EAAKxS,MAEG,EAAZ6S,EAAe,KAClB7S,GAAS8S,EACJhR,EAAI,EAAGA,EAAIiC,EAAE5B,OAAQL,OACrBiC,EAAEjC,IAAM9B,SACJ+D,EAAEjC,UAGJiC,EAAEjC,EAAE,OAEXA,EAAIiC,EAAE5B,OACNnC,GAAS8S,EACFhR,QACFiC,EAAEjC,IAAM9B,SACJ+D,EAAEjC,UAILiC,EAAE,IACN,SAAC/D,EAAO6S,EAAWC,YAAAA,IAAAA,EAAW,UAC7BC,EAAUP,EAAKxS,UACX6S,GAAatS,KAAKyD,IAAI+O,EAAU/S,GAAS8S,GAAeC,EAAU/S,EAAQ,GAAO6S,EAAY,EAAKE,EAAUP,EAAKK,EAAY,EAAI7S,EAAQuS,EAAuBvS,EAAQuS,IAIjK,SAAjBS,GAAkB/T,EAAMT,EAASyU,EAAOC,UAAaD,EAAMlN,MAAM,KAAKoN,QAAQ,SAAAnU,UAAQC,EAAKT,EAASQ,EAAMkU,KAC3F,SAAfnU,GAAgBP,EAASQ,EAAMC,EAAMmU,EAAYjU,UAAYX,EAAQY,iBAAiBJ,EAAMC,EAAM,CAACC,SAAUkU,EAAYjU,UAAWA,IAClH,SAAlBE,GAAmBb,EAASQ,EAAMC,EAAME,UAAYX,EAAQc,oBAAoBN,EAAMC,IAAQE,GAC7E,SAAjBkU,GAAkBpU,EAAMJ,EAAIyU,IAC3BA,EAAaA,GAAcA,EAAWC,gBAErCtU,EAAKJ,EAAI,QAASyU,GAClBrU,EAAKJ,EAAI,YAAayU,IAMV,SAAdE,GAAexT,EAAOyT,MACjB/D,GAAU1P,GAAQ,KACjB0T,EAAU1T,EAAMrB,QAAQ,KAC3BgV,GAAYD,GAAY1T,EAAM4T,OAAOF,EAAQ,GAAK,GAAKzG,WAAWjN,EAAM2P,OAAO+D,EAAU,IAAM,GAC3FA,IACH1T,EAAMrB,QAAQ,KAAO+U,IAAaC,GAAYF,EAAO,KACtDzT,EAAQA,EAAM2P,OAAO,EAAG+D,EAAQ,IAEjC1T,EAAQ2T,GAAa3T,KAAS6T,EAAaA,EAAU7T,GAASyT,GAAQzT,EAAMrB,QAAQ,KAAOsO,WAAWjN,GAASyT,EAAO,IAAMxG,WAAWjN,IAAU,UAE3IA,EAEQ,SAAhB8T,GAAiB9U,EAAM0F,EAAMqP,EAAWlB,IAAiEjS,EAAQoT,EAAcC,OAA3EC,IAAAA,WAAYC,IAAAA,SAAUC,IAAAA,SAAUC,IAAAA,OAAQC,IAAAA,WACvF5Q,EAAI/B,GAAK4S,cAAc,OAC1BC,EAAmB5V,GAAYmV,IAAsD,UAAxCxV,EAAcwV,EAAW,WACtEU,GAA2C,IAA9BzV,EAAKL,QAAQ,YAC1B+V,EAASF,EAAmBxP,GAAQ+O,EACpCY,GAAqC,IAA3B3V,EAAKL,QAAQ,SACvBiW,EAAQD,EAAUT,EAAaC,EAC/BU,EAAM,gBAAkBD,EAAQ,cAAgBR,EAAW,UAAYQ,EAAQ,gBAAkBN,EAAa,8IAC/GO,GAAO,cAAgBJ,GAAcR,IAAuBO,EAAmB,SAAW,cACzFC,IAAcR,GAAuBO,IAAsBK,IAAQhC,IAAc9Q,GAAY+S,EAASC,GAAW,KAAOnU,EAASqM,WAAWoH,IAAW,OACxJL,IAAiBa,GAAO,+CAAiDb,EAAagB,YAAc,OACpGtR,EAAEuR,SAAWN,EACbjR,EAAEwR,aAAa,QAAS,eAAiBlW,GAAQ0F,EAAO,WAAaA,EAAO,KAC5EhB,EAAEyR,MAAMC,QAAUP,EAClBnR,EAAE2R,UAAY3Q,GAAiB,IAATA,EAAa1F,EAAO,IAAM0F,EAAO1F,EACvD0V,EAAOY,SAAS,GAAKZ,EAAOa,aAAa7R,EAAGgR,EAAOY,SAAS,IAAMZ,EAAOc,YAAY9R,GACrFA,EAAE+R,QAAU/R,EAAE,SAAWmP,EAAU9L,GAAGH,IACtC8O,EAAgBhS,EAAG,EAAGmP,EAAW8B,GAC1BjR,EAiBA,SAARiS,YAA6C,GAA/B/S,KAAagT,KAAoCC,EAAXA,GAAoBxK,sBAAsByK,IAClF,SAAZvW,KACMC,GAAgBA,EAAYC,aAAaD,EAAYoM,OAAS5G,GAAM+Q,eACxErW,GAAWC,QACPH,EACQqW,EAAXA,GAAoBxK,sBAAsByK,GAE1CA,IAEDF,IAAmBI,EAAU,eAC7BJ,GAAkBhT,MAGC,SAArBqT,KACCC,EAAmBhW,GAAKuQ,WACxB0F,EAAoBjW,GAAK0M,YAEd,SAAZwJ,GAAazT,GACZjD,GAAWC,SACA,IAAVgD,IAAoB0T,IAAgBC,GAAkB3U,GAAK4U,mBAAsB5U,GAAK6U,yBAA6BC,GAAuBP,IAAqBhW,GAAKuQ,cAAclQ,KAAKyD,IAAI9D,GAAK0M,YAAcuJ,GAAwC,IAAnBjW,GAAK0M,eAAyB8J,EAAalK,SAAQ,GAIzQ,SAAfmK,YAAqBtX,GAAgB6E,GAAe,YAAayS,KAAiBC,IAAY,GAG5E,SAAlBC,GAAkBC,OACZ,IAAIhV,EAAI,EAAGA,EAAIiV,EAAa5U,OAAQL,GAAG,IACtCgV,GAASC,EAAajV,EAAE,IAAMiV,EAAajV,EAAE,GAAGkV,QAAUF,KAC9DC,EAAajV,GAAGqT,MAAMC,QAAU2B,EAAajV,EAAE,GAC/CiV,EAAajV,GAAGmV,SAAWF,EAAajV,GAAGoT,aAAa,YAAa6B,EAAajV,EAAE,IAAM,IAC1FiV,EAAajV,EAAE,GAAGoV,QAAU,GAIlB,SAAbC,GAAcnI,EAAM8H,OACfM,MACCC,GAAK,EAAGA,GAAKC,GAAUnV,OAAQkV,OACnCD,EAAUE,GAAUD,MACHP,GAASM,EAAQpW,OAAS8V,IACtC9H,EACHoI,EAAQpI,KAAK,GAEboI,EAAQnI,QAAO,GAAM,IAIxBsI,GAAc,EACdT,GAASD,GAAgBC,GACzBA,GAASd,EAAU,UAEC,SAArBwB,GAAsBpX,EAAmBuC,GACxCjD,GAAWC,SACVgD,GAAU8U,IAAmB/X,GAAWyT,QAAQ,SAAApB,UAAOf,GAAYe,IAAQA,EAAIrR,YAAcqR,EAAI2F,IAAM,KACxGhI,GAAUtP,KAAuBF,GAAKC,QAAQC,kBAAoBuX,EAAqBvX,GAWxE,SAAhBwX,KACC5S,GAAMwQ,YAAYqC,GAClBxH,GAAW7Q,GAAeqY,EAAUC,cAAiB5X,GAAK0M,YAC1D5H,GAAM+S,YAAYF,GAED,SAAlBG,GAAkBC,UAAQC,GAAS,gGAAgG/E,QAAQ,SAAAtU,UAAMA,EAAGsW,MAAMgD,QAAUF,EAAO,OAAS,UA8GvK,SAAbG,GAAcC,EAAKC,EAAQC,EAAIC,OACzBH,EAAII,MAAMC,UAAW,SAIxBnS,EAHGzE,EAAI6W,EAAiBxW,OACxByW,EAAcN,EAAOnD,MACrB0D,EAAWR,EAAIlD,MAETrT,KAEN8W,EADArS,EAAIoS,EAAiB7W,IACJyW,EAAGhS,GAErBqS,EAAYE,SAA2B,aAAhBP,EAAGO,SAA0B,WAAa,WACjD,WAAfP,EAAGJ,UAA0BS,EAAYT,QAAU,gBACpDU,EAAS9D,GAAW8D,EAAS/D,GAAU,OACvC8D,EAAYG,UAAYR,EAAGQ,WAAa,OACxCH,EAAYI,SAAW,UACvBJ,EAAYK,UAAY,aACxBL,EAAYM,IAAUjH,GAASoG,EAAK/R,IAAe6S,GACnDP,EAAYQ,IAAWnH,GAASoG,EAAKtW,IAAaoX,GAClDP,EAAYS,IAAYR,EAASS,IAAWT,EAAQ,IAASA,EAAQ,KAAU,IAC/EU,GAAUf,GACVK,EAASK,IAAUL,EAAQ,SAAmBN,EAAGW,IACjDL,EAASO,IAAWP,EAAQ,UAAoBN,EAAGa,IACnDP,EAASQ,IAAYd,EAAGc,IACpBhB,EAAImB,aAAelB,IACtBD,EAAImB,WAAWjE,aAAa+C,EAAQD,GACpCC,EAAO9C,YAAY6C,IAEpBA,EAAII,MAAMC,WAAY,GAsBZ,SAAZe,GAAYjb,WACPkb,EAAIC,GAAYxX,OACnBgT,EAAQ3W,EAAQ2W,MAChByE,EAAQ,GACR9X,EAAI,EACEA,EAAI4X,EAAG5X,IACb8X,EAAM5X,KAAK2X,GAAY7X,GAAIqT,EAAMwE,GAAY7X,YAE9C8X,EAAM9Y,EAAItC,EACHob,EAuBS,SAAjBC,GAAkB7Z,EAAOoX,EAAS0C,EAAcjH,EAAWkH,EAAQC,EAAQC,EAAgBlZ,EAAMmZ,EAAgBC,EAAa3F,EAAkB4F,EAAanG,EAAoBoG,GAChLrJ,GAAYhR,KAAWA,EAAQA,EAAMe,IACjC2O,GAAU1P,IAAgC,QAAtBA,EAAM2P,OAAO,EAAE,KACtC3P,EAAQoa,GAAmC,MAApBpa,EAAM4T,OAAO,GAAaJ,GAAY,IAAMxT,EAAM2P,OAAO,GAAImK,GAAgB,QAGpGQ,EAAI9T,EAAIhI,EADL+b,EAAOtG,EAAqBA,EAAmBsG,OAAS,KAE5DtG,GAAsBA,EAAmBuG,KAAK,GAC9C5M,MAAM5N,KAAWA,GAASA,GACrBiR,GAAUjR,GAkBdiU,IAAuBjU,EAAQ5B,GAAK8C,MAAMuZ,SAASxG,EAAmByG,cAAcC,MAAO1G,EAAmByG,cAAcE,IAAK,EAAGR,EAAapa,IACjJia,GAAkBvE,EAAgBuE,EAAgBH,EAAcjH,GAAW,OAnBrD,CACtB7B,GAAYoG,KAAaA,EAAUA,EAAQrW,QAE1C8Z,EAAQC,EAAaC,EAAc5C,EADhC6C,GAAWhb,GAAS,KAAK+F,MAAM,KAEnCvH,EAAUqC,EAAWuW,EAASrW,IAASiE,IACvC6V,EAASlK,GAAWnS,IAAY,MACdqc,EAAOI,MAASJ,EAAOK,MAAgD,SAAvCrJ,GAAkBrT,GAAS2Z,UAC5EA,EAAU3Z,EAAQ2W,MAAMgD,QACxB3Z,EAAQ2W,MAAMgD,QAAU,QACxB0C,EAASlK,GAAWnS,GACpB2Z,EAAW3Z,EAAQ2W,MAAMgD,QAAUA,EAAW3Z,EAAQ2W,MAAMgG,eAAe,YAE5EL,EAActH,GAAYwH,EAAQ,GAAIH,EAAOhI,EAAUlM,IACvDoU,EAAevH,GAAYwH,EAAQ,IAAM,IAAKlB,GAC9C9Z,EAAQ6a,EAAOhI,EAAUtM,GAAK2T,EAAerH,EAAUtM,GAAK4T,EAAcW,EAAcf,EAASgB,EACjGd,GAAkBvE,EAAgBuE,EAAgBc,EAAclI,EAAYiH,EAAeiB,EAAe,IAAOd,EAAehF,UAA2B,GAAf8F,GAC5IjB,GAAgBA,EAAeiB,KAK5BV,IACHtZ,EAAKsZ,GAAiBra,IAAU,KAChCA,EAAQ,IAAMA,EAAQ,IAEnBga,EAAQ,KACPlB,EAAW9Y,EAAQ8Z,EACtBnF,EAAUqF,EAAO/E,SAClBqF,EAAK,SAAWzH,EAAUjM,GAC1B8O,EAAgBsE,EAAQlB,EAAUjG,EAAY8B,GAAsB,GAAXmE,IAAoBnE,IAAYH,EAAmBjU,KAAK4C,IAAI6B,GAAMsV,GAAKzY,GAAOyY,IAAON,EAAOR,WAAWc,KAAQxB,EAAW,GAC/KtE,IACH0F,EAAiBvJ,GAAWsJ,GAC5BzF,IAAqBwF,EAAO7E,MAAMtC,EAAU9L,GAAGR,GAAM2T,EAAerH,EAAU9L,GAAGR,GAAKsM,EAAU9L,GAAGqU,EAAIpB,EAAOvE,QAAW0D,YAGvHlF,GAAsBzV,IACzB8b,EAAK3J,GAAWnS,GAChByV,EAAmBuG,KAAKJ,GACxB5T,EAAKmK,GAAWnS,GAChByV,EAAmBoH,cAAgBf,EAAGzH,EAAUtM,GAAKC,EAAGqM,EAAUtM,GAClEvG,EAAQA,EAASiU,EAAmBoH,cAAiBjB,GAEtDnG,GAAsBA,EAAmBuG,KAAKD,GACvCtG,EAAqBjU,EAAQO,KAAKC,MAAMR,GAGpC,SAAZsb,GAAa9c,EAASkW,EAAQwG,EAAKD,MAC9Bzc,EAAQgb,aAAe9E,EAAQ,KAEjCnO,EAAGgS,EADApD,EAAQ3W,EAAQ2W,SAEhBT,IAAW1P,GAAO,KAGhBuB,KAFL/H,EAAQ+c,QAAUpG,EAAMC,QACxBmD,EAAK1G,GAAkBrT,IAEhB+H,GAAMiV,GAAWC,KAAKlV,KAAMgS,EAAGhS,IAA0B,iBAAb4O,EAAM5O,IAAyB,MAANA,IAC1E4O,EAAM5O,GAAKgS,EAAGhS,IAGhB4O,EAAM+F,IAAMA,EACZ/F,EAAM8F,KAAOA,OAEb9F,EAAMC,QAAU5W,EAAQ+c,QAEzBnd,GAAK+F,KAAKuX,SAASld,GAAS0Y,QAAU,EACtCxC,EAAOc,YAAYhX,IAGE,SAAvBmd,GAAwBC,EAAcC,EAAcC,OAC/CC,EAAQF,EACXG,EAAQD,SACF,SAAA/b,OACFic,EAAU1b,KAAKC,MAAMob,YACrBK,IAAYF,GAASE,IAAYD,GAAqC,EAA5Bzb,KAAKyD,IAAIiY,EAAUF,IAA0C,EAA5Bxb,KAAKyD,IAAIiY,EAAUD,KACjGhc,EAAQic,EACRH,GAAeA,KAEhBE,EAAQD,EACRA,EAAQxb,KAAKC,MAAMR,IAIN,SAAfkc,GAAgBlC,EAAQnH,EAAW7S,OAC9BkH,EAAO,GACXA,EAAK2L,EAAUtM,GAAK,KAAOvG,EAC3B5B,GAAK+d,IAAInC,EAAQ9S,GAUC,SAAnBkV,GAAoBC,EAAUxJ,GAGjB,SAAXyJ,GAAYzV,EAAUK,EAAM2U,EAAcU,EAASC,OAC9CC,EAAQH,GAASG,MACpBC,EAAaxV,EAAKwV,WAClBC,EAAY,GACbd,EAAeA,GAAgBe,QAC3BC,EAAuBlB,GAAqBiB,EAAWf,EAAc,WACxEY,EAAMzN,OACNsN,GAASG,MAAQ,WAElBD,EAAWD,GAAWC,GAAY,EAClCD,EAAUA,GAAY1V,EAAWgV,EACjCY,GAASA,EAAMzN,OACf9H,EAAK4V,GAAQjW,EACbK,EAAK6V,SAAU,GACf7V,EAAKyV,UAAYA,GACPG,GAAQ,kBAAMD,EAAqBhB,EAAeU,EAAUE,EAAMO,MAAQR,EAAUC,EAAMO,MAAQP,EAAMO,QAClH9V,EAAK+V,SAAW,WACfvd,GAAWC,QACX2c,GAASG,OAAS3G,KAEnB5O,EAAKwV,WAAa,WACjBJ,GAASG,MAAQ,EACjBC,GAAcA,EAAWQ,KAAKT,IAE/BA,EAAQH,GAASG,MAAQre,GAAK+e,GAAGd,EAAUnV,OA1BzC0V,EAAYpb,EAAe6a,EAAUxJ,GACxCiK,EAAO,UAAYjK,EAAUrM,UA4B9B6V,EAASS,GAAQF,GACPrJ,aAAe,kBAAM+I,GAASG,OAASH,GAASG,MAAMzN,SAAWsN,GAASG,MAAQ,IAC5F1d,GAAasd,EAAU,QAASO,EAAUrJ,cAC1CrP,GAAcqB,SAAWxG,GAAasd,EAAU,YAAaO,EAAUrJ,cAChE+I,GAjkBT,IAAIle,GAAMwG,EAAc1E,GAAMyB,GAAME,GAAQmD,GAAOlG,EAAO4X,EAAcwB,GAAUkF,GAAQC,GAAQC,EAAejH,GAAavG,GAAgByN,EAAgBlG,GAAImG,EAAYC,EAAa1M,EAAc2M,GAAOC,GAAqBrH,EAAe9W,EAAaiX,EAAqBN,EAAmBD,EAAkB0H,EAAY1Y,EAAUyS,EAAoBE,EAAWxH,EAAQkH,EAAasG,GACpYC,GAiLAjI,EAyDA4B,GAEAsG,GAwEAC,GAnTA/d,GAAW,EACX2C,GAAWsD,KAAKC,IAChB8X,EAASrb,KACTgT,GAAkB,EAClBsI,GAAW,EAyBXxO,GAAY,SAAZA,UAAY1P,SAA2B,iBAAXA,GAW5Bme,GAAO5d,KAAKyD,IAGZ8Q,EAAS,QACTC,EAAU,SACVmE,GAAS,QACTE,GAAU,SACVgF,GAAS,QACTC,GAAQ,OACRC,GAAO,MACPC,GAAU,SACVlF,GAAW,UACXC,GAAU,SACVkF,GAAS,QACTC,EAAU,SACVtF,GAAM,KAYNxI,GAAa,SAAbA,WAAcnS,EAASkgB,OAClBjC,EAAQiC,GAAoE,6BAA/C7M,GAAkBrT,GAAS+e,IAAkDnf,GAAK+e,GAAG3e,EAAS,CAACwM,EAAG,EAAGC,EAAG,EAAG0T,SAAU,EAAGC,SAAU,EAAGC,SAAU,EAAGC,UAAW,EAAGC,UAAW,EAAGC,MAAO,EAAGC,MAAO,EAAGC,MAAO,IAAI5N,SAAS,GACtPuJ,EAASrc,EAAQ2gB,+BAClB1C,GAASA,EAAMnL,SAAS,GAAGtC,OACpB6L,GAwDRuE,GAAkB,CAAClL,WAAY,QAASC,SAAU,MAAOE,OAAQ,EAAGD,SAAU,OAAQE,WAAW,UACjG+K,GAAY,CAACC,cAAe,OAAQC,cAAe,GACnD1L,EAAY,CAACqH,IAAK,EAAGD,KAAM,EAAGuE,OAAQ,GAAKC,OAAQ,EAAGC,MAAO,GAiC7DhK,EAAkB,SAAlBA,gBAAmBsE,EAAQW,EAAO9H,EAAW8M,OACxCzY,EAAO,CAACiR,QAAS,SACpByH,EAAO/M,EAAU8M,EAAU,MAAQ,MACnCE,EAAehN,EAAU8M,EAAU,KAAO,OAC3C3F,EAAO8F,WAAaH,EACpBzY,EAAK2L,EAAU9O,EAAI,WAAa4b,GAAW,IAAM,EACjDzY,EAAK2L,EAAU9O,GAAK4b,EAAU,MAAQ,EACtCzY,EAAK,SAAW0Y,EAAOpB,IAAU,EACjCtX,EAAK,SAAW2Y,EAAerB,IAAU,EACzCtX,EAAK2L,EAAUtM,GAAKoU,EAAQ,KAC5Bvc,GAAK+d,IAAInC,EAAQ9S,IAElBoQ,GAAY,GACZyI,GAAO,GAuBPC,EAAa,GACbC,EAAc,GAEdjK,EAAY,SAAZA,UAAYhX,UAASghB,EAAWhhB,IAASghB,EAAWhhB,GAAMkhB,IAAI,SAAArgB,UAAKA,OAASogB,GAC5ElJ,EAAe,GAgCfoJ,GAAa,EAcbvJ,GAAc,SAAdA,YAAejU,EAAOyd,MACrBve,GAASF,GAAKoD,gBACdC,GAAQrD,GAAKmD,KACbhG,EAAQ,CAACoB,GAAMyB,GAAME,GAAQmD,KACzB4Q,IAAoBjT,GAAU4U,GAIlCK,KACAH,GAAiBvT,GAAcmc,cAAe,EAC9C3gB,GAAWyT,QAAQ,SAAApB,UAAOf,GAAYe,MAAUA,EAAIrR,UAAYqR,EAAI2F,IAAM3F,WACtEuO,EAAetK,EAAU,eAC7B0H,IAASxZ,GAAcyO,OACvByN,GAAcjJ,KACdzX,GAAWyT,QAAQ,SAAApB,GACdf,GAAYe,KACfA,EAAI1P,SAAW0P,EAAI3P,OAAO+S,MAAMoL,eAAiB,QACjDxO,EAAI,MAGNuF,GAAU/H,MAAM,GAAG4D,QAAQ,SAAArS,UAAKA,EAAE0f,YAClCjJ,GAAc,EACdD,GAAUnE,QAAQ,SAACrS,MACdA,EAAE2f,eAAiB3f,EAAEuX,IAAK,KACzByE,EAAOhc,EAAEoG,KAAKwZ,WAAa,cAAgB,eAC9CC,EAAW7f,EAAEuX,IAAIyE,GAClBhc,EAAEmO,QAAO,EAAM,GACfnO,EAAE8f,iBAAiB9f,EAAEuX,IAAIyE,GAAQ6D,GACjC7f,EAAE0f,aAGJ3C,GAAe,EACf7F,IAAgB,GAChBV,GAAUnE,QAAQ,SAAArS,OACbqC,EAAMyN,GAAW9P,EAAEub,SAAUvb,EAAE+f,MAClCC,EAA0B,QAAfhgB,EAAEoG,KAAK0T,KAAkB9Z,EAAEigB,WAAajgB,EAAE8Z,IAAMzX,EAC3D6d,EAAalgB,EAAEmgB,aAAengB,EAAE6Z,OAASxX,GACzC2d,GAAYE,IAAelgB,EAAEogB,aAAaF,EAAa7d,EAAM,EAAIrC,EAAE6Z,MAAOmG,EAAWvgB,KAAK4C,IAAI6d,EAAa7d,EAAMrC,EAAE6Z,MAAQ,EAAGxX,GAAOrC,EAAE8Z,KAAK,KAE9I5C,IAAgB,GAChB6F,GAAe,EACfyC,EAAanN,QAAQ,SAAA1B,UAAUA,GAAUA,EAAO0P,QAAU1P,EAAO0P,QAAQ,KACzEzhB,GAAWyT,QAAQ,SAAApB,GACdf,GAAYe,KACfA,EAAI1P,QAAUgJ,sBAAsB,kBAAM0G,EAAI3P,OAAO+S,MAAMoL,eAAiB,WAC5ExO,EAAI2F,KAAO3F,EAAIA,EAAI2F,QAGrBF,GAAmBG,EAAoB,GACvCjB,EAAajJ,QACb0S,KAEArK,EADA2B,GAAiB,GAEjBH,GAAUnE,QAAQ,SAAArS,UAAKkQ,GAAYlQ,EAAEoG,KAAKka,YAActgB,EAAEoG,KAAKka,UAAUtgB,KACzE2W,GAAiBvT,GAAcmc,cAAe,EAC9CrK,EAAU,gBAlDTjX,GAAamF,GAAe,YAAayS,KAoD3C0K,EAAc,EACdC,GAAa,EAEbxL,EAAa,SAAbA,WAAcnT,MACC,IAAVA,IAAiB8U,KAAmBF,EAAc,CACrDrT,GAAcqd,YAAa,EAC3BvD,IAAYA,GAAStb,OAAO,OACxBgX,EAAIpC,GAAUnV,OACjBoY,EAAO3X,KACP4e,EAAkC,IAAjBjH,EAAO0D,EACxBlE,EAASL,GAAKpC,GAAU,GAAGyC,YAC5BuH,GAA2BvH,EAAdsH,GAAwB,EAAI,EACzC5J,KAAmB4J,EAActH,GAC7ByH,IACC5L,KAAoB9F,IAA2C,IAAzByK,EAAO3E,KAChDA,GAAkB,EAClBI,EAAU,cAEXqH,GAASY,EACTA,EAAS1D,GAEN+G,GAAa,EAAG,KACnBjK,GAAKqC,EACS,EAAPrC,MACNC,GAAUD,KAAOC,GAAUD,IAAI3U,OAAO,EAAG8e,GAE1CF,GAAa,WAERjK,GAAK,EAAGA,GAAKqC,EAAGrC,KACpBC,GAAUD,KAAOC,GAAUD,IAAI3U,OAAO,EAAG8e,GAG3Ctd,GAAcqd,YAAa,EAE5B1L,EAAS,GAEV8C,EAAmB,CA5SX,OACD,MA2S0B5D,EAASD,EAAQwE,GAAUiF,GAASjF,GAAU8E,GAAQ9E,GAAUgF,GAAMhF,GAAU+E,GAAO,UAAW,aAAc,QAAS,SAAU,kBAAmB,gBAAiB,eAAgB,aAAc,WAAY,cAAe,YAAa,YAAa,SAC3R1E,GAAchB,EAAiB8I,OAAO,CAACvI,GAAQE,GAAS,YAAa,MAAQoF,GAAQ,MAAQC,EAAS,WAAYnF,GAASD,GAAUA,GAAWiF,GAAMjF,GAAW+E,GAAQ/E,GAAWkF,GAASlF,GAAWgF,KA6CxMqD,GAAW,WACXnI,GAAY,SAAZA,UAAYK,MACPA,EAAO,KAITrT,EAAGvG,EAHAmV,EAAQyE,EAAM9Y,EAAEqU,MACnBuE,EAAIE,EAAMzX,OACVL,EAAI,OAEJ8X,EAAM9Y,EAAE2X,OAASra,GAAK+F,KAAKuX,SAAS9B,EAAM9Y,IAAIoW,QAAU,EAClDpV,EAAI4X,EAAG5X,GAAI,EACjB9B,EAAQ4Z,EAAM9X,EAAE,GAChByE,EAAIqT,EAAM9X,GACN9B,EACHmV,EAAM5O,GAAKvG,EACDmV,EAAM5O,IAChB4O,EAAMgG,eAAe5U,EAAEob,QAAQD,GAAU,OAAOE,iBA4BpDrR,GAAc,CAAC0K,KAAK,EAAGC,IAAI,GA+D3BM,GAAa,qCAyFDtX,4BAQZ+C,KAAA,cAAKC,EAAMkK,WACLE,SAAWnE,KAAKwN,MAAQ,OACxBzT,MAAQiG,KAAK6B,MAAK,GAAM,GACxBkP,QAwBJ2D,EAASC,EAAUC,EAAUC,EAASC,EAAStH,EAAOC,EAAKsH,EAAaC,EAAWC,EAAoBC,EAAkBC,EAAYC,EACrIC,EAAQC,EAAkBC,EAAgBC,EAAUrK,EAAQ1X,EAAQgiB,EAAWC,EAAWC,EAAUC,EAAWC,EAAcxK,EAAayK,EAAmBC,EAC7JC,EAAiB5K,EAAI6K,EAAOC,EAAOC,GAAYC,EAAaC,EAAcC,GAAiBC,GAAYC,GAAkBC,EAAgBC,EArBrI5G,GADL/V,EAAO4K,GAAcpC,GAAUxI,IAAS+J,GAAU/J,IAASA,EAAK4c,SAAY,CAAC1M,QAASlQ,GAAQA,EAAMmY,KAC/FpC,SAAU8G,EAAsO7c,EAAtO6c,YAAajZ,EAAyN5D,EAAzN4D,GAAIkZ,EAAqN9c,EAArN8c,SAAU5C,GAA2Mla,EAA3Mka,UAAW6C,EAAgM/c,EAAhM+c,MAAO7M,GAAyLlQ,EAAzLkQ,QAASiB,GAAgLnR,EAAhLmR,IAAK6L,GAA2Khd,EAA3Kgd,WAAYC,GAA+Jjd,EAA/Jid,oBAAqB5E,EAA0IrY,EAA1IqY,cAAe6E,EAA2Hld,EAA3Hkd,gBAAiBC,EAA0Gnd,EAA1Gmd,eAAgBC,GAA0Fpd,EAA1Fod,KAAM9R,GAAoFtL,EAApFsL,KAAM+R,GAA8Erd,EAA9Eqd,YAAaC,EAAiEtd,EAAjEsd,UAAWvQ,GAAsD/M,EAAtD+M,mBAAoBwQ,GAAkCvd,EAAlCud,cAAeC,GAAmBxd,EAAnBwd,gBACjO7R,GAAY3L,EAAKwZ,YAAexZ,EAAK+M,qBAA0C,IAApB/M,EAAKwZ,WAAwBpa,GAAcvE,GACtG4iB,IAAYV,GAAmB,IAAVA,EACrB5H,GAAWxb,EAAWqG,EAAKmV,UAAYnc,IACvC0kB,EAAgBxmB,GAAK+F,KAAKuX,SAASW,IACnCjP,GAAaxO,GAAYyd,IACzB7H,GAA0H,WAAtG,YAAatN,EAAOA,EAAK2d,QAAUtmB,EAAc8d,GAAU,YAAejP,IAAc,SAC5G0X,GAAY,CAAC5d,EAAK6d,QAAS7d,EAAK8d,QAAS9d,EAAK+d,YAAa/d,EAAKge,aAChE5F,GAAgBqF,IAAYzd,EAAKoY,cAAcvZ,MAAM,KACrDof,GAAU,YAAaje,EAAOA,EAAKie,QAAU9F,GAAU8F,QACvDhL,GAAc/M,GAAa,EAAIH,WAAW4E,GAAkBwK,IAAU,SAAWxJ,GAAUrM,GAAKgY,MAAY,EAC5Gzd,GAAOoM,KACPiY,GAAgBle,EAAKke,eAAkB,kBAAMle,EAAKke,cAAcrkB,KAChEskB,GA7kBa,SAAfC,aAAgBjJ,EAAUjP,SAAazG,IAAAA,EAAGC,IAAAA,GAAI7C,IAAAA,SAAQA,EAAIxF,EAAc8d,EAAU,0BAA4B,kBAAMtY,IAAI4C,IAAK,kBAAOyG,EAAa+C,GAAsBvJ,GAAMyV,EAAS,SAAWzV,KAAQ,GA6kBrL0e,CAAajJ,GAAUjP,GAAYyF,IACrD0S,GA7kBgB,SAAlBC,gBAAmBhnB,EAAS4O,UAAgBA,IAAe1O,GAASC,QAAQH,GAAW8R,GAAe9R,GAAW,kBAAM+R,IA6kBhGiV,CAAgBnJ,GAAUjP,IAC/CqY,GAAW,EACXC,GAAc,EACdC,GAAe,EACfrS,GAAa9R,EAAe6a,GAAUxJ,OAMvC9R,GAAKkgB,YAAclgB,GAAKggB,WAAY,EACpChgB,GAAK8f,KAAOhO,GACZ0M,GAAiB,GACjBxe,GAAKsb,SAAWA,GAChBtb,GAAKgZ,OAAS9F,GAAqBA,GAAmBsG,KAAKqL,KAAK3R,IAAsBX,GACtF0O,EAAU1O,KACVvS,GAAKmG,KAAOA,EACZkK,EAAYA,GAAalK,EAAKkK,UAC1B,oBAAqBlK,IACxBwW,GAAQ,GACkB,OAA1BxW,EAAK2e,kBAA8B7H,GAAWjd,KAE/C6jB,EAAckB,YAAclB,EAAckB,aAAe,CACxD5K,IAAKkB,GAAiBC,GAAUta,IAChCkZ,KAAMmB,GAAiBC,GAAU/V,KAElCvF,GAAK8gB,QAAUA,EAAU+C,EAAckB,YAAYjT,GAAUtM,GAC7DxF,GAAKglB,cAAgB,SAAA/lB,IACpBujB,EAActS,GAAUjR,IAAUA,GAKjCsjB,GAAaA,GAAWjR,SAASrS,GAAUsjB,GAAallB,GAAK+e,GAAG/L,EAAW,CAAC4U,KAAM,OAAQC,cAAe,MAAOlJ,SAAS,EAAO1K,SAAUkR,EAAa2C,QAAQ,EAAMxJ,WAAY,6BAAM0H,GAAmBA,EAAgBrjB,QAH1NuiB,IAAcA,GAAWhS,SAAS,GAAGtC,OACrCsU,GAAa,IAKXlS,IACHA,EAAUlK,KAAKif,MAAO,EACrB/U,EAAUgV,WAAarlB,GAAKslB,aAAmD,IAAnCjV,EAAUlK,KAAKof,kBAAsD,IAAzBpf,EAAKof,iBAA6BlV,EAAUiB,YAAcjB,EAAU+P,OAAO,GAAG,GAAM,GAC7KpgB,GAAKqQ,UAAYA,EAAU3D,SAC3B2D,EAAUsJ,cAAgB3Z,IACrBglB,cAAc9B,GACnBb,EAAQ,EACDtY,EAAPA,GAAYsG,EAAUlK,KAAK4D,IAGxB0H,KAEEtB,GAAUsB,MAASA,GAAKxQ,OAC5BwQ,GAAO,CAAC+T,OAAQ/T,wBAEIxN,GAAMmQ,OAAU/W,GAAK+d,IAAI/O,GAAa,CAACpI,GAAOnD,IAAUwa,GAAU,CAACkE,eAAgB,SACxG7gB,GAAWyT,QAAQ,SAAApE,UAAKiC,GAAYjC,IAAMA,EAAE3M,UAAYgL,GAAazL,GAAKC,kBAAoBC,GAASwa,MAActN,EAAE1M,QAAS,KAChI0f,EAAW/Q,GAAYwB,GAAK+T,QAAU/T,GAAK+T,OAAyB,WAAhB/T,GAAK+T,OApkBxC,SAAnBC,iBAAmBpV,UAAa,SAAApR,UAAS5B,GAAK8C,MAAMsR,KAAKN,GAAoBd,GAAYpR,IAokBRwmB,CAAiBpV,GAA6B,sBAAhBoB,GAAK+T,OApiB7F,SAAvBE,qBAAuBtU,UAAY,SAACnS,EAAO0mB,UAAOpU,GAAiBJ,GAAoBC,GAArCG,CAAgDtS,EAAO0mB,EAAG7T,YAoiByC4T,CAAqBrV,IAAkC,IAArBoB,GAAKmU,YAAwB,SAAC3mB,EAAO0mB,UAAOpU,GAAiBE,GAAK+T,OAAtBjU,CAA8BtS,EAAO4C,KAAa8iB,GAAc,IAAM,EAAIgB,EAAG7T,YAAazU,GAAK8C,MAAMsR,KAAKA,GAAK+T,QAChV/C,EAAehR,GAAKH,UAAY,CAACxP,IAAK,GAAKM,IAAK,GAChDqgB,EAAetS,GAAUsS,GAAgBpG,GAAOoG,EAAa3gB,IAAK2gB,EAAargB,KAAOia,GAAOoG,EAAcA,GAC3GC,GAAkBrlB,GAAK4P,YAAYwE,GAAKoU,OAAUrD,EAAc,GAAM,GAAK,eACtExJ,EAASzG,KACZuT,EAAoBjkB,KAAa8iB,GAAc,IAC/CjJ,EAAQoF,EAAQpF,WACZoK,GAAqBtmB,KAAKyD,IAAIjD,GAAKsC,eAAiB,KAAQoZ,GAAU3M,IAAkB2V,KAAa1L,EAoC/FhZ,GAAK+lB,UAAYrB,KAAa1L,GACxC0J,GAAgBjX,SAAQ,OArCyF,KAMhHua,EAAUC,EALP1V,GAAYyI,EAASY,GAAS6H,EACjCyD,EAAgB7U,IAAcuT,GAAWvT,EAAU6U,gBAAkB3U,EACrE2V,EAAWJ,EAAoB,GAAMZ,EAAgB5C,IAAUzgB,KAAaya,IAAU,KAAS,EAC/Fd,EAAUne,GAAK8C,MAAM+D,OAAOqM,EAAU,EAAIA,EAAU6M,GAAK8I,EAAW,GAAKA,EAAW,MACpFC,EAAa5V,IAA6B,IAAjBkB,GAAK2U,QAAoB,EAAI5K,GAEpD6K,EAAqC5U,GAArC4U,QAAStL,EAA4BtJ,GAA5BsJ,YAAaY,EAAelK,GAAfkK,cACzBqK,EAAWhF,EAASmF,EAAYnmB,IAChCkQ,GAAU8V,KAAcA,EAAWG,GACnCF,EAAYzmB,KAAK4C,IAAI,EAAG5C,KAAKC,MAAMma,EAAQoM,EAAWvE,IAClDzI,GAAUa,GAAiBD,GAAVZ,GAAmBiN,IAAcjN,EAAQ,IACzD0C,IAAUA,EAAM2J,UAAY3J,EAAMnY,MAAQ6Z,GAAK6I,EAAYjN,WAG1C,IAAjBvH,GAAK2U,UACR5K,EAAUwK,EAAWzV,GAEtBuQ,EAAQmF,EAAW,CAClB3U,SAAUmR,EAAarF,GAAoF,KAA7E5d,KAAK4C,IAAIgb,GAAK+I,EAAajB,GAAgB9H,GAAK4I,EAAWd,IAA0BgB,EAAW,KAAS,IACvIjB,KAAMxT,GAAKwT,MAAQ,SACnB1hB,KAAM6Z,GAAK6I,EAAYjN,GACvB+B,YAAa,8BAAM2H,GAAgBjX,SAAQ,IAASsP,GAAeA,EAAY/a,KAC/E2b,iCACC3b,GAAK2B,SACL+iB,GAAWnS,KACPlC,IAAcuT,KACjBrB,GAAaA,GAAW+D,QAAQ,gBAAiBN,EAAU3V,EAAUkW,OAASlW,EAAUmW,OAASnW,EAAUE,SAASyV,IAErH3D,EAAQC,EAAQjS,IAAcuT,GAAWvT,EAAU6U,gBAAkBllB,GAAKuQ,SAC1E+S,GAAkBA,EAAetjB,IACjC2b,GAAcA,EAAW3b,MAExBgZ,EAAQwC,EAAUiG,EAAQwE,EAAYjN,EAASwC,EAAUiG,GAC5D4E,GAAWA,EAAQrmB,GAAM8gB,EAAQpF,WAKjChP,SAEJ3C,IAAOiV,GAAKjV,GAAM/J,IAKK8iB,GADvBA,GAHAzM,GAAUrW,GAAKqW,QAAUvW,EAAWuW,KAAoB,IAARiB,IAAgBA,MAGhCjB,GAAQqB,OAASrB,GAAQqB,MAAM+O,WACnB3D,EAAmB9iB,IAE/DsX,IAAc,IAARA,GAAejB,GAAUvW,EAAWwX,IAC1C3I,GAAUqU,KAAiBA,EAAc,CAAC0D,QAASrQ,GAASsQ,UAAW3D,IACnE1L,MACa,IAAf6L,IAAwBA,KAAe5K,KAAa4K,MAAcA,IAAc7L,GAAImB,YAAcnB,GAAImB,WAAWrE,OAAuD,SAA9CtD,GAAkBwG,GAAImB,YAAYrB,UAA6BkB,IAC1LtY,GAAKsX,IAAMA,IACXyJ,EAAW1jB,GAAK+F,KAAKuX,SAASrD,KAChBC,OAYbmK,EAAmBX,EAASa,UAXxB6B,KACHA,EAAY3jB,EAAW2jB,MACTA,EAAUV,WAAaU,EAAYA,EAAUvI,SAAWuI,EAAUmD,eAChF7F,EAAS8F,iBAAmBpD,EAC5BA,IAAc1C,EAAStJ,YAAciB,GAAU+K,KAEhD1C,EAASxJ,OAASA,EAASkM,GAAa7iB,GAAK4S,cAAc,OAC3D+D,EAAOuP,UAAUnW,IAAI,cACrB5G,GAAMwN,EAAOuP,UAAUnW,IAAI,cAAgB5G,GAC3CgX,EAASa,SAAWF,EAAmBhJ,GAAUpB,MAIjC,IAAjBnR,EAAK4gB,SAAqB1pB,GAAK+d,IAAI9D,GAAK,CAACyP,SAAS,IAClD/mB,GAAKuX,OAASA,EAASwJ,EAASxJ,OAChCC,EAAK1G,GAAkBwG,IACvB2K,EAAezK,EAAG2L,GAAarR,GAAUnM,KACzCkc,EAAYxkB,GAAKkE,YAAY+V,IAC7BwK,EAAYzkB,GAAK2pB,YAAY1P,GAAKxF,GAAU9O,EAAGoV,IAE/Cf,GAAWC,GAAKC,EAAQC,GACxBoK,EAAWlJ,GAAUpB,KAElB8M,GAAS,CACZ7C,EAAapR,GAAUiU,IAAWrT,GAAaqT,GAAS/F,IAAmBA,GAC3EgD,EAAqBtO,GAAc,iBAAkBhJ,EAAIuR,GAAUxJ,GAAWyP,EAAY,GAC1FD,EAAmBvO,GAAc,eAAgBhJ,EAAIuR,GAAUxJ,GAAWyP,EAAY,EAAGF,GACzFxhB,EAASwhB,EAAmB,SAAWvP,GAAU9L,GAAGH,QAChDohB,EAAUnnB,EAAWtC,EAAc8d,GAAU,YAAcA,IAC/D6F,EAAc/U,KAAK+U,YAAcpO,GAAc,QAAShJ,EAAIkd,EAASnV,GAAWyP,EAAY1hB,EAAQ,EAAGqT,IACvGkO,EAAYhV,KAAKgV,UAAYrO,GAAc,MAAOhJ,EAAIkd,EAASnV,GAAWyP,EAAY1hB,EAAQ,EAAGqT,IACjGA,KAAuB2P,EAAiBxlB,GAAK2pB,YAAY,CAAC7F,EAAaC,GAAYtP,GAAU9O,EAAGoV,KAC1F3E,IAAsB9V,GAASyD,SAAsD,IAA5C5D,EAAc8d,GAAU,kBA7rBrD,SAApB4L,kBAAoBzpB,OACfsa,EAAWjH,GAAkBrT,GAASsa,SAC1Cta,EAAQ2W,MAAM2D,SAAyB,aAAbA,GAAwC,UAAbA,EAAwBA,EAAW,WA4rBtFmP,CAAkB7a,GAAapI,GAAQqX,IACvCje,GAAK+d,IAAI,CAACiG,EAAoBC,GAAmB,CAACyF,SAAS,IAC3D7E,EAAoB7kB,GAAK2pB,YAAY3F,EAAoBvP,GAAU9O,EAAGoV,IACtEgK,EAAkB/kB,GAAK2pB,YAAY1F,EAAkBxP,GAAU9O,EAAGoV,QAIhElF,GAAoB,KACnBiU,EAAcjU,GAAmB/M,KAAK+V,SACzCkL,EAAYlU,GAAmB/M,KAAKkhB,eACrCnU,GAAmBoU,cAAc,WAAY,WAC5CtnB,GAAK2B,OAAO,EAAG,EAAG,GAClBwlB,GAAeA,EAAYI,MAAMrU,GAAoBkU,GAAa,SAIpEpnB,GAAKwnB,SAAW,kBAAMjR,GAAUA,GAAU3Y,QAAQoC,IAAQ,IAC1DA,GAAKynB,KAAO,kBAAMlR,GAAUA,GAAU3Y,QAAQoC,IAAQ,IAEtDA,GAAKkO,OAAS,SAACA,EAAQwZ,OACjBA,SAAe1nB,GAAKiO,MAAK,OAC1B0Z,GAAe,IAAXzZ,IAAqBlO,GAAKyQ,QACjCmX,EAAiBtS,GACdqS,IAAM3nB,GAAKslB,aACVqC,IACHhF,GAAanjB,KAAK4C,IAAImQ,KAAcvS,GAAKgZ,OAAOrC,KAAO,GACvDiO,GAAe5kB,GAAKuQ,SACpBqS,GAAmBvS,GAAaA,EAAUE,YAE3C4Q,GAAe,CAACA,EAAaC,EAAWC,EAAoBC,GAAkBlP,QAAQ,SAAAiI,UAAKA,EAAEjG,MAAMgD,QAAUuQ,EAAI,OAAS,UACtHA,IACHrS,GAActV,IACT2B,OAAOgmB,IAETrQ,IAASkM,IAAgBxjB,GAAK+lB,WAC7B4B,EAncM,SAAdE,YAAevQ,EAAKC,EAAQsB,GAC3BL,GAAUK,OACNja,EAAQ0Y,EAAII,SACZ9Y,EAAMioB,eACTrO,GAAU5Z,EAAM6Y,kBACV,GAAIH,EAAII,MAAMC,UAAW,KAC3BhE,EAAS4D,EAAOkB,WAChB9E,IACHA,EAAOa,aAAa8C,EAAKC,GACzB5D,EAAOqD,YAAYO,IAGrBD,EAAII,MAAMC,WAAY,EAwblBkQ,CAAYvQ,GAAKC,EAAQmK,GAEzBrK,GAAWC,GAAKC,EAAQzG,GAAkBwG,IAAMG,IAGlDkQ,GAAK3nB,GAAK2B,OAAOgmB,GACjBrS,GAAcsS,EACd5nB,GAAKslB,WAAaqC,IAIpB3nB,GAAKyf,QAAU,SAACqI,EAAMlmB,EAAOmW,EAAUgQ,OACjCzS,IAAgBtV,GAAKyQ,SAAa7O,KAGnC0V,IAAOwQ,GAAQjT,GAClB7W,GAAamF,cAAe,YAAayS,UAGzCc,IAAkB2N,IAAiBA,GAAcrkB,IAClDsV,GAActV,GACV8gB,EAAQpF,QAAU3D,IACrB+I,EAAQpF,MAAMzN,OACd6S,EAAQpF,MAAQ,GAEjB6G,IAAcA,GAAW7V,QAErB0W,IAAuB/S,IAC1BA,EAAUnC,OAAO,CAACD,MAAM,IAAQ+Z,aAChC3X,EAAU4X,aAAe5X,EAAU4X,aAAY,GAAM,GAAM,GAAO7V,QAAQ,SAAArS,UAAKA,EAAEoG,KAAKof,iBAAmBxlB,EAAEqgB,OAAO,GAAG,GAAM,MAG5HpgB,GAAKslB,YAActlB,GAAKkO,QAAO,GAAM,GACrClO,GAAK0f,eAAgB,MAapBlI,EAAIsC,EAAQd,EAAQkP,EAAYC,EAAUC,EAAYC,EAAQC,EAAgBC,EAASC,EAAcC,EAAgBC,EAAmBC,EAZrIjW,EAAO4R,KACVnL,EAAiBqL,KACjBpiB,EAAM8Q,GAAqBA,GAAmB5B,WAAazB,GAAWyL,GAAUxJ,IAChF8W,EAAiBnH,GAAU,MAASA,EACpC5hB,EAAS,EACTgpB,EAAiBd,GAAa,EAC9Be,EAAY3Y,GAAU4H,GAAYA,EAAS8B,IAAM1T,EAAK0T,IACtDkP,EAAmB5iB,EAAK6iB,YAAc3S,GACtC4S,EAAc9Y,GAAU4H,GAAYA,EAAS6B,MAASzT,EAAKyT,QAAyB,IAAfzT,EAAKyT,OAAgBvD,GAAeiB,GAAM,MAAQ,SAAnB,GACpG4R,EAAkBlpB,GAAKkpB,gBAAkB/iB,EAAK+iB,iBAAmBppB,EAAWqG,EAAK+iB,gBAAiBlpB,IAClGmpB,EAAgB9S,IAAW7W,KAAK4C,IAAI,EAAGmU,GAAU3Y,QAAQoC,MAAW,EACpEe,EAAIooB,MAED/E,IAAWjU,GAAU4H,KACxB2Q,EAAoBrrB,GAAKkE,YAAY8f,EAAoBvP,GAAUtM,GACnEmjB,EAAkBtrB,GAAKkE,YAAY+f,EAAkBxP,GAAUtM,IAEnD,EAANzE,MACNqnB,EAAa7R,GAAUxV,IACZ8Y,KAAOuO,EAAW3I,QAAQ,EAAG,KAAOnK,GAActV,MAC7DqoB,EAASD,EAAW9Q,MACL+Q,IAAWhS,IAAWgS,IAAW/Q,IAAO+Q,IAAWa,GAAqBd,EAAW9C,cAChFkD,EAAjBA,GAAgC,IACnBY,QAAQhB,GACrBA,EAAWla,QAAO,GAAM,IAErBka,IAAe7R,GAAUxV,KAC5BooB,IACApoB,SAGFkP,GAAYgZ,KAAiBA,EAAcA,EAAYjpB,KACvDipB,EAAcva,GAAYua,EAAa,QAASjpB,IAChD4Z,EAAQd,GAAemQ,EAAa5S,GAAS3D,EAAMZ,GAAWS,KAAc4O,EAAaE,EAAoBrhB,GAAMmZ,EAAgBC,GAAa3F,GAAkBrR,EAAK8Q,GAAoBlT,GAAKkgB,aAAe,iBAAmB5I,IAAO,KAAQ,GACjPrH,GAAY6Y,KAAeA,EAAYA,EAAU9oB,KAC7C2O,GAAUma,KAAeA,EAAUlrB,QAAQ,SACzCkrB,EAAUlrB,QAAQ,KACtBkrB,GAAana,GAAUsa,GAAeA,EAAYjkB,MAAM,KAAK,GAAK,IAAM8jB,GAExEjpB,EAAS4S,GAAYqW,EAAUla,OAAO,GAAI8D,GAC1CoW,EAAYna,GAAUsa,GAAeA,GAAe/V,GAAqB7V,GAAK8C,MAAMuZ,SAAS,EAAGxG,GAAmB5B,WAAY4B,GAAmByG,cAAcC,MAAO1G,GAAmByG,cAAcE,IAAKD,GAASA,GAAS/Z,EAC/NkpB,EAAmB1S,KAGrByS,EAAYpa,GAAYoa,EAAW,MAAO9oB,IAC1C6Z,EAAMra,KAAK4C,IAAIwX,EAAOd,GAAegQ,IAAcC,EAAmB,SAAW3mB,GAAM2mB,EAAkBrW,EAAMZ,GAAWS,KAAe1S,EAAQuhB,EAAWE,EAAkBthB,GAAMmZ,EAAgBC,GAAa3F,GAAkBrR,EAAK8Q,GAAoBlT,GAAKggB,WAAa,gBAAkB,KAEhSngB,EAAS,EACTkB,EAAIooB,EACGpoB,MAENsnB,GADAD,EAAa7R,GAAUxV,IACHuW,MACN8Q,EAAWxO,MAAQwO,EAAWiB,UAAYzP,IAAU1G,IAAuC,EAAjBkV,EAAWvO,MAClGrC,EAAK4Q,EAAWvO,KAAO7Z,GAAKkgB,YAAc1gB,KAAK4C,IAAI,EAAGgmB,EAAWxO,OAASwO,EAAWxO,QAC/EyO,IAAWhS,IAAW+R,EAAWxO,MAAQwO,EAAWiB,SAAWzP,GAAUyO,IAAWa,IAAoBrc,MAAMoc,KACnHppB,GAAU2X,GAAM,EAAI4Q,EAAW7X,WAEhC8X,IAAW/Q,KAAQuR,GAAkBrR,OAGvCoC,GAAS/Z,EACTga,GAAOha,EACPG,GAAKkgB,cAAgBlgB,GAAKkgB,aAAergB,GAErCG,GAAKggB,YAActJ,KACtB1W,GAAKggB,UAAYnG,IAAQ,KACzBA,EAAMra,KAAKsC,IAAI+X,EAAKhK,GAAWyL,GAAUxJ,MAE1C2P,EAAU5H,EAAMD,IAAYA,GAAS,MAAS,KAE1CgP,IACHhE,GAAevnB,GAAK8C,MAAM+D,MAAM,EAAG,EAAG7G,GAAK8C,MAAMmpB,UAAU1P,EAAOC,EAAK8I,MAExE3iB,GAAKqpB,SAAWR,EACZ1H,GAAethB,KAClB2X,EAAK,IACF1F,GAAU9O,GAAK,KAAOnD,EACzBqpB,IAAoB1R,EAAG1F,GAAUtM,GAAK,KAAO+M,MAC7ClV,GAAK+d,IAAI,CAAC+F,EAAaC,GAAY5J,KAGhCF,IAASwF,IAAgB9c,GAAK6Z,KAAOhK,GAAWyL,GAAUxJ,KAuEvD,GAAIuE,IAAW9D,OAAiBW,OACtC4G,EAASzD,GAAQoC,WACVqB,GAAUA,IAAW7V,IACvB6V,EAAOyP,aACV3P,GAASE,EAAOyP,WAChB1P,GAAOC,EAAOyP,YAEfzP,EAASA,EAAOrB,gBA7EjBjB,EAAK1G,GAAkBwG,IACvB4Q,EAAapW,KAAc9Q,GAC3BgY,EAASzG,KACTwP,EAAW7V,WAAW2V,EAAU/P,GAAU9O,IAAM6lB,GAC3CzmB,GAAa,EAANyX,IAEX4O,EAAiB,CAACrU,MADlBqU,GAAkBpc,GAAczL,GAAKC,kBAAoBC,GAAUwa,IAAUlH,MACpCnV,MAAOwpB,EAAe,WAAa3W,GAAU9O,EAAEwmB,gBACpFnd,IAAmF,WAArEyE,GAAkB7M,IAAO,WAAa6N,GAAU9O,EAAEwmB,iBACnEf,EAAerU,MAAM,WAAatC,GAAU9O,EAAEwmB,eAAiB,WAGjEnS,GAAWC,GAAKC,EAAQC,GACxBoK,EAAWlJ,GAAUpB,IAErBwC,EAASlK,GAAW0H,IAAK,GACzBgR,EAAiB7U,IAAoBhT,EAAe6a,GAAU4M,EAAa3iB,GAAcvE,GAApDP,GACjC0iB,KACH1L,EAAc,CAAC0L,GAAarR,GAAUnM,IAAK8b,EAASoH,EAAiBzQ,KACzDrY,EAAIwX,GAChBxW,EAAKoiB,KAAe7K,GAAYpH,GAASoG,GAAKxF,IAAa2P,EAASoH,EAAiB,KAEpFpR,EAAYxW,KAAK6Q,GAAUlM,EAAG7E,EAAIqX,IACP,SAA3Bb,EAAOnD,MAAM4D,YAAyBT,EAAOnD,MAAM4D,UAAYjX,EAAIqX,KAEpEI,GAAUf,GACNyR,GACH3S,GAAUnE,QAAQ,SAAArS,GACbA,EAAEuX,MAAQ4R,IAAyC,IAAtBnpB,EAAEoG,KAAKgd,aACvCpjB,EAAE2f,eAAgB,KAIrBjM,IAAoBlB,GAAWoQ,MAE/B5hB,EAAImQ,GAASoG,GAAKxF,MACc,SAA3ByF,EAAOnD,MAAM4D,YAAyBT,EAAOnD,MAAM4D,UAAYjX,EAAIqX,IAErE3E,MACH0U,EAAW,CACVhO,IAAML,EAAOK,KAAO+N,EAAalP,EAASY,EAAQ0O,GAAmBlQ,GACrE8B,KAAOJ,EAAOI,MAAQgO,EAAaI,EAAiBtP,EAASY,GAAUxB,GACvEF,UAAW,aACXH,SAAU,UAEFI,IAAUgQ,EAAQ,SAAmB3oB,KAAKiqB,KAAK3P,EAAOrK,OAAS2I,GACxE+P,EAAS9P,IAAW8P,EAAQ,UAAoB3oB,KAAKiqB,KAAK3P,EAAOnK,QAAUyI,GAC3E+P,EAAS5P,IAAW4P,EAAS5P,GAAUgF,IAAQ4K,EAAS5P,GAAU8E,IAAU8K,EAAS5P,GAAUiF,IAAW2K,EAAS5P,GAAU+E,IAAS,IACtI6K,EAAS7P,IAAYd,EAAGc,IACxB6P,EAAS7P,GAAWiF,IAAQ/F,EAAGc,GAAWiF,IAC1C4K,EAAS7P,GAAW+E,IAAU7F,EAAGc,GAAW+E,IAC5C8K,EAAS7P,GAAWkF,IAAWhG,EAAGc,GAAWkF,IAC7C2K,EAAS7P,GAAWgF,IAAS9F,EAAGc,GAAWgF,IAC3CqE,EAliBS,SAAb+H,WAAc7Q,EAAOsP,EAAUwB,WAI7BnkB,EAHGkL,EAAS,GACZiI,EAAIE,EAAMzX,OACVL,EAAI4oB,EAAc,EAAI,EAEhB5oB,EAAI4X,EAAG5X,GAAK,EAClByE,EAAIqT,EAAM9X,GACV2P,EAAOzP,KAAKuE,EAAIA,KAAK2iB,EAAYA,EAAS3iB,GAAKqT,EAAM9X,EAAE,WAExD2P,EAAO3Q,EAAI8Y,EAAM9Y,EACV2Q,EAwhBagZ,CAAWhI,EAAkByG,EAAU3E,IACxD9M,IAAkBnE,GAAW,IAE1BlC,GACHkY,EAAUlY,EAAUgV,SACpBzI,GAAoB,GACpBvM,EAAU+P,OAAO/P,EAAUiB,YAAY,GAAM,GAC7C0Q,EAAYH,EAAU/P,GAAU9O,GAAK+e,EAAWN,EAASoH,EACzD1G,EAA0C,EAA/B3iB,KAAKyD,IAAIwe,EAASO,GAC7BvO,IAAoB0O,GAAYR,EAAexT,OAAOwT,EAAevgB,OAAS,EAAG,GACjFiP,EAAU+P,OAAO,GAAG,GAAM,GAC1BmI,GAAWlY,EAAU2X,YAAW,GAChC3X,EAAUsD,QAAUtD,EAAUO,UAAUP,EAAUO,aAClDgM,GAAoB,IAEpBoF,EAAYP,EAEbgH,IAAmBA,EAAexpB,MAASwpB,EAAerU,MAAM,WAAatC,GAAU9O,EAAEwmB,eAAiBf,EAAexpB,MAASwpB,EAAerU,MAAMgG,eAAe,YAActI,GAAU9O,IAW/LwlB,GAAgBA,EAAapW,QAAQ,SAAArS,UAAKA,EAAEmO,QAAO,GAAO,KAC1DlO,GAAK4Z,MAAQA,EACb5Z,GAAK6Z,IAAMA,EACXoH,EAAUC,EAAUxK,GAAiBiM,GAAapQ,KAC7CW,IAAuBwD,KAC3BuK,EAAU0B,IAAcpQ,GAAWoQ,IACnC3iB,GAAKgZ,OAAOrC,IAAM,GAEnB3W,GAAKkO,QAAO,GAAO,GACnByW,GAAc9iB,KACV6gB,KACHgC,IAAY,EAEZhC,GAAgBjX,SAAQ,IAEzB6J,GAAc,EACdjF,GAAauT,KAAavT,EAAUgV,UAAYzC,KAAqBvS,EAAUE,aAAeqS,IAAoBvS,EAAUE,SAASqS,IAAoB,GAAG,GAAMxC,OAAO/P,EAAUmJ,QAAQ,GAAM,IAC7LoP,GAAkBhE,KAAiB5kB,GAAKuQ,UAAY2C,IAAsBkQ,IAAwB/S,IAAcA,EAAUgV,YAC7HhV,IAAcuT,KAAavT,EAAUgV,UAAYT,KAAmD,IAAnCvU,EAAUlK,KAAKof,kBAA8BlV,EAAU6U,cAAchS,IAAsB0G,GAAS,OAAUgL,GAAevnB,GAAK8C,MAAMmpB,UAAU1P,EAAOC,EAAK,GAAK+K,IAAc,GAClP5kB,GAAKuQ,SAAWqY,IAAoB3H,EAAUrH,GAAS6H,IAAWmD,GAAgB,EAAIA,IAEvFtN,IAAO6L,KAAe5L,EAAOgS,WAAa/pB,KAAKC,MAAMO,GAAKuQ,SAAWyR,IACrEO,IAAcA,GAAWyF,aAEpBnb,MAAM6b,KACVA,GAAqBrrB,GAAKkE,YAAY8f,EAAoBvP,GAAUtM,GACpEmjB,GAAmBtrB,GAAKkE,YAAY+f,EAAkBxP,GAAUtM,GAChE2V,GAAakG,EAAoBvP,GAAW4W,GAC5CvN,GAAagG,EAAarP,GAAW4W,GAAqBX,GAAa,IACvE5M,GAAamG,EAAkBxP,GAAW6W,GAC1CxN,GAAaiG,EAAWtP,GAAW6W,GAAmBZ,GAAa,KAGpEa,IAAmBlS,IAAkB1W,GAAK2B,UAEtC0e,IAAc3J,IAAmB8K,IACpCA,GAAqB,EACrBnB,GAAUrgB,IACVwhB,GAAqB,KAIvBxhB,GAAKsC,YAAc,kBAAQiQ,KAAe2O,IAAYrf,KAAaya,IAAU,KAAS,GAEtFtc,GAAK4pB,aAAe,WACnBxZ,GAAcpQ,GAAK6Q,mBACfR,IACHkS,GAAaA,GAAWhS,SAAS,GAAOF,EAAU8U,SAA4DvB,IAAYxT,GAAcC,EAAWrQ,GAAK8R,UAAY,EAAG,GAA1G1B,GAAcC,EAAWA,EAAUC,cAIlGtQ,GAAK6pB,cAAgB,SAAAC,UAASzZ,GAAaA,EAAUgB,SAAYuI,GAAS5Z,GAAKyf,WAAa7F,GAAUvJ,EAAUgB,OAAOyY,GAASzZ,EAAUiB,WAAcmQ,GAAW,GAEnKzhB,GAAK+pB,YAAc,SAAApmB,OACd5C,EAAIwV,GAAU3Y,QAAQoC,IACzBgD,EAAqB,EAAjBhD,GAAK8R,UAAgByE,GAAU/H,MAAM,EAAGzN,GAAGipB,UAAYzT,GAAU/H,MAAMzN,EAAE,UACtE4N,GAAUhL,GAAQX,EAAE+K,OAAO,SAAAhO,UAAKA,EAAEoG,KAAKwd,kBAAoBhgB,IAAQX,GAAG+K,OAAO,SAAAhO,UAAsB,EAAjBC,GAAK8R,UAAgB/R,EAAE8Z,KAAOD,EAAQ7Z,EAAE6Z,OAASC,KAI5I7Z,GAAK2B,OAAS,SAACU,EAAOoe,EAAgBwJ,OACjC/W,IAAuB+W,GAAc5nB,OAOxC0jB,EAAqBmE,EAAaC,EAAQC,EAAcC,EAASC,EAASC,EAJvEvR,GAA4B,IAAnBtC,GAA0BiM,GAAa3iB,GAAKgZ,SACxDxT,EAAInD,EAAQ,GAAK2W,EAASY,GAAS6H,EACnC+I,EAAUhlB,EAAI,EAAI,EAAQ,EAAJA,EAAQ,EAAIA,GAAK,EACvCof,EAAe5kB,GAAKuQ,YAEjBkQ,IACHS,EAAUD,EACVA,EAAU/N,GAAqBX,KAAeyG,EAC1CvH,KACH6Q,EAAQD,EACRA,EAAQhS,IAAcuT,GAAWvT,EAAU6U,gBAAkBsF,IAI3DhM,GAAiBlH,KAAQhC,KAAgBpW,IAAY2V,MACnD2V,GAAW5Q,EAAQZ,GAAWA,EAASkI,IAAYrf,KAAaya,IAAWkC,EAC/EgM,EAAU,KACY,IAAZA,GAAiB3Q,EAAMb,GAAWA,EAASkI,IAAYrf,KAAaya,IAAWkC,IACzFgM,EAAU,QAGRA,IAAY5F,GAAgB5kB,GAAKyQ,QAAS,IAI7C2Z,GADAC,GAFAtE,EAAW/lB,GAAK+lB,WAAayE,GAAWA,EAAU,OACpC5F,GAAgBA,EAAe,OAEjB4F,KAAc5F,EAC1C5kB,GAAK8R,UAAsB8S,EAAV4F,EAAyB,GAAK,EAC/CxqB,GAAKuQ,SAAWia,EAEZJ,IAAiB9U,KACpB4U,EAAcM,IAAY5F,EAAe,EAAgB,IAAZ4F,EAAgB,EAAqB,IAAjB5F,EAAqB,EAAI,EACtFhB,KACHuG,GAAWE,GAA8C,SAAnC9L,GAAc2L,EAAc,IAAiB3L,GAAc2L,EAAc,IAAO3L,GAAc2L,GACpHK,EAAiBla,IAAyB,aAAX8Z,GAAoC,UAAXA,GAAsBA,KAAU9Z,KAI1FsT,KAAoB0G,GAAWE,KAAoBA,GAAkBrH,IAAU7S,KAAeJ,GAAY0T,IAAmBA,GAAgB3jB,IAAQA,GAAK+pB,YAAYpG,IAAiBvR,QAAQ,SAAArS,UAAKA,EAAE6pB,kBAEjMhG,MACArB,IAAejN,IAAgBpW,GAQxBmR,GACVA,EAAU6U,cAAcsF,KAAYlV,KAAgBqP,KAAetiB,KARlEkgB,GAAWkI,IAAIC,MAAQnI,GAAWoI,SAAWpI,GAAWmI,OAAUnI,GAAWnC,OAAOmC,GAAWkI,IAAIC,MAAQnI,GAAWoI,QACnHpI,GAAW+D,QACd/D,GAAW+D,QAAQ,gBAAiBkE,EAASna,EAAUkW,OAASlW,EAAUmW,QAE1EjE,GAAWpc,KAAK+e,cAAgBsF,EAChCjI,GAAWyF,aAAavc,aAMvB6L,MACHjV,GAAS8gB,KAAe5L,EAAOnD,MAAM+O,GAAarR,GAAUnM,KAAOsc,GAC9DxO,IAEE,GAAI2W,EAAc,IACxBE,GAAWjoB,GAAmBuiB,EAAV4F,GAAoCxR,EAAVa,EAAM,GAAcb,EAAS,GAAKnJ,GAAWyL,GAAUxJ,IACjG0R,MACEnhB,IAAU0jB,IAAYuE,EAK1B/P,GAAUjD,GAAKC,OALqB,KAChCuC,EAASlK,GAAW0H,IAAK,GAC5BzX,EAASmZ,EAASY,EACnBW,GAAUjD,GAAKrT,GAAQ6V,EAAOK,KAAOrI,KAAc9Q,GAAYnB,EAAS,GAAMuY,GAAM0B,EAAOI,MAAQpI,KAAc9Q,GAAY,EAAInB,GAAWuY,IAK9II,GAAUuN,GAAYuE,EAAU3I,EAAiBC,GAChDO,GAAYqI,EAAU,GAAKzE,GAAajE,EAAUC,GAAwB,IAAZyI,GAAkBF,EAAsB,EAAZtI,UAb3FF,EAAU5S,GAAO6S,EAAWC,EAAYwI,KAgB1C/Y,IAASqP,EAAQpF,OAAUpG,IAAgBpW,IAAYwjB,GAAgBjX,SAAQ,GAC/EuX,IAAgBqH,GAAY9G,IAAQiH,IAAYA,EAAU,IAAMzN,MAAsB5F,GAAS6L,EAAY0D,SAAStU,QAAQ,SAAAtU,UAAMA,EAAGgpB,UAAUf,GAAYxC,GAAO,MAAQ,UAAUP,EAAY2D,cAChMzK,GAAa0H,IAAavhB,GAAS6Z,EAASlc,IACxCoqB,IAAiB9U,IAChBsO,KACC2G,IACY,aAAXJ,EACH9Z,EAAU3D,QAAQwY,cAAc,GACX,UAAXiF,EACV9Z,EAAU5E,SAAQ,GAAMiB,QACH,YAAXyd,EACV9Z,EAAU5E,SAAQ,GAElB4E,EAAU8Z,MAGZjO,GAAYA,EAASlc,MAElBqqB,GAAYtN,KACfkG,GAAYoH,GAAW7Z,GAAUxQ,GAAMijB,GACvCc,GAAUmG,IAAgB1Z,GAAUxQ,GAAM+jB,GAAUmG,IACpD3G,KAAqB,IAAZiH,EAAgBxqB,GAAKiO,MAAK,EAAO,GAAM8V,GAAUmG,GAAe,GACpEG,GAEJtG,GADAmG,EAA0B,IAAZM,EAAgB,EAAI,IACRha,GAAUxQ,GAAM+jB,GAAUmG,KAGlDxG,KAAkBqC,GAAYvmB,KAAKyD,IAAIjD,GAAKsC,gBAAkB4N,GAAUwT,IAAiBA,GAAgB,QAC5GtT,GAAcpQ,GAAK6Q,mBACnB0R,GAAaA,GAAWhS,SAAS,GAAKH,GAAcC,EAAsB,YAAX8Z,EAAuB,GAAKK,EAAS,KAE3F5G,IAAY1H,IAAa5G,IACnC4G,EAASlc,OAIPoiB,EAAiB,KAChBwI,EAAI1X,GAAqB8F,EAAS9F,GAAmB5B,YAAc4B,GAAmBoH,eAAiB,GAAKtB,EAChHkJ,EAAkB0I,GAAKvJ,EAAmBtC,WAAa,EAAI,IAC3DqD,EAAgBwI,GAEjB/H,GAAkBA,GAAgB7J,EAAS9F,GAAmB5B,YAAc4B,GAAmBoH,eAAiB,MAGjHta,GAAK4N,OAAS,SAACvL,EAAOod,GAChBzf,GAAKyQ,UACTzQ,GAAKyQ,SAAU,EACfzS,GAAasd,GAAU,SAAUjG,IACjChJ,IAAcrO,GAAasd,GAAU,SAAU9c,IAC/C6lB,IAAiBrmB,GAAamF,cAAe,cAAekhB,KAC9C,IAAVhiB,IACHrC,GAAKuQ,SAAWqU,GAAe,EAC/B3D,EAAUC,EAAUwD,GAAWnS,OAEpB,IAAZkN,GAAqBzf,GAAKyf,YAI5Bzf,GAAKub,SAAW,SAAA9J,UAAQA,GAAQqP,EAAUA,EAAQpF,MAAQ6G,IAE1DviB,GAAKmgB,aAAe,SAAC0K,EAAUC,EAAQC,EAAWhD,MAC7C7U,GAAoB,KACnByS,EAAKzS,GAAmByG,cAC3BrI,EAAW4B,GAAmB5B,WAC9BmQ,EAASkE,EAAG9L,IAAM8L,EAAG/L,MACtBiR,EAAWlF,EAAG/L,MAAQ6H,EAASoJ,EAAWvZ,EAC1CwZ,EAASnF,EAAG/L,MAAQ6H,EAASqJ,EAASxZ,EAEvCtR,GAAKyf,SAAQ,GAAO,EAAO,CAAC7F,MAAO/K,GAAWgc,EAAUE,KAAe/qB,GAAKkgB,aAAcrG,IAAKhL,GAAWic,EAAQC,KAAe/qB,GAAKggB,YAAa+H,GACnJ/nB,GAAK2B,UAGN3B,GAAK6f,iBAAmB,SAAAmL,MACnBvT,GAAeuT,EAAQ,KACtBjqB,EAAI0W,EAAY7Z,QAAQkU,GAAUlM,GAAK,EAC3C6R,EAAY1W,GAAMmL,WAAWuL,EAAY1W,IAAMiqB,EAAU5S,GACzDX,EAAY,GAAMvL,WAAWuL,EAAY,IAAMuT,EAAU5S,GACzDI,GAAUf,KAIZzX,GAAK8N,QAAU,SAACzL,EAAO4oB,MAClBjrB,GAAKyQ,WACE,IAAVpO,GAAmBrC,GAAKkO,QAAO,GAAM,GACrClO,GAAKyQ,QAAUzQ,GAAK+lB,UAAW,EAC/BkF,GAAmB1I,IAAcA,GAAW7V,QAC5CiW,GAAa,EACb5B,IAAaA,EAAS5K,QAAU,GAChCkO,IAAiB/lB,GAAgB6E,cAAe,cAAekhB,IAC3D3B,KACHA,GAAgBhW,QAChBoU,EAAQpF,OAASoF,EAAQpF,MAAMzN,SAAW6S,EAAQpF,MAAQ,KAEtDrP,IAAY,SACZtL,EAAIwV,GAAUnV,OACXL,QACFwV,GAAUxV,GAAGua,WAAaA,IAAY/E,GAAUxV,KAAOf,UAI5D1B,GAAgBgd,GAAU,SAAUjG,IACpChJ,IAAc/N,GAAgBgd,GAAU,SAAU9c,MAKrDwB,GAAKiO,KAAO,SAACC,EAAQ+c,GACpBjrB,GAAK8N,QAAQI,EAAQ+c,GACrB1I,KAAe0I,GAAkB1I,GAAWtU,OAC5ClE,UAAciV,GAAKjV,OACfhJ,EAAIwV,GAAU3Y,QAAQoC,IACrB,GAALe,GAAUwV,GAAUpI,OAAOpN,EAAG,GAC9BA,IAAMuV,IAAmB,EAAbiK,IAAkBjK,KAG9BvV,EAAI,EACJwV,GAAUnE,QAAQ,SAAArS,UAAKA,EAAEub,WAAatb,GAAKsb,WAAava,EAAI,KAC5DA,GAAK2V,KAAmB1W,GAAKgZ,OAAOrC,IAAM,GAEtCtG,IACHA,EAAUsJ,cAAgB,KAC1BzL,GAAUmC,EAAUnC,OAAO,CAACD,MAAM,IAClCgd,GAAkB5a,EAAUpC,QAE7BkT,GAAe,CAACA,EAAaC,EAAWC,EAAoBC,GAAkBlP,QAAQ,SAAAiI,UAAKA,EAAE5B,YAAc4B,EAAE5B,WAAWzB,YAAYqD,KACpI4C,KAAajd,KAASid,GAAW,GAC7B3F,KACHyJ,IAAaA,EAAS5K,QAAU,GAChCpV,EAAI,EACJwV,GAAUnE,QAAQ,SAAArS,UAAKA,EAAEuX,MAAQA,IAAOvW,MACxCA,IAAMggB,EAASxJ,OAAS,IAEzBpR,EAAK+kB,QAAU/kB,EAAK+kB,OAAOlrB,KAG5BuW,GAAUtV,KAAKjB,IACfA,GAAK4N,QAAO,GAAO,GACnBkV,GAAsBA,EAAmB9iB,IAErCqQ,GAAaA,EAAUM,MAAQ8Q,EAAQ,KACtC0J,EAAanrB,GAAK2B,OACtB3B,GAAK2B,OAAS,WACb3B,GAAK2B,OAASwpB,EACdxsB,GAAWC,QACXgb,GAASC,GAAO7Z,GAAKyf,WAEtBpiB,GAAK4P,YAAY,IAAMjN,GAAK2B,QAC5B8f,EAAS,IACT7H,EAAQC,EAAM,OAEd7Z,GAAKyf,UAENnI,IAlhCkB,SAAnB8T,sBACKpO,KAAoBoC,GAAY,KAC/BrV,EAAKiT,GAAkBoC,GAC3B9U,sBAAsB,kBAAMP,IAAOqV,IAAcvJ,IAAY,MA+gCvDuV,aA7qBDzpB,OAASyK,KAAKqT,QAAUrT,KAAK6B,KAAOgB,kBAirBpCX,SAAP,kBAAgBlL,UACVS,IACJxG,GAAO+F,GAAQhG,KACf+R,MAAmB7R,OAAOwG,UAAYX,cAAcyK,SACpD/J,EAAesZ,IAETtZ,iBAGDoN,SAAP,kBAAgB5Q,MACXA,MACE,IAAImF,KAAKnF,EACbie,GAAU9Y,GAAKnF,EAAOmF,UAGjB8Y,kBAGDxQ,QAAP,iBAAezL,EAAO4L,GACrBkP,GAAW,EACX5G,GAAUnE,QAAQ,SAAAiE,UAAWA,EAAQpI,EAAO,OAAS,WAAW5L,KAChE/D,GAAgBa,GAAM,QAASX,IAC/BF,GAAgBsC,GAAM,SAAUpC,IAChC6sB,cAAc9O,GACdje,GAAgBsC,GAAM,cAAeqO,IACrC3Q,GAAgB2F,GAAO,aAAcgL,IACrCgD,GAAe3T,GAAiBsC,GAAM,mCAAoCkO,IAC1EmD,GAAe3T,GAAiBsC,GAAM,6BAA8BoO,IACpE2G,EAAa1H,OACb6B,GAAoBxR,QACf,IAAIyC,EAAI,EAAGA,EAAIpC,GAAWyC,OAAQL,GAAG,EACzCuR,GAAehU,GAAiBK,GAAWoC,GAAIpC,GAAWoC,EAAE,IAC5DuR,GAAehU,GAAiBK,GAAWoC,GAAIpC,GAAWoC,EAAE,mBAIvD6M,OAAP,qBACCzO,GAAO7B,OACPsD,GAAOkD,SACPhD,GAASF,GAAKoD,gBACdC,GAAQrD,GAAKmD,KACT1G,KACH8Z,GAAW9Z,GAAK8C,MAAMC,QACtBic,GAAShf,GAAK8C,MAAM+D,MACpBC,EAAW9G,GAAK+F,KAAKgB,SAAW6K,GAChC2N,GAAsBvf,GAAK+F,KAAKkoB,oBAAsBrc,GACtD2H,EAAqBzX,GAAKC,QAAQC,mBAAqB,OACvDihB,EAAcnhB,GAAK8G,aAAe,EAClC5I,GAAK+F,KAAKC,QAAQ,gBAAiBF,eAC/Bc,IAAO,CACVkZ,GAAW,GACXrG,EAAYhT,SAAS0P,cAAc,QACzBY,MAAMzE,OAAS,QACzBmH,EAAU1C,MAAM2D,SAAW,WAC3BlB,KA7yCU,SAAb0U,oBAAmBpO,IAAY7S,sBAAsBihB,YA8yClDA,GACAhnB,EAAS+J,SAASjR,IAElB8F,cAAcqB,QAAUD,EAASC,QACjCqY,EAAatY,EAASC,SAAW,0BAA0BkW,KAAK/V,UAAU6mB,WAC1E9V,EAA2C,IAArBnR,EAASC,QAC/BxG,GAAamB,GAAM,QAASX,IAC5BT,EAAQ,CAACoB,GAAMyB,GAAME,GAAQmD,IACzB5G,GAAKoH,YACRtB,cAAcsB,WAAa,SAAA0B,OAEzBX,EADGimB,EAAKpuB,GAAKoH,iBAETe,KAAKW,EACTslB,EAAG9a,IAAInL,EAAGW,EAAKX,WAETimB,GAERpuB,GAAKgB,iBAAiB,iBAAkB,kBAAM+X,OAC9C/Y,GAAKgB,iBAAiB,mBAAoB,kBAAMyX,OAChDzY,GAAKgB,iBAAiB,aAAc,WACnCwX,GAAY,EAAG,GACfZ,EAAU,gBAEX5X,GAAKoH,aAAakM,IAAI,0BAA2B,kBAChDuE,KACOA,MAGR3U,QAAQC,KAAK,iCAEd0U,KACAlX,GAAa4C,GAAM,SAAUpC,QAK5Bsb,EAAQ/Y,EAJL2qB,EAAeznB,GAAM0nB,aAAa,SACrCC,EAAY3nB,GAAMmQ,MAClByX,EAASD,EAAUE,eACnBC,EAAiB1uB,GAAK+F,KAAK4oB,UAAUC,cAEtCF,EAAe7d,QAAUge,OAAOC,eAAeJ,EAAgB,SAAU,CAAE9sB,MAAO,wBAAoBmN,KAAKoN,MAAM,KAAM,MACvHoS,EAAUE,eAAiB,QAC3BhS,EAASlK,GAAW3L,IACpBjD,GAAUqZ,EAAI7a,KAAKC,MAAMqa,EAAOK,IAAMnZ,GAAUL,OAAS,EACzD4E,GAAY8U,EAAI7a,KAAKC,MAAMqa,EAAOI,KAAO3U,GAAY5E,OAAS,EAC9DkrB,EAAUD,EAAUE,eAAiBD,EAAUD,EAAUxR,eAAe,oBACnEsR,IACJznB,GAAMkQ,aAAa,QAAS,IAC5BlQ,GAAMmoB,gBAAgB,UAGvB7P,EAAgB8P,YAAYzX,GAAO,KACnCvX,GAAK4P,YAAY,GAAK,kBAAM/N,GAAW,IACvClB,GAAa4C,GAAM,cAAeqO,IAClCjR,GAAaiG,GAAO,aAAcgL,IAClCgD,GAAejU,GAAc4C,GAAM,mCAAoCkO,IACvEmD,GAAejU,GAAc4C,GAAM,6BAA8BoO,IACjEwN,EAAiBnf,GAAK8C,MAAMmsB,YAAY,aACxC1T,GAAY3X,KAAKub,GACjB3Y,EAAehC,KACf8T,EAAetY,GAAK4P,YAAY,GAAK4I,IAAanJ,QAClDsD,EAAe,CAACpP,GAAM,mBAAoB,eACrC2rB,EAAIptB,GAAKuQ,WACZ8c,EAAIrtB,GAAK0M,YACNjL,GAAK6rB,QACRhQ,EAAa8P,EACb7P,EAAc8P,GACJ/P,IAAe8P,GAAK7P,IAAgB8P,GAC9CnX,MAECzU,GAAM,mBAAoBiV,GAAa1W,GAAM,OAAQ0W,GAAa1W,GAAM,SAAUkW,IACrFvF,GAAoB9R,IACpBuY,GAAUnE,QAAQ,SAAAiE,UAAWA,EAAQzI,OAAO,EAAG,KAC1C7M,EAAI,EAAGA,EAAIpC,GAAWyC,OAAQL,GAAG,EACrCuR,GAAehU,GAAiBK,GAAWoC,GAAIpC,GAAWoC,EAAE,IAC5DuR,GAAehU,GAAiBK,GAAWoC,GAAIpC,GAAWoC,EAAE,oBAMzDV,OAAP,gBAAc8F,sBACQA,IAAU4W,KAAoB5W,EAAKumB,oBACpDC,EAAKxmB,EAAKymB,aACdD,GAAMtB,cAAc9O,KAAoBA,EAAgBoQ,IAAON,YAAYzX,GAAO+X,0BACzDxmB,IAAUuP,EAAgD,IAA1BvS,cAAcqB,SAAiB2B,EAAK0mB,oBACzF,sBAAuB1mB,IAC1B2J,GAAoBxR,KAAoBwR,GAAoB9R,GAAcmI,EAAK2mB,mBAAqB,QACpGvX,GAAqE,KAApDpP,EAAK2mB,kBAAoB,IAAIlvB,QAAQ,0BAIjDmvB,cAAP,uBAAqB1rB,EAAQ8E,OACxBpG,EAAID,EAAWuB,GAClBN,EAAIpC,GAAWf,QAAQmC,GACvBsM,EAAaxO,GAAYkC,IACrBgB,GACJpC,GAAWwP,OAAOpN,EAAGsL,EAAa,EAAI,GAEnClG,IACHkG,EAAa1O,GAASyrB,QAAQjqB,GAAMgH,EAAMlC,GAAOkC,EAAMrF,GAAQqF,GAAQxI,GAASyrB,QAAQrpB,EAAGoG,mBAItF6mB,gBAAP,yBAAuB/W,GACtBM,GAAUnE,QAAQ,SAAArS,UAAKA,EAAEE,MAAQF,EAAEE,KAAKgW,QAAUA,GAASlW,EAAEE,KAAKgO,MAAK,GAAM,oBAGvEgf,aAAP,sBAAoBxvB,EAASwe,EAAO0D,OAC/B7F,GAAUnL,GAAUlR,GAAWqC,EAAWrC,GAAWA,GAAS2gB,wBACjEve,EAASia,EAAO6F,EAAaxH,GAASE,IAAW4D,GAAS,SACpD0D,EAAqC,EAAxB7F,EAAO6E,MAAQ9e,GAAcia,EAAOI,KAAOra,EAASV,GAAKuQ,WAAsC,EAAzBoK,EAAO4E,OAAS7e,GAAcia,EAAOK,IAAMta,EAASV,GAAK0M,2BAG7IqhB,mBAAP,4BAA0BzvB,EAAS0vB,EAAgBxN,GAClDhR,GAAUlR,KAAaA,EAAUqC,EAAWrC,QACxCqc,EAASrc,EAAQ2gB,wBACpB1L,EAAOoH,EAAO6F,EAAaxH,GAASE,IACpCxY,EAA2B,MAAlBstB,EAAyBza,EAAO,EAAMya,KAAkBra,EAAaA,EAAUqa,GAAkBza,GAAQya,EAAevvB,QAAQ,KAAOsO,WAAWihB,GAAkBza,EAAO,IAAMxG,WAAWihB,IAAmB,SAClNxN,GAAc7F,EAAOI,KAAOra,GAAUV,GAAKuQ,YAAcoK,EAAOK,IAAMta,GAAUV,GAAK0M,2BAGtFuhB,QAAP,iBAAeC,MACd9W,GAAU/H,MAAM,GAAG4D,QAAQ,SAAArS,SAAmB,mBAAdA,EAAEoG,KAAK4D,IAA2BhK,EAAEkO,UAC7C,IAAnBof,EAAyB,KACxBC,EAAYrO,EAAWmO,SAAW,GACtCnO,EAAa,GACbqO,EAAUlb,QAAQ,SAAAtT,UAAKA,8CA92BbqH,EAAMkK,GACjBxM,GAAgBV,cAAcmL,SAASjR,KAASkD,QAAQC,KAAK,6CAC7D2D,EAASiI,WACJlG,KAAKC,EAAMkK,MAi3BJjC,QAAU,YACVmf,WAAa,SAAA7G,UAAWA,EAAUvP,GAASuP,GAAStU,QAAQ,SAAA/Q,MACrEA,GAAUA,EAAO+S,MAAO,KACvBrT,EAAIiV,EAAapY,QAAQyD,GACxB,GAALN,GAAUiV,EAAa7H,OAAOpN,EAAG,GACjCiV,EAAa/U,KAAKI,EAAQA,EAAO+S,MAAMC,QAAShT,EAAO6U,SAAW7U,EAAOmsB,aAAa,aAAcnwB,GAAK+F,KAAKuX,SAAStZ,GAAS8C,QAE7H6R,MACS9H,OAAS,SAAC4Z,EAAM/R,UAAUK,IAAY0R,EAAM/R,OAC5C1H,OAAS,SAAClI,EAAMkK,UAAc,IAAIlN,GAAcgD,EAAMkK,OACtDoP,QAAU,SAAAgO,UAAQA,EAAOpY,IAAU,IAASxR,GAAgBV,GAAcmL,aAAeuH,IAAY,OACrGlU,OAAS,SAAAC,WAAWjD,GAAWC,OAASmW,GAAqB,IAAVnT,EAAiB,EAAI,OACxE8rB,kBAAoBjX,MACpBkX,UAAY,SAAClwB,EAASkiB,UAAe9P,GAAWpS,EAASkiB,EAAapa,GAAcvE,QACpF4sB,cAAgB,SAACnwB,EAASkiB,UAAelf,EAAeX,EAAWrC,GAAUkiB,EAAapa,GAAcvE,QACxGyN,QAAU,SAAA1E,UAAMiV,GAAKjV,OACrBwE,OAAS,kBAAMgI,GAAUxI,OAAO,SAAAhO,SAAmB,mBAAdA,EAAEoG,KAAK4D,SAC5C8jB,YAAc,mBAAQhZ,OACtBiZ,gBAAkBvc,MAClBlT,iBAAmB,SAACJ,EAAMkU,OACnCnP,EAAIic,EAAWhhB,KAAUghB,EAAWhhB,GAAQ,KAC/C+E,EAAEpF,QAAQuU,IAAanP,EAAE/B,KAAKkR,OAElB5T,oBAAsB,SAACN,EAAMkU,OACtCnP,EAAIic,EAAWhhB,GAClB8C,EAAIiC,GAAKA,EAAEpF,QAAQuU,GACf,GAALpR,GAAUiC,EAAEmL,OAAOpN,EAAG,OAETgtB,MAAQ,SAACrH,EAASvgB,GAKd,SAAhB6nB,GAAiB/vB,EAAMkU,OAClB8b,EAAW,GACdC,EAAW,GACXrI,EAAQxoB,GAAK4P,YAAYkhB,EAAU,WAAOhc,EAAS8b,EAAUC,GAAWD,EAAW,GAAIC,EAAW,KAAMxhB,eAClG,SAAA1M,GACNiuB,EAAS7sB,QAAUykB,EAAMpa,SAAQ,GACjCwiB,EAAShtB,KAAKjB,EAAKqW,SACnB6X,EAASjtB,KAAKjB,GACdouB,GAAYH,EAAS7sB,QAAUykB,EAAMtV,SAAS,QAGhD/K,EAfGkL,EAAS,GACZ2d,EAAW,GACXF,EAAWhoB,EAAKgoB,UAAY,KAC5BC,EAAWjoB,EAAKioB,UAAY,QAaxB5oB,KAAKW,EACTkoB,EAAS7oB,GAAyB,OAAnBA,EAAEoJ,OAAO,EAAG,IAAeqB,GAAY9J,EAAKX,KAAa,kBAANA,EAAyBwoB,GAAcxoB,EAAGW,EAAKX,IAAMW,EAAKX,UAEzHyK,GAAYme,KACfA,EAAWA,IACXpwB,GAAamF,GAAe,UAAW,kBAAMirB,EAAWjoB,EAAKioB,cAE9DjX,GAASuP,GAAStU,QAAQ,SAAA/Q,OACrBhB,EAAS,OACRmF,KAAK6oB,EACThuB,EAAOmF,GAAK6oB,EAAS7oB,GAEtBnF,EAAOgW,QAAUhV,EACjBqP,EAAOzP,KAAKkC,GAAckL,OAAOhO,MAE3BqQ,GAKmC,SAAvC4d,GAAwC/b,EAAY2I,EAASrB,EAAKzX,UAC1DA,EAAV8Y,EAAgB3I,EAAWnQ,GAAO8Y,EAAU,GAAK3I,EAAW,GAC/CnQ,EAANyX,GAAazX,EAAM8Y,IAAYrB,EAAMqB,GAAWrB,EAAM,EAAIqB,GAAWA,EAAUrB,GAAO,EAExE,SAAtB0U,GAAuBltB,EAAQyQ,IACZ,IAAdA,EACHzQ,EAAO+S,MAAMgG,eAAe,gBAE5B/Y,EAAO+S,MAAMoa,aAA4B,IAAd1c,EAAqB,OAASA,EAAY,OAASA,GAAavN,EAASC,QAAU,cAAgB,IAAM,OAErInD,IAAWP,IAAUytB,GAAoBtqB,GAAO6N,GAGjC,SAAhB2c,UAGqBjX,EAHH5Q,IAAAA,MAAOvF,IAAAA,OAAQmJ,IAAAA,KAC5BkkB,GAAQ9nB,EAAM9D,eAAiB8D,EAAM9D,eAAe,GAAK8D,GAAOvF,OACnEzC,EAAQ8vB,EAAKhX,OAASra,GAAK+F,KAAKuX,SAAS+T,GACzClV,EAAO3X,SACHjD,EAAM+vB,YAAwC,IAA1BnV,EAAO5a,EAAM+vB,WAAmB,MACjDD,GAAQA,IAASzqB,KAAWyqB,EAAKE,cAAgBF,EAAKG,cAAgBH,EAAKI,aAAeJ,EAAK1Z,cAAkB+Z,IAAWvX,EAAK1G,GAAkB4d,IAAOM,aAAcD,GAAUvX,EAAGyX,aAAcP,EAAOA,EAAKjW,WACtN7Z,EAAMswB,UAAYR,GAAQA,IAASrtB,IAAWxD,GAAY6wB,KAAUK,IAAWvX,EAAK1G,GAAkB4d,IAAOM,YAAcD,GAAUvX,EAAGyX,YACxIrwB,EAAM+vB,WAAanV,GAEhB5a,EAAMswB,WAAsB,MAAT1kB,IACtB5D,EAAMuoB,kBACNvoB,EAAM/D,YAAa,GAIJ,SAAjBusB,GAAkB/tB,EAAQpD,EAAMoxB,EAAQC,UAAW/qB,EAAS8J,OAAO,CAClEhN,OAAQA,EACRjD,SAAS,EACTmI,UAAU,EACViC,UAAU,EACVvK,KAAMA,EACNiK,QAAUonB,EAASA,GAAUb,GAC7BznB,QAASsoB,EACTvoB,OAAQuoB,EACRnkB,SAAUmkB,EACVnnB,SAAU,2BAAMknB,GAAUrxB,GAAa4C,GAAM2D,EAASQ,WAAW,GAAIwqB,IAAgB,GAAO,IAC5FnnB,UAAW,4BAAM9J,GAAgBsC,GAAM2D,EAASQ,WAAW,GAAIwqB,IAAgB,MAWzD,SAAvBC,GAAuBrpB,GAoBH,SAAlBspB,YAAwBC,GAAgB,EAGzB,SAAfC,KACCC,EAAO/f,GAAWxO,EAAQL,IAC1B6uB,EAAexT,GAAOQ,EAAa,EAAI,EAAG+S,GAC1CE,IAAqBC,EAAe1T,GAAO,EAAGxM,GAAWxO,EAAQkE,MACjEyqB,EAAgB5Q,GAEK,SAAtB6Q,KACChJ,EAAQvP,MAAMxN,EAAIgF,GAAOhD,WAAW+a,EAAQvP,MAAMxN,GAAKmB,EAAYxL,QAAU,KAC7EonB,EAAQ7S,MAAM8b,UAAY,mDAAqDhkB,WAAW+a,EAAQvP,MAAMxN,GAAK,UAC7GmB,EAAYxL,OAASwL,EAAY1L,QAAU,EAqBjC,SAAXwwB,KACCR,KACIjU,EAAMqK,YAAcrK,EAAMvV,KAAKoF,QAAUqkB,IAC5CvkB,IAAgBukB,EAAOlU,EAAMnL,SAAS,IAAMlF,EAAYukB,GAAQlU,EAAM4K,QAAQ,UAAWsJ,IAvD5Fzf,GAAUhK,KAAUA,EAAO,IAC3BA,EAAKvD,eAAiBuD,EAAK4B,aAAe5B,EAAKoC,aAAc,EAC7DpC,EAAKlI,OAASkI,EAAKlI,KAAO,eAC1BkI,EAAKI,WAAaJ,EAAKI,SACvBJ,EAAK4D,GAAK5D,EAAK4D,IAAM,iBAEpB/J,EAAM4vB,EAWNI,EAAeN,EAkCfhU,EAAO0U,EAAcC,EAAc7kB,EA9C/BskB,EAA4D3pB,EAA5D2pB,iBAAkBQ,EAA0CnqB,EAA1CmqB,SAAUC,EAAgCpqB,EAAhCoqB,kBAAmBtpB,EAAad,EAAbc,UAEnD5F,EAASvB,EAAWqG,EAAK9E,SAAWP,GACpC0vB,EAAWnzB,GAAK+F,KAAKC,UAAUotB,eAC/BC,EAAmBF,GAAYA,EAASG,MACxC1J,EAAUpK,IAAgB1W,EAAK8gB,SAAWnnB,EAAWqG,EAAK8gB,UAAcyJ,IAAqC,IAAjBvqB,EAAK8gB,UAAsByJ,EAAiBpvB,UAAYovB,EAAiBzJ,WACrK5b,EAAc5K,EAAeY,EAAQL,IACrCoK,EAAc3K,EAAeY,EAAQkE,IACrC0Y,EAAQ,EACR2S,GAAgBrsB,EAASC,SAAWrF,GAAK0xB,eAAiB1xB,GAAK0xB,eAAe5S,MAAQ9e,GAAK0xB,eAAephB,MAAQtQ,GAAK2xB,YAAc3xB,GAAKuQ,WAC1IqhB,EAAe,EACfC,EAA0B/gB,GAAYqgB,GAAY,kBAAMA,EAAStwB,IAAQ,kBAAMswB,GAAY,KAE3FW,EAAgB7B,GAAe/tB,EAAQ8E,EAAKlI,MAAM,EAAMsyB,GAExDR,EAAe9gB,GACf4gB,EAAe5gB,UAqChBgY,GAAW5pB,GAAK+d,IAAI6L,EAAS,CAAC/c,EAAG,QACjC/D,EAAK2B,YAAc,SAAAnF,UAAMka,GAAyB,cAAXla,EAAE1E,MA1B3B,SAAbizB,gBACKxB,EAAe,CAClBplB,sBAAsBmlB,QAClB5vB,EAASqP,GAAOlP,EAAKsJ,OAAS,GACjC0P,EAAS6W,EAAaxkB,EAAY9L,EAAIM,MACnConB,GAAWjO,IAAW3N,EAAY9L,EAAI8L,EAAYxL,OAAQ,CAC7DwL,EAAYxL,OAASmZ,EAAS3N,EAAY9L,MACtC2K,EAAIgF,IAAQhD,WAAW+a,GAAWA,EAAQvP,MAAMxN,IAAM,GAAKmB,EAAYxL,QAC3EonB,EAAQ7S,MAAM8b,UAAY,mDAAqDhmB,EAAI,UACnF+c,EAAQvP,MAAMxN,EAAIA,EAAI,KACtBmB,EAAY1L,QAAUhB,GAAWC,MACjCmW,WAEM,EAER1J,EAAYxL,QAAUowB,KACtBP,GAAgB,EAU+CwB,IAA2B,KAARjT,GAA2B,eAAXtb,EAAE1E,MAA0B+B,EAAKiL,aAAgBtI,EAAEqI,SAA8B,EAAnBrI,EAAEqI,QAAQ5J,QAC5K+E,EAAKa,QAAU,WACd0oB,GAAgB,MACZyB,EAAYlT,EAChBA,EAAQ/O,IAAS/P,GAAK0xB,gBAAkB1xB,GAAK0xB,eAAe5S,OAAU,GAAK2S,GAC3ElV,EAAMhP,QACNykB,IAAclT,GAASsQ,GAAoBltB,EAAgB,KAAR4c,IAAsB6R,GAA2B,KACpGM,EAAehlB,IACfilB,EAAehlB,IACfskB,KACAK,EAAgB5Q,IAEjBjZ,EAAKc,UAAYd,EAAK6B,eAAiB,SAAChI,EAAM8M,MAC7CzB,EAAYxL,QAAUowB,KACjBnjB,EAEE,CACNnO,GAAWC,YAGVwyB,EAAenL,EADZoL,EAAML,IAENlB,IAEH7J,GADAmL,EAAgBhmB,KACmB,IAANimB,GAAcrxB,EAAKsxB,UAAa,KAC7DD,GAAO/C,GAAqCljB,EAAagmB,EAAenL,EAAWpW,GAAWxO,EAAQkE,KACtGmW,EAAMvV,KAAKmF,QAAUykB,EAAa9J,IAGnCA,GADAmL,EAAgB/lB,KACmB,IAANgmB,GAAcrxB,EAAKuxB,UAAa,KAC7DF,GAAO/C,GAAqCjjB,EAAa+lB,EAAenL,EAAWpW,GAAWxO,EAAQL,KACtG0a,EAAMvV,KAAKoF,QAAUskB,EAAa5J,GAClCvK,EAAMsM,aAAa1W,SAAS+f,GAAKG,KAAK,MAClC3U,GAAcnB,EAAMvV,KAAKoF,SAAWqkB,GAAyBA,EAAK,GAAtBwB,IAC/C/zB,GAAK+e,GAAG,GAAI,CAACF,SAAUiU,GAAU7e,SAAU+f,SAlB5C7lB,EAAkBC,SAAQ,GAqB3BxE,GAAaA,EAAUjH,IAExBmG,EAAK+B,QAAU,WACdwT,EAAM+V,KAAO/V,EAAMhP,QACa,IAA5B7K,KAAakvB,IAChBf,EAAgB,EAChBe,EAAelvB,OAGjBsE,EAAKqB,SAAW,SAACxH,EAAMmJ,EAAIE,EAAIqoB,EAAQC,MACtCvS,KAAe4Q,GAAiBL,KAChCxmB,GAAM2mB,GAAoB1kB,EAAY2kB,EAAa2B,EAAO,KAAOvoB,EAAKinB,GAAgBpwB,EAAK6K,OAAS7K,EAAKiK,GAAKmB,IAAgBjC,EAAKuoB,EAAO,KACtIroB,EAAI,CACPgC,EAAYxL,QAAUowB,SAClBzrB,EAAUmtB,EAAO,KAAOtoB,EAC3Ba,EAAI1F,EAAU6rB,EAAerwB,EAAK8K,OAAS9K,EAAKkK,EAAImB,IAAgBhC,EAAKsoB,EAAO,GAChFC,EAAW/B,EAAa3lB,GACzB1F,GAAW0F,IAAM0nB,IAAavB,GAAgBuB,EAAW1nB,GACzDmB,EAAYumB,IAEZvoB,GAAMF,IAAO4L,KAEf5O,EAAKgC,SAAW,WACfomB,GAAoBltB,GAAQyuB,GAA2B,KACvD3sB,GAAc9E,iBAAiB,UAAW8xB,IAC1CnyB,GAAamB,GAAM,SAAUgxB,IACzB9kB,EAAY/J,SACf+J,EAAYhK,OAAO+S,MAAMoL,eAAiB,OAC1CnU,EAAY/J,OAAS8J,EAAY9J,QAAS,GAE3C2vB,EAAcrjB,UAEfzH,EAAKiC,UAAY,WAChBmmB,GAAoBltB,GAAQ,GAC5B/C,GAAgBa,GAAM,SAAUgxB,IAChChtB,GAAc5E,oBAAoB,UAAW4xB,IAC7Cc,EAAchjB,QAEf9H,EAAKqC,UAA6B,IAAlBrC,EAAKqC,WACrBxI,EAAO,IAAIuE,EAAS4B,IACfzG,IAAMmd,KACIxR,KAAiBA,EAAY,GAC5CwR,GAAcxf,GAAKw0B,OAAOlhB,IAAI1B,IAC9BzD,EAAoBxL,EAAK0N,IACzBgO,EAAQre,GAAK+e,GAAGpc,EAAM,CAACilB,KAAM,SAAUE,QAAQ,EAAMnJ,SAAS,EAAO1Q,QAASwkB,EAAmB,QAAU,MAAOvkB,QAAS,QAASqQ,UAAW,CAACrQ,QAASqP,GAAqBvP,EAAaA,IAAe,kBAAMqQ,EAAMhP,WAAYwP,SAAUnH,EAAY4G,WAAYnQ,EAAkBrF,KAAKwV,aACpR3b,EA/LT,IA0CC8xB,GA9BA/C,GAAY,CAACgD,KAAM,EAAG/Y,OAAQ,GA6B9BgZ,GAAY,iCAEZzC,GAAiB,SAAjBA,eAAiB5sB,OACZsvB,EAAUD,GAAUtX,KAAK/X,EAAEtB,OAAO6wB,UAClCD,GAAWH,MACdnvB,EAAEE,YAAa,EACfivB,GAAkBG,OAmJPrgB,KAAO,SAAA1T,MAChB+R,GAAY/R,UACRqY,GAAU3E,KAAK1T,OAEnB8a,EAAS7Z,GAAK8G,aAAe,SACjC9C,GAAcoL,SAAS6D,QAAQ,SAAArS,UAAKA,EAAEoyB,OAASpyB,EAAEsW,QAAU2C,EAASjZ,EAAEsW,QAAQ+H,wBAAwBjE,IAAMpa,EAAE6Z,MAAQza,GAAK0M,cACpH0K,GAAU3E,KAAK1T,GAAS,SAAC8E,EAAG6O,UAAuC,KAAhC7O,EAAEmD,KAAK2e,iBAAmB,IAAa9hB,EAAEmD,KAAK+M,mBAAqB,IAAMlQ,EAAEmvB,UAAYtgB,EAAE1L,KAAK+M,mBAAqB,IAAMrB,EAAEsgB,SAA2C,KAAhCtgB,EAAE1L,KAAK2e,iBAAmB,UAE7LsN,QAAU,SAAAjsB,UAAQ,IAAI5B,EAAS4B,OAC/BksB,gBAAkB,SAAAlsB,WACV,IAAVA,SACH1H,MAEK,IAAT0H,GAAiB1H,SACbA,EAAYmP,aAEP,IAATzH,SACH1H,GAAeA,EAAYwP,YAC3BxP,EAAc0H,OAGXmsB,EAAansB,aAAgB5B,EAAW4B,EAAOqpB,GAAqBrpB,UACxE1H,GAAeA,EAAY4C,SAAWixB,EAAWjxB,QAAU5C,EAAYwP,OACvEpQ,GAAYy0B,EAAWjxB,UAAY5C,EAAc6zB,GAC1CA,MAIMlvB,KAAO,CACpB5B,iBAAAA,EACA4tB,eAAAA,GACAzwB,WAAAA,GACAhB,SAAAA,GACA6F,OAAQ,CAEP+uB,GAAI,cACH1d,IAAmBI,EAAU,eAC7BJ,GAAkBhT,MAGnB2wB,IAAK,sBAAMld,YAICjY,GAAKE,eAAe4F"}