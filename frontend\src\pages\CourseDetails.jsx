import React, { useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { useGSAP } from '@gsap/react';
import { 
  Play, 
  Clock, 
  Users, 
  Award, 
  CheckCircle, 
  Star,
  Calendar,
  Globe,
  Download,
  ChevronDown,
  ChevronUp,
  User,
  BookOpen,
  Target,
  Zap
} from 'lucide-react';

gsap.registerPlugin(ScrollTrigger, useGSAP);

const CourseDetails = () => {
  const { courseId } = useParams();
  const heroRef = useRef(null);
  const statsRef = useRef(null);
  const curriculumRef = useRef(null);
  const faqRef = useRef(null);
  
  const [activeModule, setActiveModule] = useState(null);
  const [activeFaq, setActiveFaq] = useState(null);

  // Sample course data - in real app, fetch based on courseId
  const courseData = {
    title: "DSA Domination Cohort",
    subtitle: "Zero To Job-Ready In 6 Months",
    description: "Master Data Structures & Algorithms with live mentorship, real projects, and guaranteed placement support.",
    instructor: {
      name: "<PERSON><PERSON><PERSON>",
      title: "Senior Software Engineer at Google",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      experience: "5+ years"
    },
    pricing: {
      original: 15999,
      current: 7999,
      discount: 50,
      emi: 1333
    },
    stats: {
      students: "2000+",
      duration: "6 months",
      projects: "15+",
      placement: "95%"
    },
    features: [
      "Live Interactive Classes",
      "1:1 Doubt Sessions", 
      "Real Industry Projects",
      "Placement Assistance",
      "Lifetime Access",
      "Certificate of Completion"
    ],
    curriculum: [
      {
        module: "Foundation & Basics",
        duration: "2 weeks",
        topics: [
          "Programming Fundamentals",
          "Time & Space Complexity",
          "Basic Problem Solving",
          "Coding Environment Setup"
        ]
      },
      {
        module: "Arrays & Strings",
        duration: "3 weeks", 
        topics: [
          "Array Manipulation",
          "Two Pointer Technique",
          "Sliding Window",
          "String Algorithms"
        ]
      },
      {
        module: "Linked Lists & Stacks",
        duration: "2 weeks",
        topics: [
          "Singly & Doubly Linked Lists",
          "Stack Implementation",
          "Queue Implementation", 
          "Deque Operations"
        ]
      },
      {
        module: "Trees & Graphs",
        duration: "4 weeks",
        topics: [
          "Binary Trees & BST",
          "Tree Traversals",
          "Graph Representations",
          "BFS & DFS Algorithms"
        ]
      },
      {
        module: "Dynamic Programming",
        duration: "3 weeks",
        topics: [
          "DP Fundamentals",
          "Memoization Techniques",
          "Classic DP Problems",
          "Optimization Strategies"
        ]
      },
      {
        module: "System Design & Projects",
        duration: "4 weeks",
        topics: [
          "System Design Basics",
          "Scalability Concepts",
          "Real-world Projects",
          "Interview Preparation"
        ]
      }
    ],
    faqs: [
      {
        question: "Is this course suitable for beginners?",
        answer: "Yes! This course is designed for complete beginners. We start from the very basics and gradually build up to advanced concepts."
      },
      {
        question: "What programming language will be used?",
        answer: "We primarily use Java and Python for implementations, but concepts are language-agnostic and can be applied to any programming language."
      },
      {
        question: "Do you provide placement assistance?",
        answer: "Yes, we provide comprehensive placement assistance including resume building, mock interviews, and direct referrals to partner companies."
      },
      {
        question: "Can I access the course content after completion?",
        answer: "Absolutely! You get lifetime access to all course materials, including future updates and additional content."
      },
      {
        question: "What if I miss a live class?",
        answer: "All live classes are recorded and available within 24 hours. You can catch up at your own pace."
      }
    ]
  };

  useGSAP(() => {
    // Hero section animations
    gsap.fromTo('.hero-content', 
      { y: 100, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: "power3.out" }
    );

    gsap.fromTo('.hero-image',
      { scale: 0.8, opacity: 0 },
      { scale: 1, opacity: 1, duration: 1.2, ease: "back.out(1.7)", delay: 0.3 }
    );

    // Stats animation
    ScrollTrigger.create({
      trigger: statsRef.current,
      start: "top 80%",
      onEnter: () => {
        gsap.fromTo('.stat-item',
          { y: 50, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.8, stagger: 0.2, ease: "power3.out" }
        );
      }
    });

    // Curriculum animation
    ScrollTrigger.create({
      trigger: curriculumRef.current,
      start: "top 70%",
      onEnter: () => {
        gsap.fromTo('.curriculum-item',
          { x: -100, opacity: 0 },
          { x: 0, opacity: 1, duration: 0.8, stagger: 0.15, ease: "power3.out" }
        );
      }
    });

    // FAQ animation
    ScrollTrigger.create({
      trigger: faqRef.current,
      start: "top 80%",
      onEnter: () => {
        gsap.fromTo('.faq-item',
          { y: 30, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.6, stagger: 0.1, ease: "power2.out" }
        );
      }
    });

  }, []);

  const toggleModule = (index) => {
    setActiveModule(activeModule === index ? null : index);
  };

  const toggleFaq = (index) => {
    setActiveFaq(activeFaq === index ? null : index);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Hero Section */}
      <section ref={heroRef} className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.3) 1px, transparent 1px)`,
            backgroundSize: '50px 50px'
          }}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="hero-content space-y-8">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 bg-blue-600/20 rounded-full border border-blue-500/30">
                  <Zap className="h-4 w-4 text-blue-400 mr-2" />
                  <span className="text-blue-300 text-sm font-medium">Live Cohort Program</span>
                </div>
                
                <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
                  <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                    {courseData.title}
                  </span>
                </h1>
                
                <h2 className="text-2xl lg:text-3xl text-gray-300 font-light">
                  {courseData.subtitle}
                </h2>
                
                <p className="text-lg text-gray-400 leading-relaxed max-w-2xl">
                  {courseData.description}
                </p>
              </div>

              {/* Instructor Info */}
              <div className="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700">
                <img 
                  src={courseData.instructor.image} 
                  alt={courseData.instructor.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div>
                  <h3 className="text-lg font-semibold text-white">{courseData.instructor.name}</h3>
                  <p className="text-gray-400">{courseData.instructor.title}</p>
                  <p className="text-blue-400 text-sm">{courseData.instructor.experience} experience</p>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300 flex items-center justify-center">
                  <Play className="h-5 w-5 mr-2" />
                  Enroll Now - ₹{courseData.pricing.current.toLocaleString()}
                </button>
                <button className="px-8 py-4 border border-gray-600 text-gray-300 font-semibold rounded-lg hover:bg-gray-800 transition-all duration-300 flex items-center justify-center">
                  <BookOpen className="h-5 w-5 mr-2" />
                  View Curriculum
                </button>
              </div>

              {/* Pricing Info */}
              <div className="flex items-center space-x-4 text-sm">
                <span className="text-gray-500 line-through">₹{courseData.pricing.original.toLocaleString()}</span>
                <span className="text-green-400 font-semibold">{courseData.pricing.discount}% OFF</span>
                <span className="text-gray-400">or ₹{courseData.pricing.emi}/month</span>
              </div>
            </div>

            {/* Right Content - Course Preview */}
            <div className="hero-image">
              <div className="relative">
                <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 border border-gray-700 shadow-2xl">
                  <div className="aspect-video bg-gray-700 rounded-lg mb-6 flex items-center justify-center relative overflow-hidden">
                    <img 
                      src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop"
                      alt="Course Preview"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                      <button className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors">
                        <Play className="h-6 w-6 text-white ml-1" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold">Course Highlights</h3>
                    <div className="grid grid-cols-2 gap-4">
                      {courseData.features.slice(0, 4).map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-400" />
                          <span className="text-sm text-gray-300">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section ref={statsRef} className="py-20 bg-gray-800/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="stat-item text-center">
              <div className="flex items-center justify-center mb-4">
                <Users className="h-8 w-8 text-blue-400" />
              </div>
              <div className="text-3xl font-bold text-white mb-2">{courseData.stats.students}</div>
              <div className="text-gray-400">Students Enrolled</div>
            </div>
            <div className="stat-item text-center">
              <div className="flex items-center justify-center mb-4">
                <Clock className="h-8 w-8 text-green-400" />
              </div>
              <div className="text-3xl font-bold text-white mb-2">{courseData.stats.duration}</div>
              <div className="text-gray-400">Course Duration</div>
            </div>
            <div className="stat-item text-center">
              <div className="flex items-center justify-center mb-4">
                <Target className="h-8 w-8 text-purple-400" />
              </div>
              <div className="text-3xl font-bold text-white mb-2">{courseData.stats.projects}</div>
              <div className="text-gray-400">Real Projects</div>
            </div>
            <div className="stat-item text-center">
              <div className="flex items-center justify-center mb-4">
                <Award className="h-8 w-8 text-yellow-400" />
              </div>
              <div className="text-3xl font-bold text-white mb-2">{courseData.stats.placement}</div>
              <div className="text-gray-400">Placement Rate</div>
            </div>
          </div>
        </div>
      </section>

      {/* Curriculum Section */}
      <section ref={curriculumRef} className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Course Curriculum</h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Comprehensive curriculum designed by industry experts to take you from zero to job-ready
            </p>
          </div>

          <div className="space-y-4">
            {courseData.curriculum.map((module, index) => (
              <div key={index} className="curriculum-item">
                <div
                  className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden cursor-pointer hover:border-blue-500 transition-colors"
                  onClick={() => toggleModule(index)}
                >
                  <div className="p-6 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span className="text-white font-bold">{index + 1}</span>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-white">{module.module}</h3>
                        <p className="text-gray-400">{module.duration}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-400">{module.topics.length} topics</span>
                      {activeModule === index ?
                        <ChevronUp className="h-5 w-5 text-gray-400" /> :
                        <ChevronDown className="h-5 w-5 text-gray-400" />
                      }
                    </div>
                  </div>

                  {activeModule === index && (
                    <div className="px-6 pb-6 border-t border-gray-700">
                      <div className="pt-4 grid grid-cols-1 md:grid-cols-2 gap-3">
                        {module.topics.map((topic, topicIndex) => (
                          <div key={topicIndex} className="flex items-center space-x-3 p-3 bg-gray-700/50 rounded-lg">
                            <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0" />
                            <span className="text-gray-300">{topic}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Why Choose This Course?</h2>
            <p className="text-xl text-gray-400">Everything you need to succeed in your coding journey</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {courseData.features.map((feature, index) => (
              <div key={index} className="bg-gray-800 p-6 rounded-lg border border-gray-700 hover:border-blue-500 transition-colors">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                    <CheckCircle className="h-5 w-5 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-white">{feature}</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  {feature === "Live Interactive Classes" && "Join live sessions with real-time doubt solving and peer interaction."}
                  {feature === "1:1 Doubt Sessions" && "Get personalized help from mentors whenever you're stuck."}
                  {feature === "Real Industry Projects" && "Build portfolio-worthy projects used by real companies."}
                  {feature === "Placement Assistance" && "Complete job preparation with mock interviews and referrals."}
                  {feature === "Lifetime Access" && "Access all course materials forever, including future updates."}
                  {feature === "Certificate of Completion" && "Industry-recognized certificate to boost your resume."}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section ref={faqRef} className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-400">Got questions? We've got answers!</p>
          </div>

          <div className="space-y-4">
            {courseData.faqs.map((faq, index) => (
              <div key={index} className="faq-item">
                <div
                  className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden cursor-pointer hover:border-blue-500 transition-colors"
                  onClick={() => toggleFaq(index)}
                >
                  <div className="p-6 flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-white pr-4">{faq.question}</h3>
                    {activeFaq === index ?
                      <ChevronUp className="h-5 w-5 text-gray-400 flex-shrink-0" /> :
                      <ChevronDown className="h-5 w-5 text-gray-400 flex-shrink-0" />
                    }
                  </div>

                  {activeFaq === index && (
                    <div className="px-6 pb-6 border-t border-gray-700">
                      <p className="pt-4 text-gray-300 leading-relaxed">{faq.answer}</p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-4">Ready to Start Your Journey?</h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of students who have transformed their careers with our courses
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button className="px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transform hover:scale-105 transition-all duration-300 flex items-center">
              <Play className="h-5 w-5 mr-2" />
              Enroll Now - ₹{courseData.pricing.current.toLocaleString()}
            </button>
            <button className="px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-all duration-300 flex items-center">
              <Download className="h-5 w-5 mr-2" />
              Download Brochure
            </button>
          </div>

          <div className="mt-8 flex items-center justify-center space-x-6 text-blue-100">
            <div className="flex items-center space-x-2">
              <Star className="h-5 w-5 text-yellow-400" />
              <span>4.9/5 Rating</span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>2000+ Students</span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="h-5 w-5" />
              <span>95% Placement</span>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CourseDetails;
