import { Link } from "react-router-dom";
import {
  BookOpen,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
} from "lucide-react";

const Footer = () => {
  const size = 100;
  const color = "black";
  const className = "h-8 w-8";
  const style = {
    transform: "translate3d(0, 0, 0) scale3d(1, 1, 1)",
    transformStyle: "preserve-3d",
  };

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <Link to="/" className="flex items-center space-x-2">
              <BookOpen className="h-8 w-8 text-primary-400" />
              <span className="text-xl font-bold">LearnHub</span>
            </Link>
            <p className="text-gray-300 text-sm">
              Empowering learners worldwide with high-quality online courses
              from industry experts.
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="text-gray-400 hover:text-primary-400 transition-colors"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-primary-400 transition-colors"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-primary-400 transition-colors"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-primary-400 transition-colors"
              >
                <Linkedin className="h-5 w-5" />
              </a>
            </div>
          </div>
          <div className="flex justify-center items-center gap-2 bg-red-800">
            <div className="flex items-center space-x-3">
              <svg
                width={size}
                height={size}
                viewBox="0 0 200 200"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M135.07 52.725C115.33 34.425 84.3897 34.425 64.6497 52.725C54.1997 62.415 48.2897 75.585 48.0097 89.805C47.7397 104.025 53.1297 117.405 63.1897 127.455L95.2497 159.525C96.5097 160.785 98.1797 161.475 99.9497 161.475H99.9597C101.74 161.475 103.41 160.775 104.67 159.515L136.51 127.465C146.56 117.405 151.95 104.035 151.68 89.815C151.41 75.595 145.5 62.435 135.05 52.745L135.07 52.725ZM99.8597 70.025C109.13 70.025 116.66 77.565 116.66 86.825C116.66 96.085 109.12 103.625 99.8597 103.625C90.5997 103.625 83.0597 96.085 83.0597 86.825C83.0597 77.565 90.5997 70.025 99.8597 70.025Z"
                  fill={color}
                />
              </svg>
            </div>
            <div className="x">
              <svg
                width={size}
                height={size}
                viewBox="0 0 200 200"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className=""
                style={{
                  transform: "translate3d(0, 0, 0) scale3d(1, 1, 1)",
                  transformStyle: "preserve-3d",
                }}
              >
                <path
                  d="M158.92 129.346L134.83 115.586C132.06 114.006 128.57 114.496 126.35 116.786L114.77 128.736C113.97 129.556 112.78 129.876 111.69 129.586C106.79 126.946 102.06 123.876 97.64 120.466C90.87 115.246 84.76 109.196 79.47 102.476C76.21 98.3358 73.25 93.9058 70.67 89.3358C70.07 88.1058 70.33 86.6458 71.32 85.6958L83.35 74.1958C85.66 71.9858 86.17 68.5058 84.61 65.7258L71.02 41.5558C69.66 39.1358 67 37.7558 64.24 38.0358L59.78 38.4758C54.72 38.9758 50.07 41.2558 46.69 44.8958L46.45 45.1558C40.35 52.0158 37 60.8358 37 70.0158C37 71.6158 37.1 73.2358 37.31 74.8258C37.52 76.5358 37.94 78.2258 38.35 79.8558L38.58 80.8058C43.29 100.056 53.2 117.696 67.23 131.806C81.29 145.946 98.87 155.916 118.07 160.626C121.84 161.546 127.51 162.586 130.11 162.636C130.35 162.636 130.6 162.636 130.84 162.636C138.93 162.636 147 159.716 153.65 154.356C154.46 153.706 155.4 152.806 156.54 151.596C159.61 148.346 161.57 144.066 162.05 139.556L162.41 136.136C162.7 133.376 161.33 130.706 158.93 129.336L158.92 129.346Z"
                  fill={color}
                />
              </svg>
            </div>
            <div className="mail">
              <svg
                width={size}
                height={size}
                viewBox="0 0 200 200"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className={className}
              >
                <path
                  d="M101.57 102.57C101.08 102.89 100.52 103.04 99.95 103.04C99.38 103.04 98.83 102.88 98.33 102.57L20.96 53.0901C20.35 54.4201 20 55.8901 20 57.4401V142.55C20 148.31 24.69 152.99 30.44 152.99H169.47C175.23 152.99 179.91 148.3 179.91 142.55V57.4401C179.91 55.8901 179.56 54.4101 178.95 53.0901L101.57 102.57Z"
                  fill={color}
                />
              </svg>
            </div>
          </div>
          {/* Contact Info */}
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 LearnHub. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a
                href="#"
                className="text-gray-400 hover:text-primary-400 transition-colors text-sm"
              >
                Privacy Policy
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-primary-400 transition-colors text-sm"
              >
                Terms of Service
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-primary-400 transition-colors text-sm"
              >
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
