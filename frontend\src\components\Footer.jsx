import { useRef } from "react";
import { <PERSON> } from "react-router-dom";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { ScrollToPlugin } from "gsap/ScrollToPlugin";
import { useGSAP } from "@gsap/react";
import {
  BookOpen,
  Mail,
  Phone,
  MapPin,
  ArrowUp,
  Heart,
  Code,
  Users,
  Award,
  Globe
} from "lucide-react";

gsap.registerPlugin(ScrollTrigger, ScrollToPlugin, useGSAP);

const Footer = () => {
  const footerRef = useRef(null);
  const logoRef = useRef(null);
  const socialRef = useRef(null);
  const contactRef = useRef(null);
  const backToTopRef = useRef(null);



  useGSAP(() => {
    const footer = footerRef.current;
    const logo = logoRef.current;
    const social = socialRef.current;
    const contact = contactRef.current;
    const backToTop = backToTopRef.current;

    if (!footer) return;

    // Set initial states
    gsap.set([logo, social, contact], {
      y: 50,
      opacity: 0
    });

    gsap.set(".footer-link", {
      y: 20,
      opacity: 0
    });

    gsap.set(".stat-item", {
      scale: 0,
      opacity: 0
    });

    // Animate footer elements on scroll
    ScrollTrigger.create({
      trigger: footer,
      start: "top 80%",
      end: "bottom bottom",
      onEnter: () => {
        // Animate main sections
        gsap.to([logo, social, contact], {
          y: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: "power3.out"
        });

        // Animate links
        gsap.to(".footer-link", {
          y: 0,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: "power2.out",
          delay: 0.3
        });

        // Animate stats
        gsap.to(".stat-item", {
          scale: 1,
          opacity: 1,
          duration: 0.6,
          stagger: 0.15,
          ease: "back.out(1.7)",
          delay: 0.5
        });
      }
    });

    // Back to top button animation
    if (backToTop) {
      gsap.set(backToTop, { scale: 0, rotation: -180 });

      ScrollTrigger.create({
        start: "top -100",
        end: "max",
        onUpdate: (self) => {
          if (self.direction === 1 && self.progress > 0.1) {
            gsap.to(backToTop, {
              scale: 1,
              rotation: 0,
              duration: 0.3,
              ease: "back.out(1.7)"
            });
          } else if (self.direction === -1 && self.progress < 0.1) {
            gsap.to(backToTop, {
              scale: 0,
              rotation: -180,
              duration: 0.3,
              ease: "power2.in"
            });
          }
        }
      });

      // Click handler for back to top
      backToTop.addEventListener('click', () => {
        gsap.to(window, {
          scrollTo: { y: 0 },
          duration: 1.5,
          ease: "power3.inOut"
        });
      });
    }

    // Hover animations for social icons
    const socialIcons = document.querySelectorAll('.social-icon');
    socialIcons.forEach(icon => {
      icon.addEventListener('mouseenter', () => {
        gsap.to(icon, {
          scale: 1.2,
          rotation: 10,
          duration: 0.3,
          ease: "power2.out"
        });
      });

      icon.addEventListener('mouseleave', () => {
        gsap.to(icon, {
          scale: 1,
          rotation: 0,
          duration: 0.3,
          ease: "power2.out"
        });
      });
    });

  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <footer ref={footerRef} className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 1px, transparent 1px)`,
          backgroundSize: '30px 30px'
        }}></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-10 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 right-20 w-24 h-24 bg-purple-500/10 rounded-full blur-lg animate-bounce"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          <div className="stat-item text-center">
            <div className="flex items-center justify-center mb-2">
              <Users className="h-8 w-8 text-blue-400" />
            </div>
            <div className="text-3xl font-bold text-white">10K+</div>
            <div className="text-gray-400 text-sm">Students</div>
          </div>
          <div className="stat-item text-center">
            <div className="flex items-center justify-center mb-2">
              <BookOpen className="h-8 w-8 text-green-400" />
            </div>
            <div className="text-3xl font-bold text-white">100+</div>
            <div className="text-gray-400 text-sm">Courses</div>
          </div>
          <div className="stat-item text-center">
            <div className="flex items-center justify-center mb-2">
              <Award className="h-8 w-8 text-yellow-400" />
            </div>
            <div className="text-3xl font-bold text-white">95%</div>
            <div className="text-gray-400 text-sm">Success Rate</div>
          </div>
          <div className="stat-item text-center">
            <div className="flex items-center justify-center mb-2">
              <Globe className="h-8 w-8 text-purple-400" />
            </div>
            <div className="text-3xl font-bold text-white">50+</div>
            <div className="text-gray-400 text-sm">Countries</div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div ref={logoRef} className="space-y-6">
            <Link to="/" className="flex items-center space-x-3 group">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg group-hover:scale-110 transition-transform duration-300">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                LearnHub
              </span>
            </Link>
            <p className="text-gray-300 text-sm leading-relaxed">
              Empowering learners worldwide with high-quality online courses from industry experts.
              Join our community and transform your career today.
            </p>
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <Heart className="h-4 w-4 text-red-400" />
              <span>Made with love for learners</span>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Quick Links</h3>
            <ul className="space-y-3">
              {['About Us', 'Courses', 'Instructors', 'Blog', 'Contact'].map((link, index) => (
                <li key={index}>
                  <Link
                    to="#"
                    className="footer-link text-gray-300 hover:text-blue-400 transition-colors duration-300 flex items-center group"
                  >
                    <Code className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    {link}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div ref={contactRef} className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Contact Info</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 group">
                <div className="p-2 bg-gray-800 rounded-lg group-hover:bg-blue-600 transition-colors duration-300">
                  <MapPin className="h-4 w-4 text-blue-400 group-hover:text-white" />
                </div>
                <span className="text-gray-300 text-sm">123 Learning St, Education City</span>
              </div>
              <div className="flex items-center space-x-3 group">
                <div className="p-2 bg-gray-800 rounded-lg group-hover:bg-green-600 transition-colors duration-300">
                  <Phone className="h-4 w-4 text-green-400 group-hover:text-white" />
                </div>
                <span className="text-gray-300 text-sm">+****************</span>
              </div>
              <div className="flex items-center space-x-3 group">
                <div className="p-2 bg-gray-800 rounded-lg group-hover:bg-purple-600 transition-colors duration-300">
                  <Mail className="h-4 w-4 text-purple-400 group-hover:text-white" />
                </div>
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Social Media */}
          <div ref={socialRef} className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Follow Us</h3>
            <div className="flex space-x-4">
              <a href="#" className="social-icon p-3 bg-gray-800 rounded-lg hover:bg-blue-600 transition-colors duration-300 group">
                <svg className="h-5 w-5 text-blue-400 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="#" className="social-icon p-3 bg-gray-800 rounded-lg hover:bg-sky-500 transition-colors duration-300 group">
                <svg className="h-5 w-5 text-sky-400 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
              </a>
              <a href="#" className="social-icon p-3 bg-gray-800 rounded-lg hover:bg-pink-600 transition-colors duration-300 group">
                <svg className="h-5 w-5 text-pink-400 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001 12.017.001z"/>
                </svg>
              </a>
              <a href="#" className="social-icon p-3 bg-gray-800 rounded-lg hover:bg-blue-700 transition-colors duration-300 group">
                <svg className="h-5 w-5 text-blue-400 group-hover:text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
            </div>
            <p className="text-gray-400 text-sm">
              Join our community of learners and stay updated with the latest courses and tips.
            </p>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 LearnHub. All rights reserved. Built with ❤️ for education.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              {['Privacy Policy', 'Terms of Service', 'Cookie Policy'].map((link, index) => (
                <a
                  key={index}
                  href="#"
                  className="footer-link text-gray-400 hover:text-blue-400 transition-colors duration-300 text-sm"
                >
                  {link}
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Back to Top Button */}
      <button
        ref={backToTopRef}
        onClick={scrollToTop}
        className="fixed bottom-8 right-8 p-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300 z-50"
        aria-label="Back to top"
      >
        <ArrowUp className="h-5 w-5" />
      </button>
    </footer>
  );
};

export default Footer;
