import { useRef } from "react";
import { <PERSON> } from "react-router-dom";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";
import {
  BookOpen,
  Mail,
  Phone,
  MapPin,
  ArrowUp,
  Heart,
  Code,
  Users,
  Award,
  Globe
} from "lucide-react";

gsap.registerPlugin(ScrollTrigger, useGSAP);

const Footer = () => {
  const footerRef = useRef(null);
  const logoRef = useRef(null);
  const socialRef = useRef(null);
  const contactRef = useRef(null);
  const backToTopRef = useRef(null);

  const size = 24;
  const iconColor = "#ffffff";

  useGSAP(() => {
    const footer = footerRef.current;
    const logo = logoRef.current;
    const social = socialRef.current;
    const contact = contactRef.current;
    const backToTop = backToTopRef.current;

    if (!footer) return;

    // Set initial states
    gsap.set([logo, social, contact], {
      y: 50,
      opacity: 0
    });

    gsap.set(".footer-link", {
      y: 20,
      opacity: 0
    });

    gsap.set(".stat-item", {
      scale: 0,
      opacity: 0
    });

    // Animate footer elements on scroll
    ScrollTrigger.create({
      trigger: footer,
      start: "top 80%",
      end: "bottom bottom",
      onEnter: () => {
        // Animate main sections
        gsap.to([logo, social, contact], {
          y: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: "power3.out"
        });

        // Animate links
        gsap.to(".footer-link", {
          y: 0,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: "power2.out",
          delay: 0.3
        });

        // Animate stats
        gsap.to(".stat-item", {
          scale: 1,
          opacity: 1,
          duration: 0.6,
          stagger: 0.15,
          ease: "back.out(1.7)",
          delay: 0.5
        });
      }
    });

    // Back to top button animation
    if (backToTop) {
      gsap.set(backToTop, { scale: 0, rotation: -180 });

      ScrollTrigger.create({
        start: "top -100",
        end: "max",
        onUpdate: (self) => {
          if (self.direction === 1 && self.progress > 0.1) {
            gsap.to(backToTop, {
              scale: 1,
              rotation: 0,
              duration: 0.3,
              ease: "back.out(1.7)"
            });
          } else if (self.direction === -1 && self.progress < 0.1) {
            gsap.to(backToTop, {
              scale: 0,
              rotation: -180,
              duration: 0.3,
              ease: "power2.in"
            });
          }
        }
      });

      // Click handler for back to top
      backToTop.addEventListener('click', () => {
        gsap.to(window, {
          scrollTo: { y: 0 },
          duration: 1.5,
          ease: "power3.inOut"
        });
      });
    }

    // Hover animations for social icons
    const socialIcons = document.querySelectorAll('.social-icon');
    socialIcons.forEach(icon => {
      icon.addEventListener('mouseenter', () => {
        gsap.to(icon, {
          scale: 1.2,
          rotation: 10,
          duration: 0.3,
          ease: "power2.out"
        });
      });

      icon.addEventListener('mouseleave', () => {
        gsap.to(icon, {
          scale: 1,
          rotation: 0,
          duration: 0.3,
          ease: "power2.out"
        });
      });
    });

  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <footer ref={footerRef} className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 1px, transparent 1px)`,
          backgroundSize: '30px 30px'
        }}></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-10 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 right-20 w-24 h-24 bg-purple-500/10 rounded-full blur-lg animate-bounce"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          <div className="stat-item text-center">
            <div className="flex items-center justify-center mb-2">
              <Users className="h-8 w-8 text-blue-400" />
            </div>
            <div className="text-3xl font-bold text-white">10K+</div>
            <div className="text-gray-400 text-sm">Students</div>
          </div>
          <div className="stat-item text-center">
            <div className="flex items-center justify-center mb-2">
              <BookOpen className="h-8 w-8 text-green-400" />
            </div>
            <div className="text-3xl font-bold text-white">100+</div>
            <div className="text-gray-400 text-sm">Courses</div>
          </div>
          <div className="stat-item text-center">
            <div className="flex items-center justify-center mb-2">
              <Award className="h-8 w-8 text-yellow-400" />
            </div>
            <div className="text-3xl font-bold text-white">95%</div>
            <div className="text-gray-400 text-sm">Success Rate</div>
          </div>
          <div className="stat-item text-center">
            <div className="flex items-center justify-center mb-2">
              <Globe className="h-8 w-8 text-purple-400" />
            </div>
            <div className="text-3xl font-bold text-white">50+</div>
            <div className="text-gray-400 text-sm">Countries</div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div ref={logoRef} className="space-y-6">
            <Link to="/" className="flex items-center space-x-3 group">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg group-hover:scale-110 transition-transform duration-300">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                LearnHub
              </span>
            </Link>
            <p className="text-gray-300 text-sm leading-relaxed">
              Empowering learners worldwide with high-quality online courses from industry experts.
              Join our community and transform your career today.
            </p>
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <Heart className="h-4 w-4 text-red-400" />
              <span>Made with love for learners</span>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Quick Links</h3>
            <ul className="space-y-3">
              {['About Us', 'Courses', 'Instructors', 'Blog', 'Contact'].map((link, index) => (
                <li key={index}>
                  <Link
                    to="#"
                    className="footer-link text-gray-300 hover:text-blue-400 transition-colors duration-300 flex items-center group"
                  >
                    <Code className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    {link}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div ref={contactRef} className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Contact Info</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 group">
                <div className="p-2 bg-gray-800 rounded-lg group-hover:bg-blue-600 transition-colors duration-300">
                  <MapPin className="h-4 w-4 text-blue-400 group-hover:text-white" />
                </div>
                <span className="text-gray-300 text-sm">123 Learning St, Education City</span>
              </div>
              <div className="flex items-center space-x-3 group">
                <div className="p-2 bg-gray-800 rounded-lg group-hover:bg-green-600 transition-colors duration-300">
                  <Phone className="h-4 w-4 text-green-400 group-hover:text-white" />
                </div>
                <span className="text-gray-300 text-sm">+****************</span>
              </div>
              <div className="flex items-center space-x-3 group">
                <div className="p-2 bg-gray-800 rounded-lg group-hover:bg-purple-600 transition-colors duration-300">
                  <Mail className="h-4 w-4 text-purple-400 group-hover:text-white" />
                </div>
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Social Media */}
          <div ref={socialRef} className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Follow Us</h3>
            <div className="flex space-x-4">
              <a href="#" className="social-icon p-3 bg-gray-800 rounded-lg hover:bg-blue-600 transition-colors duration-300 group">
                <Facebook className="h-5 w-5 text-blue-400 group-hover:text-white" />
              </a>
              <a href="#" className="social-icon p-3 bg-gray-800 rounded-lg hover:bg-sky-500 transition-colors duration-300 group">
                <Twitter className="h-5 w-5 text-sky-400 group-hover:text-white" />
              </a>
              <a href="#" className="social-icon p-3 bg-gray-800 rounded-lg hover:bg-pink-600 transition-colors duration-300 group">
                <Instagram className="h-5 w-5 text-pink-400 group-hover:text-white" />
              </a>
              <a href="#" className="social-icon p-3 bg-gray-800 rounded-lg hover:bg-blue-700 transition-colors duration-300 group">
                <Linkedin className="h-5 w-5 text-blue-400 group-hover:text-white" />
              </a>
            </div>
            <p className="text-gray-400 text-sm">
              Join our community of learners and stay updated with the latest courses and tips.
            </p>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 LearnHub. All rights reserved. Built with ❤️ for education.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              {['Privacy Policy', 'Terms of Service', 'Cookie Policy'].map((link, index) => (
                <a
                  key={index}
                  href="#"
                  className="footer-link text-gray-400 hover:text-blue-400 transition-colors duration-300 text-sm"
                >
                  {link}
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Back to Top Button */}
      <button
        ref={backToTopRef}
        onClick={scrollToTop}
        className="fixed bottom-8 right-8 p-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300 z-50"
        aria-label="Back to top"
      >
        <ArrowUp className="h-5 w-5" />
      </button>
    </footer>
  );
};

export default Footer;
